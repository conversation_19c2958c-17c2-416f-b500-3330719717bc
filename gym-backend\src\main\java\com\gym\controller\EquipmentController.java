package com.gym.controller;

import com.gym.common.result.Result;
import com.gym.entity.EquipmentInfo;
import com.gym.entity.EquipmentUsageLog;
import com.gym.service.EquipmentService;
import com.gym.util.JwtUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/equipment")
public class EquipmentController {

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取设备列表
     */
    @GetMapping("/list")
    public Result<List<EquipmentInfo>> getEquipmentList(@RequestParam(required = false) Integer equipmentType,
                                                       @RequestParam(required = false) Long roomId) {
        try {
            List<EquipmentInfo> equipments;
            if (equipmentType != null) {
                equipments = equipmentService.getEquipmentsByType(equipmentType);
            } else if (roomId != null) {
                equipments = equipmentService.getEquipmentsByRoom(roomId);
            } else {
                equipments = equipmentService.getAvailableEquipments();
            }
            return Result.success("查询成功", equipments);
        } catch (Exception e) {
            log.error("查询设备列表失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取设备详情
     */
    @GetMapping("/{equipmentId}")
    public Result<EquipmentInfo> getEquipmentDetail(@PathVariable Long equipmentId) {
        try {
            EquipmentInfo equipment = equipmentService.getById(equipmentId);
            if (equipment == null) {
                return Result.error("设备不存在");
            }
            return Result.success("查询成功", equipment);
        } catch (Exception e) {
            log.error("查询设备详情失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 根据设备编号获取设备信息
     */
    @GetMapping("/code/{equipmentCode}")
    public Result<EquipmentInfo> getEquipmentByCode(@PathVariable String equipmentCode) {
        try {
            EquipmentInfo equipment = equipmentService.getEquipmentByCode(equipmentCode);
            if (equipment == null) {
                return Result.error("设备不存在");
            }
            return Result.success("查询成功", equipment);
        } catch (Exception e) {
            log.error("根据编号查询设备失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 扫码启动设备
     */
    @PostMapping("/start")
    public Result<Map<String, Object>> startEquipment(@RequestHeader("Authorization") String authHeader,
                                                     @Valid @RequestBody StartEquipmentRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = equipmentService.startEquipment(userId, request.getQrCode());
            
            if (success) {
                // 获取用户当前使用的设备信息
                EquipmentUsageLog inProgress = equipmentService.getUserInProgressEquipment(userId);
                Map<String, Object> data = new HashMap<>();
                data.put("usageLogId", inProgress.getId());
                data.put("startTime", inProgress.getStartTime());
                
                return Result.success("设备启动成功", data);
            } else {
                return Result.error("设备启动失败");
            }
        } catch (Exception e) {
            log.error("启动设备失败: {}", e.getMessage(), e);
            return Result.error("启动失败: " + e.getMessage());
        }
    }

    /**
     * 结束使用设备
     */
    @PostMapping("/end")
    public Result<Map<String, Object>> endEquipment(@RequestHeader("Authorization") String authHeader,
                                                   @Valid @RequestBody EndEquipmentRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = equipmentService.endEquipment(userId, request.getUsageLogId());
            
            if (success) {
                // 获取使用记录详情
                EquipmentUsageLog usageLog = equipmentService.getById(request.getUsageLogId());
                Map<String, Object> data = new HashMap<>();
                data.put("duration", usageLog.getDuration());
                data.put("caloriesBurned", usageLog.getCaloriesBurned());
                data.put("endTime", usageLog.getEndTime());
                
                return Result.success("设备使用结束", data);
            } else {
                return Result.error("结束使用失败");
            }
        } catch (Exception e) {
            log.error("结束设备使用失败: {}", e.getMessage(), e);
            return Result.error("结束失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户当前使用的设备
     */
    @GetMapping("/current-usage")
    public Result<Map<String, Object>> getCurrentUsage(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            EquipmentUsageLog inProgress = equipmentService.getUserInProgressEquipment(userId);
            
            if (inProgress == null) {
                return Result.success("当前无设备使用", null);
            }
            
            // 获取设备信息
            EquipmentInfo equipment = equipmentService.getById(inProgress.getEquipmentId());
            
            Map<String, Object> data = new HashMap<>();
            data.put("usageLog", inProgress);
            data.put("equipment", equipment);
            data.put("currentDuration", LocalDateTime.now().compareTo(inProgress.getStartTime()));
            
            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("查询当前使用设备失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户使用历史
     */
    @GetMapping("/usage-history")
    public Result<List<EquipmentUsageLog>> getUsageHistory(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            List<EquipmentUsageLog> history = equipmentService.getUserUsageHistory(userId);
            return Result.success("查询成功", history);
        } catch (Exception e) {
            log.error("查询使用历史失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取设备使用统计
     */
    @GetMapping("/usage-stats")
    public Result<Map<String, Object>> getUsageStats(@RequestHeader("Authorization") String authHeader,
                                                     @RequestParam(required = false) Integer days) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 默认查询最近7天
            if (days == null) days = 7;
            
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            Integer totalDuration = equipmentService.getUserUsageDuration(userId, startTime, endTime);
            List<EquipmentUsageLog> recentLogs = equipmentService.getUserUsageHistory(userId);
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalDuration", totalDuration);
            stats.put("totalSessions", recentLogs.size());
            stats.put("averageDuration", recentLogs.size() > 0 ? totalDuration / recentLogs.size() : 0);
            stats.put("period", days + "天");
            
            return Result.success("查询成功", stats);
        } catch (Exception e) {
            log.error("查询使用统计失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取需要维护的设备（管理员接口）
     */
    @GetMapping("/maintenance-needed")
    public Result<List<EquipmentInfo>> getNeedMaintenanceEquipments() {
        try {
            List<EquipmentInfo> equipments = equipmentService.getNeedMaintenanceEquipments();
            return Result.success("查询成功", equipments);
        } catch (Exception e) {
            log.error("查询需维护设备失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 更新设备状态（管理员接口）
     */
    @PutMapping("/{equipmentId}/status")
    public Result<Void> updateEquipmentStatus(@PathVariable Long equipmentId,
                                             @Valid @RequestBody UpdateStatusRequest request) {
        try {
            boolean success = equipmentService.updateEquipmentStatus(equipmentId, request.getStatus());
            if (success) {
                return Result.success("状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新设备状态失败: {}", e.getMessage(), e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 从令牌中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        String token = jwtUtil.extractTokenFromHeader(authHeader);
        if (token == null || !jwtUtil.isTokenValid(token)) {
            throw new RuntimeException("令牌无效");
        }
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 启动设备请求对象
     */
    @Data
    public static class StartEquipmentRequest {
        @NotBlank(message = "二维码不能为空")
        private String qrCode;
    }

    /**
     * 结束使用设备请求对象
     */
    @Data
    public static class EndEquipmentRequest {
        @NotNull(message = "使用记录ID不能为空")
        private Long usageLogId;
    }

    /**
     * 更新设备状态请求对象
     */
    @Data
    public static class UpdateStatusRequest {
        @NotNull(message = "设备状态不能为空")
        private Integer status;
    }
} 