const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 时间筛选
    timeTabs: [
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本年', value: 'year' }
    ],
    timeFilter: 'month',
    
    // 概览统计
    overviewStats: {
      totalWorkouts: 42,
      workoutChange: 15,
      totalHours: '32.5小时',
      timeChange: 12,
      totalCalories: 8450,
      calorieChange: 18,
      consistency: '85%',
      consistencyChange: 5
    },
    
    // 体征数据类型
    bodyDataTypes: [
      { label: '体重', value: 'weight', unit: 'kg' },
      { label: '体脂率', value: 'bodyFat', unit: '%' },
      { label: '肌肉量', value: 'muscle', unit: 'kg' },
      { label: '内脏脂肪', value: 'visceralFat', unit: '级' }
    ],
    selectedBodyDataIndex: 0,
    
    // 当前体征数据
    currentBodyData: {
      current: 68.5,
      change: -1.2,
      target: 65.0,
      unit: 'kg'
    },
    
    // 训练类型分布
    workoutDistribution: [
      { name: '力量训练', type: 'strength', percentage: 35, color: '#667eea' },
      { name: '有氧运动', type: 'cardio', percentage: 28, color: '#10b981' },
      { name: 'HIIT', type: 'hiit', percentage: 20, color: '#f59e0b' },
      { name: '瑜伽', type: 'yoga', percentage: 12, color: '#ec4899' },
      { name: '其他', type: 'other', percentage: 5, color: '#8b5cf6' }
    ],
    
    // 成就数据
    recentAchievements: [
      {
        id: 1,
        name: '坚持不懈',
        description: '连续训练30天',
        icon: '🔥',
        unlocked: true,
        unlockDate: '12月15日'
      },
      {
        id: 2,
        name: '力量之王',
        description: '深蹲重量达到体重2倍',
        icon: '👑',
        unlocked: false,
        progress: 75,
        current: 120,
        target: 140
      },
      {
        id: 3,
        name: '卡路里粉碎机',
        description: '单次训练消耗500卡路里',
        icon: '💥',
        unlocked: true,
        unlockDate: '12月10日'
      },
      {
        id: 4,
        name: '马拉松挑战者',
        description: '累计跑步100公里',
        icon: '🏃‍♂️',
        unlocked: false,
        progress: 60,
        current: 60,
        target: 100
      }
    ],
    
    // 个人记录
    personalRecords: [
      {
        type: 'benchPress',
        name: '卧推',
        value: 80,
        unit: 'kg',
        date: '12月18日',
        icon: '💪',
        isImproved: true
      },
      {
        type: 'squat',
        name: '深蹲',
        value: 120,
        unit: 'kg',
        date: '12月15日',
        icon: '🏋️‍♂️',
        isImproved: true
      },
      {
        type: 'deadlift',
        name: '硬拉',
        value: 140,
        unit: 'kg',
        date: '12月12日',
        icon: '⚡',
        isImproved: false
      },
      {
        type: 'run',
        name: '5公里跑',
        value: 22,
        unit: '分钟',
        date: '12月20日',
        icon: '🏃‍♂️',
        isImproved: true
      }
    ],
    
    // 图表相关
    showTooltip: false,
    tooltipX: 0,
    tooltipY: 0,
    tooltipData: {
      date: '',
      value: 0
    },
    
    // 图表数据
    chartData: {
      workout: {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [2, 1, 3, 2, 2, 1, 0]
      },
      bodyData: {
        weight: [
          { date: '12/01', value: 70.2 },
          { date: '12/05', value: 69.8 },
          { date: '12/10', value: 69.5 },
          { date: '12/15', value: 69.1 },
          { date: '12/20', value: 68.5 }
        ]
      }
    }
  },

  /**
   * 图表上下文
   */
  workoutChartCtx: null,
  bodyDataChartCtx: null,
  distributionChartCtx: null,

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Analytics page loaded');
    
    // 检查URL参数
    if (options.tab) {
      // 根据tab参数调整显示内容
      this.handleTabParam(options.tab);
    }
    
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新数据
    this.loadAnalyticsData();
    
    // 延迟绘制图表，确保DOM已渲染
    setTimeout(() => {
      this.initCharts();
    }, 500);
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 更新体征数据显示
    this.updateCurrentBodyData();
    
    // 从缓存加载数据
    this.loadAnalyticsData();
  },

  /**
   * 处理tab参数
   */
  handleTabParam(tab) {
    if (tab === 'workout') {
      // 滚动到训练相关区域
      this.scrollToSection('chart-section');
    } else if (tab === 'body') {
      // 滚动到体征数据区域
      this.scrollToSection('body-data-section');
    }
  },

  scrollToSection(sectionClass) {
    // 简单的滚动实现
    setTimeout(() => {
      wx.pageScrollTo({
        selector: `.${sectionClass}`,
        duration: 300
      });
    }, 100);
  },

  /**
   * 时间筛选切换
   */
  onTimeFilterChange(e) {
    const { filter } = e.currentTarget.dataset;
    this.setData({ timeFilter: filter });
    
    // 更新数据和图表
    this.updateDataByTimeFilter(filter);
    this.refreshCharts();
  },

  updateDataByTimeFilter(filter) {
    // 根据时间筛选更新数据
    let newOverviewStats = { ...this.data.overviewStats };
    
    switch (filter) {
      case 'week':
        newOverviewStats = {
          totalWorkouts: 5,
          workoutChange: 25,
          totalHours: '4.5小时',
          timeChange: 20,
          totalCalories: 1250,
          calorieChange: 15,
          consistency: '75%',
          consistencyChange: -5
        };
        break;
      case 'month':
        newOverviewStats = {
          totalWorkouts: 22,
          workoutChange: 15,
          totalHours: '18.5小时',
          timeChange: 12,
          totalCalories: 4850,
          calorieChange: 18,
          consistency: '85%',
          consistencyChange: 8
        };
        break;
      case 'year':
        newOverviewStats = {
          totalWorkouts: 185,
          workoutChange: 25,
          totalHours: '156小时',
          timeChange: 30,
          totalCalories: 38500,
          calorieChange: 22,
          consistency: '78%',
          consistencyChange: 3
        };
        break;
    }
    
    this.setData({ overviewStats: newOverviewStats });
  },

  /**
   * 体征数据类型切换
   */
  onBodyDataTypeChange(e) {
    const selectedIndex = parseInt(e.detail.value);
    this.setData({ selectedBodyDataIndex: selectedIndex });
    
    this.updateCurrentBodyData();
    this.drawBodyDataChart();
  },

  updateCurrentBodyData() {
    const currentType = this.data.bodyDataTypes[this.data.selectedBodyDataIndex];
    let newBodyData = {};
    
    switch (currentType.value) {
      case 'weight':
        newBodyData = { current: 68.5, change: -1.2, target: 65.0, unit: 'kg' };
        break;
      case 'bodyFat':
        newBodyData = { current: 15.8, change: -0.5, target: 12.0, unit: '%' };
        break;
      case 'muscle':
        newBodyData = { current: 42.3, change: 0.8, target: 45.0, unit: 'kg' };
        break;
      case 'visceralFat':
        newBodyData = { current: 3, change: 0, target: 2, unit: '级' };
        break;
    }
    
    this.setData({ currentBodyData: newBodyData });
  },

  /**
   * 初始化图表
   */
  initCharts() {
    // 获取图表上下文
    this.workoutChartCtx = wx.createCanvasContext('workoutChart', this);
    this.bodyDataChartCtx = wx.createCanvasContext('bodyDataChart', this);
    this.distributionChartCtx = wx.createCanvasContext('distributionChart', this);
    
    // 绘制所有图表
    this.drawWorkoutChart();
    this.drawBodyDataChart();
    this.drawDistributionChart();
  },

  refreshCharts() {
    setTimeout(() => {
      this.drawWorkoutChart();
      this.drawBodyDataChart();
    }, 100);
  },

  /**
   * 绘制训练频率图表
   */
  drawWorkoutChart() {
    if (!this.workoutChartCtx) return;
    
    const ctx = this.workoutChartCtx;
    const { labels, data } = this.data.chartData.workout;
    
    // 获取画布尺寸
    const canvasWidth = 686; // rpx转换为实际像素
    const canvasHeight = 400;
    const padding = 60;
    const chartWidth = canvasWidth - padding * 2;
    const chartHeight = canvasHeight - padding * 2;
    
    // 清除画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    // 计算最大值
    const maxValue = Math.max(...data, 3); // 至少为3
    
    // 绘制网格线
    ctx.setStrokeStyle('#f3f4f6');
    ctx.setLineWidth(1);
    
    for (let i = 0; i <= 3; i++) {
      const y = padding + (chartHeight / 3) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(canvasWidth - padding, y);
      ctx.stroke();
    }
    
    // 绘制柱状图
    const barWidth = chartWidth / labels.length * 0.6;
    const barSpacing = chartWidth / labels.length;
    
    data.forEach((value, index) => {
      const barHeight = (value / maxValue) * chartHeight;
      const x = padding + index * barSpacing + (barSpacing - barWidth) / 2;
      const y = canvasHeight - padding - barHeight;
      
      // 绘制柱子
      ctx.setFillStyle('#667eea');
      ctx.fillRect(x, y, barWidth, barHeight);
      
      // 绘制数值
      ctx.setFillStyle('#1f2937');
      ctx.setFontSize(12);
      ctx.setTextAlign('center');
      ctx.fillText(value.toString(), x + barWidth / 2, y - 5);
      
      // 绘制标签
      ctx.setFillStyle('#6b7280');
      ctx.setFontSize(10);
      ctx.fillText(labels[index], x + barWidth / 2, canvasHeight - padding + 20);
    });
    
    ctx.draw();
  },

  /**
   * 绘制体征数据图表
   */
  drawBodyDataChart() {
    if (!this.bodyDataChartCtx) return;
    
    const ctx = this.bodyDataChartCtx;
    const currentType = this.data.bodyDataTypes[this.data.selectedBodyDataIndex];
    const data = this.data.chartData.bodyData.weight; // 简化处理，使用weight数据
    
    const canvasWidth = 686;
    const canvasHeight = 300;
    const padding = 40;
    const chartWidth = canvasWidth - padding * 2;
    const chartHeight = canvasHeight - padding * 2;
    
    // 清除画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    if (data.length === 0) return;
    
    // 计算数据范围
    const values = data.map(item => item.value);
    const minValue = Math.min(...values) - 1;
    const maxValue = Math.max(...values) + 1;
    const valueRange = maxValue - minValue;
    
    // 绘制网格线
    ctx.setStrokeStyle('#f3f4f6');
    ctx.setLineWidth(1);
    
    for (let i = 0; i <= 4; i++) {
      const y = padding + (chartHeight / 4) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(canvasWidth - padding, y);
      ctx.stroke();
    }
    
    // 绘制折线
    ctx.setStrokeStyle('#667eea');
    ctx.setLineWidth(3);
    ctx.beginPath();
    
    data.forEach((item, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth;
      const y = canvasHeight - padding - ((item.value - minValue) / valueRange) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
    
    // 绘制数据点
    data.forEach((item, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth;
      const y = canvasHeight - padding - ((item.value - minValue) / valueRange) * chartHeight;
      
      ctx.setFillStyle('#667eea');
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();
      
      // 绘制数值
      ctx.setFillStyle('#1f2937');
      ctx.setFontSize(10);
      ctx.setTextAlign('center');
      ctx.fillText(item.value.toString(), x, y - 10);
      
      // 绘制日期
      ctx.setFillStyle('#6b7280');
      ctx.setFontSize(8);
      ctx.fillText(item.date, x, canvasHeight - padding + 15);
    });
    
    ctx.draw();
  },

  /**
   * 绘制训练类型分布饼图
   */
  drawDistributionChart() {
    if (!this.distributionChartCtx) return;
    
    const ctx = this.distributionChartCtx;
    const data = this.data.workoutDistribution;
    
    const canvasSize = 300;
    const centerX = canvasSize / 2;
    const centerY = canvasSize / 2;
    const radius = 100;
    
    // 清除画布
    ctx.clearRect(0, 0, canvasSize, canvasSize);
    
    let currentAngle = -Math.PI / 2; // 从顶部开始
    
    data.forEach(item => {
      const sliceAngle = (item.percentage / 100) * 2 * Math.PI;
      
      // 绘制扇形
      ctx.setFillStyle(item.color);
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      ctx.fill();
      
      currentAngle += sliceAngle;
    });
    
    // 绘制中心圆
    ctx.setFillStyle('#ffffff');
    ctx.beginPath();
    ctx.arc(centerX, centerY, 40, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.draw();
  },

  /**
   * 图表触摸事件
   */
  onChartTouch(e) {
    if (e.type === 'touchstart' || e.type === 'touchmove') {
      const touch = e.touches[0];
      const x = touch.x;
      const y = touch.y;
      
      // 简化的tooltip显示
      this.setData({
        showTooltip: true,
        tooltipX: x,
        tooltipY: y,
        tooltipData: {
          date: '12月20日',
          value: 2
        }
      });
    } else if (e.type === 'touchend') {
      this.setData({ showTooltip: false });
    }
  },

  /**
   * 成就相关
   */
  onViewAllAchievements() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  onAchievementDetail(e) {
    const { achievement } = e.currentTarget.dataset;
    
    let content = achievement.description;
    if (!achievement.unlocked) {
      content += `\n进度：${achievement.current}/${achievement.target}`;
    } else {
      content += `\n解锁时间：${achievement.unlockDate}`;
    }
    
    wx.showModal({
      title: achievement.name,
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 数据导出
   */
  onExportData(e) {
    const { type } = e.currentTarget.dataset;
    
    wx.showLoading({ title: '导出中...' });
    
    setTimeout(() => {
      wx.hideLoading();
      
      if (type === 'csv') {
        wx.showModal({
          title: '导出成功',
          content: '数据已导出为CSV格式，请在文件管理器中查看',
          showCancel: false
        });
      }
    }, 2000);
  },

  onShareReport() {
    const { overviewStats } = this.data;
    
    wx.showShareMenu({
      withShareTicket: true,
      success: () => {
        wx.showToast({
          title: '分享成功！',
          icon: 'success'
        });
      }
    });
  },

  onShareData() {
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  /**
   * 加载分析数据
   */
  loadAnalyticsData() {
    try {
      const savedData = wx.getStorageSync('analyticsData');
      if (savedData) {
        this.setData({
          overviewStats: savedData.stats || this.data.overviewStats,
          personalRecords: savedData.records || this.data.personalRecords,
          recentAchievements: savedData.achievements || this.data.recentAchievements
        });
      }
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    }
  },

  /**
   * 保存分析数据
   */
  saveAnalyticsData() {
    try {
      const dataToSave = {
        stats: this.data.overviewStats,
        records: this.data.personalRecords,
        achievements: this.data.recentAchievements,
        timestamp: Date.now()
      };
      
      wx.setStorageSync('analyticsData', dataToSave);
    } catch (error) {
      console.error('Failed to save analytics data:', error);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('Refreshing analytics page...');
    
    // 重新加载数据
    this.loadAnalyticsData();
    
    // 重新绘制图表
    this.refreshCharts();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 页面隐藏时保存数据
   */
  onHide() {
    this.saveAnalyticsData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { overviewStats } = this.data;
    return {
      title: `我的健身数据：${overviewStats.totalWorkouts}次训练，${overviewStats.totalHours}训练时长！`,
      path: '/pages/analytics/analytics',
      imageUrl: '/assets/images/analytics-share.jpg'
    };
  }
}); 