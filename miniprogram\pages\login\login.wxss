/* 登录页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 120rpx;
  height: 120rpx;
  top: 30%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.9;
  }
}

/* 应用头部 */
.app-header {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-top: 80rpx;
  margin-bottom: 100rpx;
}

.app-logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

.app-name {
  display: block;
  font-size: 64rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.app-slogan {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 2rpx;
}

/* 登录表单 */
.login-form {
  position: relative;
  z-index: 2;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

/* 微信快速登录 */
.quick-login-section {
  margin-bottom: 20rpx;
}

.wechat-login-btn {
  width: 100%;
  height: 100rpx;
  background: #07c160;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.4);
  transition: all 0.3s ease;
}

.wechat-login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.4);
}

.wechat-icon {
  width: 48rpx;
  height: 48rpx;
}

.login-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin: 20rpx 0;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 手机号登录 */
.phone-login-section {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.input-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 0 32rpx;
  height: 96rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.input-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #667eea;
}

.input-field {
  flex: 1;
  font-size: 32rpx;
  color: #1f2937;
  background: transparent;
  border: none;
}

.code-input {
  margin-right: 16rpx;
}

.get-code-btn {
  padding: 16rpx 32rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.2s ease;
}

.get-code-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.get-code-btn:active:not(.disabled) {
  transform: scale(0.95);
}

.phone-login-btn {
  width: 100%;
  height: 100rpx;
  background: #ffffff;
  color: #667eea;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.phone-login-btn.disabled {
  background: rgba(255, 255, 255, 0.5);
  color: rgba(102, 126, 234, 0.5);
  box-shadow: none;
}

.phone-login-btn:active:not(.disabled) {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 255, 255, 0.3);
}

/* 用户协议 */
.agreement-section {
  margin-top: 20rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.agreement-link {
  color: #ffffff;
  text-decoration: underline;
}

/* 其他登录方式 */
.other-login-section {
  position: relative;
  z-index: 2;
  margin-top: 60rpx;
}

.section-title {
  text-align: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 32rpx;
}

.other-login-buttons {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}

.other-login-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 24rpx;
  border: none;
  transition: all 0.2s ease;
}

.other-login-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.other-login-icon {
  font-size: 48rpx;
}

.other-login-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 底部信息 */
.footer-info {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-top: 40rpx;
}

.footer-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.version-text {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
}

/* 协议弹窗 */
.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.agreement-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.modal-body {
  max-height: 500rpx;
  padding: 20rpx 40rpx;
}

.agreement-content {
  padding: 20rpx 0;
}

.agreement-content-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.6;
  white-space: pre-line;
}

.modal-actions {
  padding: 20rpx 40rpx 40rpx;
}

.modal-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #667eea;
  color: #ffffff;
}

.modal-btn:active {
  transform: scale(0.98);
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.loading-overlay.show {
  opacity: 1;
  pointer-events: all;
}

.loading-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f4f6;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6b7280;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .container {
    padding: 40rpx 32rpx 32rpx;
  }
  
  .app-header {
    margin-top: 40rpx;
    margin-bottom: 60rpx;
  }
  
  .app-logo {
    width: 120rpx;
    height: 120rpx;
  }
  
  .app-name {
    font-size: 48rpx;
  }
} 