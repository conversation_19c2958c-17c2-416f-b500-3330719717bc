const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 当前训练状态
    currentWorkout: {
      isActive: false,
      name: '',
      duration: '00:00',
      progress: 0,
      completedExercises: 0,
      totalExercises: 0,
      startTime: null
    },
    
    // 训练模板
    workoutTemplates: [
      { id: 1, name: '力量训练', icon: '💪', duration: '45分钟', exercises: 8, difficulty: 'medium' },
      { id: 2, name: '有氧运动', icon: '🏃‍♂️', duration: '30分钟', exercises: 6, difficulty: 'easy' },
      { id: 3, name: 'HIIT', icon: '🔥', duration: '20分钟', exercises: 10, difficulty: 'hard' },
      { id: 4, name: '瑜伽拉伸', icon: '🧘‍♀️', duration: '25分钟', exercises: 12, difficulty: 'easy' },
      { id: 5, name: '核心训练', icon: '⚡', duration: '15分钟', exercises: 6, difficulty: 'medium' }
    ],
    
    // 本周统计
    weeklyStats: {
      workouts: 5,
      duration: '6.5小时',
      calories: 1850,
      streak: 7
    },
    
    // 今日训练计划
    todayPlan: [
      { id: 1, name: '深蹲', sets: 3, reps: 15, completed: true },
      { id: 2, name: '俯卧撑', sets: 3, reps: 12, completed: false },
      { id: 3, name: '卷腹', sets: 4, reps: 20, completed: false },
      { id: 4, name: '平板支撑', sets: 3, reps: '60秒', completed: false }
    ],
    
    // 过滤选项
    filterTabs: [
      { label: '全部', value: 'all' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' }
    ],
    historyFilter: 'all',
    
    // 训练历史记录
    workoutHistory: [
      {
        id: 1,
        name: '力量训练',
        icon: '💪',
        duration: '45分钟',
        exercises: 8,
        calories: 420,
        date: '今天 18:30',
        rating: 4,
        type: 'strength'
      },
      {
        id: 2,
        name: 'HIIT训练',
        icon: '🔥',
        duration: '20分钟',
        exercises: 10,
        calories: 280,
        date: '昨天 19:00',
        rating: 5,
        type: 'cardio'
      },
      {
        id: 3,
        name: '瑜伽拉伸',
        icon: '🧘‍♀️',
        duration: '30分钟',
        exercises: 12,
        calories: 150,
        date: '12月20日',
        rating: 4,
        type: 'flexibility'
      },
      {
        id: 4,
        name: '有氧运动',
        icon: '🏃‍♂️',
        duration: '35分钟',
        exercises: 6,
        calories: 350,
        date: '12月19日',
        rating: 3,
        type: 'cardio'
      }
    ],
    
    hasMoreHistory: true,
    
    // 弹窗状态
    showCreateModal: false,
    showCompleteModal: false,
    
    // 新建训练
    newWorkout: {
      name: '',
      typeIndex: 0,
      duration: ''
    },
    
    workoutTypes: [
      { name: '力量训练', value: 'strength' },
      { name: '有氧运动', value: 'cardio' },
      { name: 'HIIT', value: 'hiit' },
      { name: '瑜伽', value: 'yoga' },
      { name: '核心训练', value: 'core' },
      { name: '功能性训练', value: 'functional' }
    ],
    
    // 完成的训练数据
    completedWorkout: {
      duration: 0,
      exercises: 0,
      calories: 0,
      rating: 0
    }
  },

  /**
   * 定时器
   */
  workoutTimer: null,

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Workout page loaded');
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从store恢复数据
    this.loadDataFromStore();
    
    // 更新过滤后的历史记录
    this.updateFilteredHistory();
    
    // 如果有活跃的训练，继续计时
    if (this.data.currentWorkout.isActive) {
      this.startWorkoutTimer();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 保存数据到store
    this.saveDataToStore();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.clearWorkoutTimer();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 加载缓存的训练数据
    this.loadWorkoutData();
    
    // 更新统计数据
    this.updateWeeklyStats();
  },

  /**
   * 从store加载数据
   */
  loadDataFromStore() {
    const state = store.getState();
    
    // 加载训练数据
    if (state.fitness.currentWorkout) {
      this.setData({
        currentWorkout: state.fitness.currentWorkout
      });
    }
    
    // 加载历史记录
    if (state.fitness.workoutHistory.length > 0) {
      this.setData({
        workoutHistory: state.fitness.workoutHistory
      });
    }
  },

  /**
   * 保存数据到store
   */
  saveDataToStore() {
    store.updateFitnessData({
      currentWorkout: this.data.currentWorkout,
      workoutHistory: this.data.workoutHistory
    });
  },

  /**
   * 训练模板操作
   */
  onStartTemplate(e) {
    const { template } = e.currentTarget.dataset;
    
    wx.showModal({
      title: template.name,
      content: `开始${template.name}训练？\n预计时长：${template.duration}\n动作数量：${template.exercises}个`,
      confirmText: '开始训练',
      success: (res) => {
        if (res.confirm) {
          this.startWorkout(template);
        }
      }
    });
  },

  startWorkout(template) {
    const startTime = Date.now();
    
    this.setData({
      'currentWorkout.isActive': true,
      'currentWorkout.name': template.name,
      'currentWorkout.startTime': startTime,
      'currentWorkout.totalExercises': template.exercises,
      'currentWorkout.completedExercises': 0,
      'currentWorkout.progress': 0
    });
    
    this.startWorkoutTimer();
    
    // 触觉反馈
    wx.vibrateShort();
    
    wx.showToast({
      title: '训练开始！',
      icon: 'success'
    });
  },

  startWorkoutTimer() {
    this.clearWorkoutTimer();
    
    this.workoutTimer = setInterval(() => {
      if (!this.data.currentWorkout.isActive) {
        this.clearWorkoutTimer();
        return;
      }
      
      const elapsed = Date.now() - this.data.currentWorkout.startTime;
      const duration = this.formatDuration(elapsed);
      
      this.setData({
        'currentWorkout.duration': duration
      });
      
    }, 1000);
  },

  clearWorkoutTimer() {
    if (this.workoutTimer) {
      clearInterval(this.workoutTimer);
      this.workoutTimer = null;
    }
  },

  formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
  },

  /**
   * 训练控制
   */
  onPauseWorkout() {
    wx.showModal({
      title: '暂停训练',
      content: '确定要暂停当前训练吗？',
      confirmText: '暂停',
      cancelText: '继续',
      success: (res) => {
        if (res.confirm) {
          this.pauseWorkout();
        }
      }
    });
  },

  pauseWorkout() {
    this.setData({
      'currentWorkout.isActive': false
    });
    
    this.clearWorkoutTimer();
    
    wx.showToast({
      title: '训练已暂停',
      icon: 'none'
    });
  },

  onStopWorkout() {
    wx.showModal({
      title: '结束训练',
      content: '确定要结束当前训练吗？训练数据将被保存。',
      confirmText: '结束',
      cancelText: '继续',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.completeWorkout();
        }
      }
    });
  },

  completeWorkout() {
    const currentWorkout = this.data.currentWorkout;
    const elapsed = Date.now() - currentWorkout.startTime;
    const durationMinutes = Math.round(elapsed / 60000);
    
    // 计算卡路里（简单估算）
    const calories = Math.round(durationMinutes * 8); // 每分钟8卡路里
    
    // 设置完成数据
    this.setData({
      'completedWorkout.duration': durationMinutes,
      'completedWorkout.exercises': currentWorkout.completedExercises,
      'completedWorkout.calories': calories,
      'completedWorkout.rating': 0,
      showCompleteModal: true
    });
    
    // 重置当前训练
    this.setData({
      'currentWorkout.isActive': false,
      'currentWorkout.name': '',
      'currentWorkout.duration': '00:00',
      'currentWorkout.progress': 0,
      'currentWorkout.completedExercises': 0,
      'currentWorkout.totalExercises': 0,
      'currentWorkout.startTime': null
    });
    
    this.clearWorkoutTimer();
    
    // 更新统计
    this.updateWeeklyStats();
  },

  /**
   * 统计数据
   */
  updateWeeklyStats() {
    // 模拟更新统计数据
    const stats = {
      workouts: this.data.weeklyStats.workouts,
      duration: this.data.weeklyStats.duration,
      calories: this.data.weeklyStats.calories,
      streak: this.data.weeklyStats.streak
    };
    
    this.setData({ weeklyStats: stats });
  },

  onViewAllStats() {
    wx.navigateTo({
      url: '/pages/analytics/analytics?tab=workout'
    });
  },

  /**
   * 训练计划管理
   */
  onTogglePlanItem(e) {
    const { index } = e.currentTarget.dataset;
    const todayPlan = [...this.data.todayPlan];
    todayPlan[index].completed = !todayPlan[index].completed;
    
    this.setData({ todayPlan });
    
    // 更新当前训练进度
    if (this.data.currentWorkout.isActive) {
      const completed = todayPlan.filter(item => item.completed).length;
      const progress = (completed / todayPlan.length) * 100;
      
      this.setData({
        'currentWorkout.completedExercises': completed,
        'currentWorkout.progress': progress
      });
    }
    
    // 触觉反馈
    wx.vibrateShort();
  },

  onEditPlan() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 历史记录管理
   */
  updateFilteredHistory() {
    const { workoutHistory, historyFilter } = this.data;
    let filtered = [...workoutHistory];
    
    // 根据筛选条件过滤
    if (historyFilter === 'week') {
      const weekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
      filtered = filtered.filter(item => {
        // 简化处理，实际应该解析日期
        return item.date.includes('今天') || item.date.includes('昨天');
      });
    } else if (historyFilter === 'month') {
      // 本月的记录
      filtered = filtered.slice(0, 3);
    }
    
    this.setData({ filteredHistory: filtered });
  },

  onFilterChange(e) {
    const { filter } = e.currentTarget.dataset;
    this.setData({ historyFilter: filter });
    this.updateFilteredHistory();
  },

  onViewWorkoutDetail(e) {
    const { workout } = e.currentTarget.dataset;
    
    wx.showModal({
      title: workout.name,
      content: `时长：${workout.duration}\n动作：${workout.exercises}个\n卡路里：${workout.calories}\n评分：${'★'.repeat(workout.rating)}${'☆'.repeat(5 - workout.rating)}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  onLoadMoreHistory() {
    wx.showLoading({ title: '加载中...' });
    
    // 模拟加载更多数据
    setTimeout(() => {
      wx.hideLoading();
      
      const moreHistory = [
        {
          id: 5,
          name: '核心训练',
          icon: '⚡',
          duration: '15分钟',
          exercises: 6,
          calories: 120,
          date: '12月18日',
          rating: 4,
          type: 'core'
        }
      ];
      
      this.setData({
        workoutHistory: [...this.data.workoutHistory, ...moreHistory],
        hasMoreHistory: false
      });
      
      this.updateFilteredHistory();
    }, 1000);
  },

  /**
   * 创建训练弹窗
   */
  onAddWorkout() {
    this.setData({ showCreateModal: true });
  },

  onCloseCreateModal() {
    this.setData({
      showCreateModal: false,
      'newWorkout.name': '',
      'newWorkout.typeIndex': 0,
      'newWorkout.duration': ''
    });
  },

  onWorkoutNameInput(e) {
    this.setData({
      'newWorkout.name': e.detail.value
    });
  },

  onWorkoutTypeChange(e) {
    this.setData({
      'newWorkout.typeIndex': parseInt(e.detail.value)
    });
  },

  onWorkoutDurationInput(e) {
    this.setData({
      'newWorkout.duration': e.detail.value
    });
  },

  onCreateWorkout() {
    const { name, typeIndex, duration } = this.data.newWorkout;
    
    if (!name.trim()) {
      wx.showToast({
        title: '请输入训练名称',
        icon: 'none'
      });
      return;
    }
    
    if (!duration) {
      wx.showToast({
        title: '请输入预计时长',
        icon: 'none'
      });
      return;
    }
    
    // 创建新的训练模板
    const newTemplate = {
      id: Date.now(),
      name: name.trim(),
      icon: '🏋️‍♂️',
      duration: `${duration}分钟`,
      exercises: 6,
      difficulty: 'custom'
    };
    
    // 添加到模板列表
    this.setData({
      workoutTemplates: [...this.data.workoutTemplates, newTemplate]
    });
    
    this.onCloseCreateModal();
    
    wx.showToast({
      title: '创建成功！',
      icon: 'success'
    });
  },

  /**
   * 训练完成弹窗
   */
  onRateWorkout(e) {
    const { rating } = e.currentTarget.dataset;
    this.setData({
      'completedWorkout.rating': rating
    });
  },

  onShareWorkout() {
    const { duration, calories, rating } = this.data.completedWorkout;
    
    wx.showShareMenu({
      withShareTicket: true,
      success: () => {
        wx.showToast({
          title: '分享成功！',
          icon: 'success'
        });
      }
    });
  },

  onCloseCompleteModal() {
    // 保存训练记录到历史
    const { completedWorkout } = this.data;
    const newRecord = {
      id: Date.now(),
      name: '自定义训练',
      icon: '🏋️‍♂️',
      duration: `${completedWorkout.duration}分钟`,
      exercises: completedWorkout.exercises,
      calories: completedWorkout.calories,
      date: '刚刚',
      rating: completedWorkout.rating,
      type: 'custom'
    };
    
    this.setData({
      workoutHistory: [newRecord, ...this.data.workoutHistory],
      showCompleteModal: false
    });
    
    this.updateFilteredHistory();
    this.updateWeeklyStats();
  },

  /**
   * 加载训练数据
   */
  loadWorkoutData() {
    try {
      const savedData = wx.getStorageSync('workoutData');
      if (savedData) {
        this.setData({
          workoutHistory: savedData.history || this.data.workoutHistory,
          todayPlan: savedData.plan || this.data.todayPlan,
          weeklyStats: savedData.stats || this.data.weeklyStats
        });
      }
    } catch (error) {
      console.error('Failed to load workout data:', error);
    }
  },

  /**
   * 保存训练数据
   */
  saveWorkoutData() {
    try {
      const dataToSave = {
        history: this.data.workoutHistory,
        plan: this.data.todayPlan,
        stats: this.data.weeklyStats,
        timestamp: Date.now()
      };
      
      wx.setStorageSync('workoutData', dataToSave);
    } catch (error) {
      console.error('Failed to save workout data:', error);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('Refreshing workout page...');
    
    // 更新统计数据
    this.updateWeeklyStats();
    
    // 刷新历史记录
    this.updateFilteredHistory();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMoreHistory) {
      this.onLoadMoreHistory();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { weeklyStats } = this.data;
    return {
      title: `本周已完成${weeklyStats.workouts}次训练，坚持健身，遇见更好的自己！`,
      path: '/pages/workout/workout',
      imageUrl: '/assets/images/workout-share.jpg'
    };
  }
}); 