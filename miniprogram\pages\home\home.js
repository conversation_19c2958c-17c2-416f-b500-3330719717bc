const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {
      nickname: 'FitFocus',
      avatar: '/assets/images/avatar.png'
    },
    
    // 番茄钟状态
    pomodoro: {
      isRunning: false,
      timeLeft: 25 * 60, // 25分钟（秒）
      totalTime: 25 * 60,
      displayTime: '25:00',
      currentSession: 1,
      totalSessions: 4
    },
    
    // 今日目标
    goals: [
      { id: 1, text: '完成30分钟有氧运动', completed: true },
      { id: 2, text: '力量训练 - 胸肌和三头肌', completed: false },
      { id: 3, text: '10分钟拉伸放松', completed: false },
      { id: 4, text: '喝水2升', completed: false }
    ],
    
    // 推荐课程
    recommendedCourse: {
      name: '高强度间歇训练 HIIT',
      instructor: '张教练',
      time: '19:00-20:00',
      spots: '5个名额',
      image: '/assets/images/course-hiit.jpg'
    },
    
    // 设备状态
    equipmentList: [
      { name: '跑步机', icon: '🏃‍♂️', available: 3, total: 8, status: 'available' },
      { name: '力量区', icon: '💪', available: 1, total: 6, status: 'busy' },
      { name: '淋浴间', icon: '🚿', available: 0, total: 4, status: 'full' },
      { name: '瑜伽室', icon: '🧘‍♀️', available: 12, total: 15, status: 'available' }
    ],
    
    // 弹窗显示状态
    showAchievementToast: false,
    showPomodoroModal: false,
    
    // 成就信息
    achievementInfo: {
      title: '连续打卡7天!',
      desc: '坚持就是胜利，继续保持！'
    }
  },

  /**
   * 定时器相关
   */
  timer: null,

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Home page loaded');
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从store获取最新状态
    const state = store.getState();
    
    // 更新用户信息
    if (state.user.isLogin) {
      this.setData({
        'userInfo.nickname': state.user.userInfo.nickname || 'FitFocus',
        'userInfo.avatar': state.user.userInfo.avatar || '/assets/images/avatar.png'
      });
    }
    
    // 更新番茄钟状态
    if (state.pomodoro.isRunning) {
      this.setData({
        'pomodoro.isRunning': true,
        'pomodoro.timeLeft': state.pomodoro.timeLeft,
        'pomodoro.displayTime': this.formatTime(state.pomodoro.timeLeft)
      });
      this.startTimer();
    }
    
    // 更新设备状态
    this.updateEquipmentStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 保存番茄钟状态到store
    if (this.data.pomodoro.isRunning) {
      store.setState({
        pomodoro: {
          ...store.getState().pomodoro,
          isRunning: this.data.pomodoro.isRunning,
          timeLeft: this.data.pomodoro.timeLeft
        }
      });
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.clearTimer();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 获取系统信息设置状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 检查是否有成就需要显示
    this.checkAchievements();
    
    // 从缓存恢复番茄钟状态
    this.restorePomodoroState();
  },

  /**
   * 番茄钟控制
   */
  togglePomodoro() {
    const { isRunning } = this.data.pomodoro;
    
    if (isRunning) {
      this.pausePomodoro();
    } else {
      this.startPomodoro();
    }
  },

  startPomodoro() {
    this.setData({
      'pomodoro.isRunning': true
    });
    
    // 更新store状态
    store.updatePomodoroTimer({
      isRunning: true,
      timeLeft: this.data.pomodoro.timeLeft
    });
    
    this.startTimer();
    
    // 触觉反馈
    wx.vibrateShort();
  },

  pausePomodoro() {
    this.setData({
      'pomodoro.isRunning': false
    });
    
    store.updatePomodoroTimer({
      isRunning: false,
      timeLeft: this.data.pomodoro.timeLeft
    });
    
    this.clearTimer();
  },

  resetPomodoro() {
    this.clearTimer();
    this.setData({
      'pomodoro.isRunning': false,
      'pomodoro.timeLeft': this.data.pomodoro.totalTime,
      'pomodoro.displayTime': this.formatTime(this.data.pomodoro.totalTime)
    });
    
    store.updatePomodoroTimer({
      isRunning: false,
      timeLeft: this.data.pomodoro.totalTime
    });
  },

  startTimer() {
    this.clearTimer();
    
    this.timer = setInterval(() => {
      const timeLeft = this.data.pomodoro.timeLeft - 1;
      
      if (timeLeft <= 0) {
        this.onPomodoroComplete();
        return;
      }
      
      this.setData({
        'pomodoro.timeLeft': timeLeft,
        'pomodoro.displayTime': this.formatTime(timeLeft)
      });
      
      // 更新store
      store.updatePomodoroTimer({
        timeLeft: timeLeft
      });
      
    }, 1000);
  },

  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  onPomodoroComplete() {
    this.clearTimer();
    this.setData({
      'pomodoro.isRunning': false,
      showPomodoroModal: true
    });
    
    // 播放完成提示音
    wx.showToast({
      title: '番茄钟完成！',
      icon: 'success'
    });
    
    // 触觉反馈
    wx.vibrateLong();
    
    // 更新目标完成状态
    this.updateGoalProgress();
  },

  /**
   * 格式化时间显示
   */
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  /**
   * 目标管理
   */
  toggleGoal(e) {
    const { index } = e.currentTarget.dataset;
    const goals = [...this.data.goals];
    goals[index].completed = !goals[index].completed;
    
    this.setData({ goals });
    
    // 检查是否有新成就
    this.checkGoalAchievements(goals);
    
    // 触觉反馈
    wx.vibrateShort();
  },

  updateGoalProgress() {
    // 标记第一个未完成的目标为完成
    const goals = [...this.data.goals];
    const incompleteIndex = goals.findIndex(goal => !goal.completed);
    
    if (incompleteIndex !== -1) {
      goals[incompleteIndex].completed = true;
      this.setData({ goals });
      this.checkGoalAchievements(goals);
    }
  },

  checkGoalAchievements(goals) {
    const completedCount = goals.filter(goal => goal.completed).length;
    const totalCount = goals.length;
    
    if (completedCount === totalCount) {
      this.showAchievement('今日目标全部完成！', '太棒了，明天继续加油！');
    }
  },

  /**
   * 快捷操作
   */
  onScanCode() {
    wx.navigateTo({
      url: '/pages/scanner/scanner?type=equipment'
    });
  },

  onBookCourse() {
    wx.navigateTo({
      url: '/pages/course-detail/course-detail?id=1'
    });
  },

  /**
   * 课程相关
   */
  onCourseDetail() {
    wx.navigateTo({
      url: '/pages/course-detail/course-detail?id=1'
    });
  },

  onBookRecommended() {
    wx.showLoading({ title: '预约中...' });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '预约成功！',
        icon: 'success'
      });
    }, 1500);
  },

  /**
   * 设备状态
   */
  updateEquipmentStatus() {
    // 模拟实时设备状态更新
    const equipmentList = [...this.data.equipmentList];
    
    equipmentList.forEach(equipment => {
      // 随机更新可用数量（模拟实时数据）
      const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
      let newAvailable = equipment.available + change;
      newAvailable = Math.max(0, Math.min(newAvailable, equipment.total));
      
      equipment.available = newAvailable;
      
      // 更新状态
      if (newAvailable === 0) {
        equipment.status = 'full';
      } else if (newAvailable <= equipment.total * 0.3) {
        equipment.status = 'busy';
      } else {
        equipment.status = 'available';
      }
    });
    
    this.setData({ equipmentList });
  },

  onEquipmentDetail(e) {
    const { index } = e.currentTarget.dataset;
    const equipment = this.data.equipmentList[index];
    
    wx.showModal({
      title: equipment.name,
      content: `可用：${equipment.available}/${equipment.total}\n状态：${this.getStatusText(equipment.status)}`,
      confirmText: '扫码使用',
      success: (res) => {
        if (res.confirm) {
          this.onScanCode();
        }
      }
    });
  },

  getStatusText(status) {
    const statusMap = {
      'available': '充足',
      'busy': '紧张',
      'full': '已满'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 成就系统
   */
  checkAchievements() {
    // 检查连续打卡
    const consecutiveDays = wx.getStorageSync('consecutiveDays') || 0;
    if (consecutiveDays >= 7 && consecutiveDays % 7 === 0) {
      this.showAchievement('连续打卡7天!', '坚持就是胜利，继续保持！');
    }
  },

  showAchievement(title, desc) {
    this.setData({
      'achievementInfo.title': title,
      'achievementInfo.desc': desc,
      showAchievementToast: true
    });
    
    // 3秒后隐藏
    setTimeout(() => {
      this.setData({ showAchievementToast: false });
    }, 3000);
  },

  /**
   * 弹窗控制
   */
  closePomodoroModal() {
    this.setData({ showPomodoroModal: false });
  },

  onStartNext() {
    this.setData({ showPomodoroModal: false });
    this.resetPomodoro();
    this.startPomodoro();
  },

  onTakeBreak() {
    this.setData({ showPomodoroModal: false });
    this.resetPomodoro();
    
    // 设置为休息时间（5分钟）
    this.setData({
      'pomodoro.totalTime': 5 * 60,
      'pomodoro.timeLeft': 5 * 60,
      'pomodoro.displayTime': '05:00'
    });
  },

  /**
   * 状态恢复
   */
  restorePomodoroState() {
    const savedState = wx.getStorageSync('pomodoroState');
    if (savedState) {
      const { timeLeft, isRunning, totalTime } = savedState;
      this.setData({
        'pomodoro.timeLeft': timeLeft,
        'pomodoro.totalTime': totalTime,
        'pomodoro.displayTime': this.formatTime(timeLeft),
        'pomodoro.isRunning': isRunning
      });
      
      if (isRunning) {
        this.startTimer();
      }
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('Refreshing home page...');
    
    // 更新设备状态
    this.updateEquipmentStatus();
    
    // 检查成就
    this.checkAchievements();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'FitFocus - 专注健身，科学训练',
      path: '/pages/home/<USER>',
      imageUrl: '/assets/images/share.jpg'
    };
  }
}); 