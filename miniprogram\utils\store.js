/**
 * 简单的状态管理工具
 * 用于管理小程序全局状态
 */

class Store {
  constructor() {
    this.state = {
      // 用户相关
      userInfo: null,
      memberLevel: 1, // 1铜 2银 3VIP
      
      // 番茄钟相关
      pomodoroTimer: {
        isRunning: false,
        duration: 25 * 60, // 25分钟
        remainingTime: 25 * 60,
        currentSession: 'focus', // focus, break
        completedToday: 0
      },
      
      // 健身数据
      fitnessData: {
        todayWorkouts: [],
        weeklyGoal: 4,
        completedWorkouts: 0,
        totalCalories: 0
      },
      
      // 设备状态
      equipmentStatus: {
        treadmill: { available: 5, total: 8 },
        strength: { available: 2, total: 6 },
        bike: { available: 12, total: 15 },
        shower: { available: 8, total: 10 }
      },
      
      // 课程数据
      courses: [],
      todayCourses: [],
      myBookings: [],
      
      // 社区数据
      communityPosts: [],
      myPosts: [],
      
      // 体测数据
      bodyData: {
        weight: 70.2,
        bodyFat: 15.8,
        muscle: 55.4,
        bmi: 22.1,
        lastUpdate: null
      }
    }
    
    this.listeners = new Map()
  }

  // 获取状态
  getState(key) {
    if (key) {
      return this.state[key]
    }
    return this.state
  }

  // 设置状态
  setState(updates) {
    const oldState = { ...this.state }
    
    if (typeof updates === 'function') {
      this.state = { ...this.state, ...updates(this.state) }
    } else {
      this.state = { ...this.state, ...updates }
    }
    
    // 通知监听器
    this.notifyListeners(oldState)
  }

  // 订阅状态变化
  subscribe(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, [])
    }
    this.listeners.get(key).push(callback)
    
    // 返回取消订阅函数
    return () => {
      const callbacks = this.listeners.get(key)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  // 通知监听器
  notifyListeners(oldState) {
    for (const [key, callbacks] of this.listeners) {
      if (this.state[key] !== oldState[key]) {
        callbacks.forEach(callback => {
          try {
            callback(this.state[key], oldState[key])
          } catch (error) {
            console.error('状态监听器错误:', error)
          }
        })
      }
    }
  }

  // 番茄钟相关方法
  startPomodoroTimer() {
    this.setState({
      pomodoroTimer: {
        ...this.state.pomodoroTimer,
        isRunning: true
      }
    })
  }

  pausePomodoroTimer() {
    this.setState({
      pomodoroTimer: {
        ...this.state.pomodoroTimer,
        isRunning: false
      }
    })
  }

  resetPomodoroTimer() {
    this.setState({
      pomodoroTimer: {
        ...this.state.pomodoroTimer,
        isRunning: false,
        remainingTime: this.state.pomodoroTimer.duration
      }
    })
  }

  completePomodoroSession() {
    const currentTimer = this.state.pomodoroTimer
    this.setState({
      pomodoroTimer: {
        ...currentTimer,
        isRunning: false,
        completedToday: currentTimer.completedToday + 1,
        remainingTime: currentTimer.duration
      }
    })
  }

  // 更新健身数据
  updateFitnessData(data) {
    this.setState({
      fitnessData: {
        ...this.state.fitnessData,
        ...data
      }
    })
  }

  // 更新设备状态
  updateEquipmentStatus(equipmentType, status) {
    this.setState({
      equipmentStatus: {
        ...this.state.equipmentStatus,
        [equipmentType]: status
      }
    })
  }

  // 添加课程预约
  addCourseBooking(booking) {
    this.setState({
      myBookings: [...this.state.myBookings, booking]
    })
  }

  // 取消课程预约
  cancelCourseBooking(bookingId) {
    this.setState({
      myBookings: this.state.myBookings.filter(booking => booking.id !== bookingId)
    })
  }

  // 更新体测数据
  updateBodyData(data) {
    this.setState({
      bodyData: {
        ...this.state.bodyData,
        ...data,
        lastUpdate: new Date().toISOString()
      }
    })
  }

  // 添加社区动态
  addCommunityPost(post) {
    this.setState({
      communityPosts: [post, ...this.state.communityPosts],
      myPosts: [post, ...this.state.myPosts]
    })
  }

  // 点赞动态
  likeCommunityPost(postId) {
    this.setState({
      communityPosts: this.state.communityPosts.map(post => 
        post.id === postId 
          ? { ...post, likes: post.likes + 1, isLiked: true }
          : post
      )
    })
  }

  // 持久化状态到本地存储
  persist() {
    try {
      wx.setStorageSync('app_state', this.state)
    } catch (error) {
      console.error('状态持久化失败:', error)
    }
  }

  // 从本地存储恢复状态
  restore() {
    try {
      const savedState = wx.getStorageSync('app_state')
      if (savedState) {
        this.state = { ...this.state, ...savedState }
      }
    } catch (error) {
      console.error('状态恢复失败:', error)
    }
  }
}

// 创建全局store实例
export function createStore() {
  return new Store()
}

// 默认导出
export default Store 