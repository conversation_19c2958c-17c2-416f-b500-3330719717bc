/* 课程详情页面样式 */
.container {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 180rpx;
}

/* 页面标题 */
.page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 32rpx 20rpx;
  margin-top: var(--status-bar-height, 44px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1rpx solid #e5e7eb;
}

.back-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.back-btn:active {
  transform: scale(0.9);
  background: #e5e7eb;
}

.back-icon {
  color: #374151;
  font-size: 32rpx;
  font-weight: bold;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.action-btns {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.action-btn.favorited {
  background: #fef2f2;
}

.action-icon {
  font-size: 32rpx;
}

/* 课程封面 */
.course-hero {
  position: relative;
  height: 500rpx;
  margin-top: 160rpx;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent);
  z-index: 2;
}

.course-badge {
  display: inline-block;
  padding: 8rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin-bottom: 16rpx;
}

.badge-text {
  font-size: 20rpx;
  color: #667eea;
  font-weight: 500;
}

.course-level {
  display: inline-block;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.course-level.beginner {
  background: rgba(34, 197, 94, 0.9);
}

.course-level.intermediate {
  background: rgba(251, 146, 60, 0.9);
}

.course-level.advanced {
  background: rgba(239, 68, 68, 0.9);
}

.level-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}

.hero-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  z-index: 2;
}

.course-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.course-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 24rpx;
}

.course-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 课程内容 */
.course-content {
  padding: 32rpx;
}

/* 通用章节样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 教练信息 */
.instructor-section {
  margin-bottom: 48rpx;
}

.instructor-card {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.instructor-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04);
}

.instructor-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  flex-shrink: 0;
}

.instructor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.instructor-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.instructor-title {
  font-size: 24rpx;
  color: #6b7280;
}

.instructor-stats {
  display: flex;
  gap: 32rpx;
  margin-top: 8rpx;
}

.instructor-stat {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #9ca3af;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #667eea;
}

.instructor-arrow {
  padding: 8rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 课程描述 */
.description-section {
  margin-bottom: 48rpx;
}

.description-content {
  padding: 24rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #374151;
  margin-bottom: 24rpx;
}

.description-highlights {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.highlight-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #667eea;
  color: #ffffff;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.highlight-text {
  font-size: 26rpx;
  color: #374151;
}

/* 课程安排 */
.schedule-section {
  margin-bottom: 48rpx;
}

.schedule-filter {
  display: flex;
  gap: 12rpx;
}

.filter-btn {
  padding: 12rpx 24rpx;
  background: #f3f4f6;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.filter-btn.active {
  background: #667eea;
}

.filter-btn:active {
  transform: scale(0.95);
}

.filter-text {
  font-size: 24rpx;
  color: #6b7280;
}

.filter-btn.active .filter-text {
  color: #ffffff;
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.schedule-item:active {
  transform: translateY(2rpx);
}

.schedule-item.booked {
  border: 2rpx solid #667eea;
}

.schedule-item.full {
  opacity: 0.6;
}

.schedule-item.past {
  opacity: 0.4;
}

.schedule-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100rpx;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 16rpx;
}

.date-weekday {
  font-size: 20rpx;
  color: #9ca3af;
  margin-bottom: 4rpx;
}

.date-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.date-month {
  font-size: 20rpx;
  color: #9ca3af;
}

.schedule-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.schedule-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-start, .time-end {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.time-separator {
  font-size: 24rpx;
  color: #9ca3af;
}

.schedule-location {
  font-size: 24rpx;
  color: #6b7280;
}

.schedule-participants {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.participants-count {
  font-size: 20rpx;
  color: #9ca3af;
}

.participants-avatars {
  display: flex;
  gap: -8rpx;
}

.participant-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
  margin-left: -8rpx;
}

.participant-avatar:first-child {
  margin-left: 0;
}

.schedule-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.status-badge.booked {
  background: #f0f9ff;
}

.status-badge.booked .status-text {
  color: #0369a1;
}

.status-badge.full {
  background: #fef2f2;
}

.status-badge.full .status-text {
  color: #dc2626;
}

.status-badge.past {
  background: #f9fafb;
}

.status-badge.past .status-text {
  color: #9ca3af;
}

.book-btn {
  padding: 12rpx 24rpx;
  background: #667eea;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.book-btn:active {
  transform: scale(0.95);
  background: #5a67d8;
}

.book-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.empty-schedule {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 学员评价 */
.reviews-section {
  margin-bottom: 48rpx;
}

.reviews-summary {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.rating-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #f59e0b;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  color: #d1d5db;
}

.star.filled {
  color: #f59e0b;
}

.rating-count {
  font-size: 24rpx;
  color: #6b7280;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.review-item {
  padding: 24rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.review-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.reviewer-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.reviewer-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.reviewer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

.review-rating {
  display: flex;
  gap: 4rpx;
}

.review-rating .star {
  font-size: 20rpx;
}

.review-date {
  font-size: 20rpx;
  color: #9ca3af;
}

.review-content {
  font-size: 26rpx;
  line-height: 1.5;
  color: #374151;
  margin-bottom: 16rpx;
}

.review-images {
  display: flex;
  gap: 12rpx;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
}

.view-all-reviews {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.view-all-reviews:active {
  background: #e5e7eb;
}

.view-all-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

.view-all-arrow {
  font-size: 24rpx;
  color: #667eea;
}

/* 相关推荐 */
.related-section {
  margin-bottom: 32rpx;
}

.related-courses {
  display: flex;
  gap: 20rpx;
  padding: 0 0 20rpx;
}

.related-course {
  display: flex;
  flex-direction: column;
  width: 240rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.related-course:active {
  transform: translateY(2rpx);
}

.related-image {
  width: 100%;
  height: 160rpx;
}

.related-info {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.related-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-instructor {
  font-size: 22rpx;
  color: #6b7280;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.related-duration {
  font-size: 20rpx;
  color: #9ca3af;
}

.related-rating {
  font-size: 20rpx;
  color: #f59e0b;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  z-index: 50;
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.price-label {
  font-size: 20rpx;
  color: #9ca3af;
}

.price-value {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-currency {
  font-size: 24rpx;
  color: #ef4444;
  font-weight: 500;
}

.price-amount {
  font-size: 36rpx;
  color: #ef4444;
  font-weight: bold;
}

.price-unit {
  font-size: 20rpx;
  color: #9ca3af;
}

.book-course-btn {
  flex: 1;
  height: 88rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
}

.book-course-btn:active {
  transform: scale(0.98);
  background: #5a67d8;
}

.book-course-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
  transform: none !important;
}

/* 预约弹窗 */
.booking-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.booking-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 0;
  position: sticky;
  top: 0;
  background: #ffffff;
  z-index: 10;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.booking-details {
  padding: 40rpx;
}

.course-summary {
  display: flex;
  gap: 20rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
}

.summary-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.summary-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.summary-instructor {
  font-size: 24rpx;
  color: #6b7280;
}

.summary-location {
  font-size: 24rpx;
  color: #9ca3af;
}

.booking-time {
  margin-bottom: 32rpx;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.time-item:last-child {
  border-bottom: none;
}

.time-label {
  font-size: 28rpx;
  color: #6b7280;
}

.time-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

.booking-notes {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #fef9c3;
  border-radius: 16rpx;
}

.notes-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 16rpx;
}

.notes-item {
  display: block;
  font-size: 24rpx;
  color: #92400e;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.payment-section {
  margin-bottom: 32rpx;
}

.payment-method {
  margin-bottom: 24rpx;
}

.payment-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.payment-option.selected {
  border-color: #667eea;
  background: #f0f9ff;
}

.payment-option:active {
  transform: scale(0.98);
}

.payment-icon {
  font-size: 32rpx;
}

.payment-text {
  flex: 1;
  font-size: 26rpx;
  color: #374151;
}

.payment-check {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

.price-breakdown {
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.price-desc {
  font-size: 26rpx;
  color: #6b7280;
}

.price-value {
  font-size: 26rpx;
  color: #1f2937;
  font-weight: 500;
}

.price-value.discount {
  color: #16a34a;
}

.price-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-top: 1rpx solid #e5e7eb;
  margin-top: 8rpx;
}

.total-desc {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.total-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ef4444;
}

.modal-actions {
  display: flex;
  gap: 24rpx;
  padding: 0 40rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #667eea;
  color: #ffffff;
}

.modal-btn.secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.modal-btn:active {
  transform: scale(0.98);
}

/* 响应式调整 */
@media (max-width: 400px) {
  .course-stats {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .schedule-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .schedule-date {
    align-self: flex-start;
  }
  
  .related-course {
    width: 200rpx;
  }
  
  .instructor-stats {
    flex-direction: column;
    gap: 16rpx;
  }
} 