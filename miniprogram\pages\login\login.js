const { createStore } = require('../../utils/store');
const { request } = require('../../utils/request');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 手机号登录
    phoneNumber: '',
    verifyCode: '',
    canGetCode: false,
    canLogin: false,
    codeButtonText: '获取验证码',
    codeTimer: null,
    codeCountdown: 60,
    
    // 用户协议
    agreedToTerms: false,
    showAgreementModal: false,
    agreementType: 'user', // 'user' | 'privacy'
    agreementModalTitle: '',
    
    // 加载状态
    isLoading: false,
    loadingText: '登录中...'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Login page loaded');
    
    // 检查是否已登录
    this.checkLoginStatus();
    
    // 检查是否有重定向参数
    if (options.redirect) {
      this.redirectUrl = decodeURIComponent(options.redirect);
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    try {
      const token = wx.getStorageSync('token');
      const userInfo = wx.getStorageSync('userInfo');
      
      if (token && userInfo) {
        // 已登录，直接跳转到主页
        this.navigateToHome();
      }
    } catch (error) {
      console.error('Failed to check login status:', error);
    }
  },

  /**
   * 手机号输入
   */
  onPhoneInput(e) {
    const phoneNumber = e.detail.value;
    this.setData({ phoneNumber });
    this.validateForm();
  },

  /**
   * 验证码输入
   */
  onCodeInput(e) {
    const verifyCode = e.detail.value;
    this.setData({ verifyCode });
    this.validateForm();
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { phoneNumber, verifyCode, agreedToTerms } = this.data;
    
    // 检查手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    const canGetCode = phoneRegex.test(phoneNumber) && agreedToTerms;
    
    // 检查是否可以登录
    const canLogin = canGetCode && verifyCode.length === 6;
    
    this.setData({ canGetCode, canLogin });
  },

  /**
   * 获取验证码
   */
  onGetCode() {
    if (!this.data.canGetCode) return;
    
    const { phoneNumber } = this.data;
    
    wx.showLoading({ title: '发送中...' });
    
    // 模拟发送验证码
    setTimeout(() => {
      wx.hideLoading();
      
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      });
      
      // 开始倒计时
      this.startCodeCountdown();
      
      // 在开发环境自动填入验证码
      if (wx.getSystemInfoSync().platform === 'devtools') {
        setTimeout(() => {
          this.setData({ verifyCode: '123456' });
          this.validateForm();
        }, 1000);
      }
    }, 1500);
  },

  /**
   * 开始验证码倒计时
   */
  startCodeCountdown() {
    this.setData({
      canGetCode: false,
      codeCountdown: 60,
      codeButtonText: '60s后重新获取'
    });
    
    this.data.codeTimer = setInterval(() => {
      const countdown = this.data.codeCountdown - 1;
      
      if (countdown <= 0) {
        this.stopCodeCountdown();
      } else {
        this.setData({
          codeCountdown: countdown,
          codeButtonText: `${countdown}s后重新获取`
        });
      }
    }, 1000);
  },

  /**
   * 停止验证码倒计时
   */
  stopCodeCountdown() {
    if (this.data.codeTimer) {
      clearInterval(this.data.codeTimer);
      this.setData({ codeTimer: null });
    }
    
    this.setData({
      codeButtonText: '重新获取',
      canGetCode: true
    });
    
    this.validateForm();
  },

  /**
   * 手机号登录
   */
  onPhoneLogin() {
    if (!this.data.canLogin) return;
    
    const { phoneNumber, verifyCode } = this.data;
    
    this.setData({
      isLoading: true,
      loadingText: '登录中...'
    });
    
    // 模拟登录请求
    setTimeout(() => {
      // 验证验证码
      if (verifyCode === '123456') {
        this.handleLoginSuccess({
          token: 'mock_token_' + Date.now(),
          userInfo: {
            id: '202312001',
            phone: phoneNumber,
            nickname: '健身用户',
            avatar: '/assets/images/default-avatar.png',
            memberLevel: '铜牌',
            isVip: false
          }
        });
      } else {
        this.setData({ isLoading: false });
        wx.showToast({
          title: '验证码错误',
          icon: 'error'
        });
      }
    }, 2000);
  },

  /**
   * 微信登录
   */
  onWechatLogin(e) {
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      });
      return;
    }
    
    const { userInfo } = e.detail;
    
    if (!userInfo) {
      wx.showToast({
        title: '授权失败，请重试',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      isLoading: true,
      loadingText: '微信登录中...'
    });
    
    // 获取微信登录code
    wx.login({
      success: (res) => {
        if (res.code) {
          this.performWechatLogin(res.code, userInfo);
        } else {
          this.setData({ isLoading: false });
          wx.showToast({
            title: '获取授权码失败',
            icon: 'error'
          });
        }
      },
      fail: () => {
        this.setData({ isLoading: false });
        wx.showToast({
          title: '微信登录失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 执行微信登录
   */
  performWechatLogin(code, userInfo) {
    // 模拟微信登录
    setTimeout(() => {
      this.handleLoginSuccess({
        token: 'wechat_token_' + Date.now(),
        userInfo: {
          id: '202312002',
          openid: 'mock_openid_' + Date.now(),
          nickname: userInfo.nickName || 'FitFocus用户',
          avatar: userInfo.avatarUrl || '/assets/images/default-avatar.png',
          gender: userInfo.gender || 0,
          memberLevel: '铜牌',
          isVip: false,
          loginType: 'wechat'
        }
      });
    }, 2000);
  },

  /**
   * 处理登录成功
   */
  handleLoginSuccess(loginData) {
    const { token, userInfo } = loginData;
    
    try {
      // 保存登录信息
      wx.setStorageSync('token', token);
      wx.setStorageSync('userInfo', userInfo);
      
      // 更新store
      store.setState({ 
        user: userInfo,
        isLoggedIn: true 
      });
      
      this.setData({ isLoading: false });
      
      wx.showToast({
        title: '登录成功！',
        icon: 'success',
        duration: 1500
      });
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.navigateToHome();
      }, 1500);
      
    } catch (error) {
      console.error('Failed to save login data:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'error'
      });
    }
  },

  /**
   * 游客登录
   */
  onVisitorLogin() {
    wx.showModal({
      title: '游客体验',
      content: '游客模式下部分功能将受限，建议您注册登录以获得完整体验。',
      confirmText: '继续体验',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.handleLoginSuccess({
            token: 'visitor_token_' + Date.now(),
            userInfo: {
              id: 'visitor_' + Date.now(),
              nickname: '游客用户',
              avatar: '/assets/images/visitor-avatar.png',
              memberLevel: '游客',
              isVip: false,
              isVisitor: true
            }
          });
        }
      }
    });
  },

  /**
   * 跳转到主页
   */
  navigateToHome() {
    const url = this.redirectUrl || '/pages/home/<USER>';
    
    if (this.redirectUrl) {
      wx.redirectTo({ url });
    } else {
      wx.switchTab({ url: '/pages/home/<USER>' });
    }
  },

  /**
   * 用户协议相关
   */
  onAgreementChange(e) {
    const agreedToTerms = e.detail.value.length > 0;
    this.setData({ agreedToTerms });
    this.validateForm();
  },

  onViewUserAgreement() {
    this.setData({
      showAgreementModal: true,
      agreementType: 'user',
      agreementModalTitle: '用户协议'
    });
  },

  onViewPrivacyPolicy() {
    this.setData({
      showAgreementModal: true,
      agreementType: 'privacy',
      agreementModalTitle: '隐私政策'
    });
  },

  onCloseAgreementModal() {
    this.setData({ showAgreementModal: false });
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    this.stopCodeCountdown();
  },

  /**
   * 页面隐藏时暂停定时器
   */
  onHide() {
    if (this.data.codeTimer) {
      clearInterval(this.data.codeTimer);
    }
  },

  /**
   * 页面显示时恢复定时器
   */
  onShow() {
    // 如果有倒计时在进行，恢复定时器
    if (this.data.codeCountdown > 0 && this.data.codeCountdown < 60 && !this.data.codeTimer) {
      this.startCodeCountdown();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'FitFocus - 专注健身，精彩生活',
      path: '/pages/login/login',
      imageUrl: '/assets/images/login-share.jpg'
    };
  }
}); 