package com.gym.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gym.entity.EquipmentInfo;
import com.gym.entity.EquipmentUsageLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface EquipmentService extends IService<EquipmentInfo> {

    /**
     * 根据设备编号获取设备信息
     */
    EquipmentInfo getEquipmentByCode(String equipmentCode);

    /**
     * 根据二维码获取设备信息
     */
    EquipmentInfo getEquipmentByQrCode(String qrCode);

    /**
     * 根据类型获取设备列表
     */
    List<EquipmentInfo> getEquipmentsByType(Integer equipmentType);

    /**
     * 获取可用设备列表
     */
    List<EquipmentInfo> getAvailableEquipments();

    /**
     * 根据房间获取设备列表
     */
    List<EquipmentInfo> getEquipmentsByRoom(Long roomId);

    /**
     * 扫码启动设备
     */
    boolean startEquipment(Long userId, String qrCode);

    /**
     * 结束使用设备
     */
    boolean endEquipment(Long userId, Long usageLogId);

    /**
     * 获取用户正在使用的设备
     */
    EquipmentUsageLog getUserInProgressEquipment(Long userId);

    /**
     * 获取设备当前使用状态
     */
    EquipmentUsageLog getEquipmentCurrentUsage(Long equipmentId);

    /**
     * 获取用户使用记录
     */
    List<EquipmentUsageLog> getUserUsageHistory(Long userId);

    /**
     * 获取设备使用记录
     */
    List<EquipmentUsageLog> getEquipmentUsageHistory(Long equipmentId);

    /**
     * 统计用户使用时长
     */
    Integer getUserUsageDuration(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计设备使用次数
     */
    Integer getEquipmentUsageCount(Long equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 更新设备状态
     */
    boolean updateEquipmentStatus(Long equipmentId, Integer status);

    /**
     * 获取需要维护的设备
     */
    List<EquipmentInfo> getNeedMaintenanceEquipments();
} 