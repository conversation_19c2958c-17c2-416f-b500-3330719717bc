const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 课程信息
    course: {
      id: 'course_001',
      name: '活力瑜伽晨练',
      subtitle: '唤醒身体活力，开启美好一天',
      coverImage: '/assets/images/yoga-class.jpg',
      category: '瑜伽',
      level: 'beginner',
      levelText: '初级',
      duration: 60,
      currentParticipants: 8,
      maxParticipants: 15,
      rating: 4.8,
      ratingStars: 5,
      reviewCount: 156,
      price: 88,
      canBook: true,
      disabledReason: '',
      isFavorite: false,
      description: '专为初学者设计的瑜伽课程，通过温和的体式练习，帮助您缓解身体紧张，提升柔韧性和平衡感。在宁静的环境中，配合呼吸调节，让身心得到完全的放松和恢复。',
      highlights: [
        '适合零基础学员',
        '专业认证教练授课',
        '小班制个性化指导',
        '提供瑜伽垫及道具',
        '包含冥想放松环节'
      ],
      instructor: {
        id: 'instructor_001',
        name: '张雅琪',
        title: '国际认证瑜伽导师',
        avatar: '/assets/images/instructor-yoga.jpg',
        experience: 8,
        rating: 4.9
      },
      reviews: [
        {
          id: 1,
          userName: '瑜伽爱好者',
          avatar: '/assets/images/user1.jpg',
          rating: 5,
          date: '2024-01-15',
          content: '张老师的课程非常棒！动作讲解详细，特别关注每个学员的状态，课后整个人都感觉很放松。',
          images: []
        },
        {
          id: 2,
          userName: '健身小白',
          avatar: '/assets/images/user2.jpg',
          rating: 5,
          date: '2024-01-10',
          content: '第一次上瑜伽课，老师很耐心地纠正我的动作，环境也很舒适，会继续坚持练习的！',
          images: ['/assets/images/review1.jpg']
        }
      ]
    },
    
    // 课程安排
    scheduleFilter: 'week',
    courseSchedule: [
      {
        id: 'schedule_001',
        date: '15',
        month: '01月',
        weekday: '周三',
        fullDate: '2024年1月15日',
        startTime: '09:00',
        endTime: '10:00',
        location: '瑜伽教室A',
        participants: 8,
        maxParticipants: 15,
        participantAvatars: [
          '/assets/images/user1.jpg',
          '/assets/images/user2.jpg',
          '/assets/images/user3.jpg'
        ],
        isBooked: false,
        isFull: false,
        isPast: false,
        status: 'available'
      },
      {
        id: 'schedule_002',
        date: '16',
        month: '01月',
        weekday: '周四',
        fullDate: '2024年1月16日',
        startTime: '09:00',
        endTime: '10:00',
        location: '瑜伽教室A',
        participants: 12,
        maxParticipants: 15,
        participantAvatars: [
          '/assets/images/user1.jpg',
          '/assets/images/user2.jpg',
          '/assets/images/user3.jpg',
          '/assets/images/user4.jpg'
        ],
        isBooked: true,
        isFull: false,
        isPast: false,
        status: 'booked'
      },
      {
        id: 'schedule_003',
        date: '17',
        month: '01月',
        weekday: '周五',
        fullDate: '2024年1月17日',
        startTime: '09:00',
        endTime: '10:00',
        location: '瑜伽教室A',
        participants: 15,
        maxParticipants: 15,
        participantAvatars: [
          '/assets/images/user1.jpg',
          '/assets/images/user2.jpg',
          '/assets/images/user3.jpg',
          '/assets/images/user4.jpg',
          '/assets/images/user5.jpg'
        ],
        isBooked: false,
        isFull: true,
        isPast: false,
        status: 'full'
      }
    ],
    filteredSchedule: [],
    
    // 相关推荐
    relatedCourses: [
      {
        id: 'related_001',
        name: '力量普拉提',
        instructor: '李教练',
        duration: 45,
        rating: 4.7,
        coverImage: '/assets/images/pilates-class.jpg'
      },
      {
        id: 'related_002', 
        name: '舒缓冥想',
        instructor: '王教练',
        duration: 30,
        rating: 4.9,
        coverImage: '/assets/images/meditation-class.jpg'
      },
      {
        id: 'related_003',
        name: '高温瑜伽',
        instructor: '陈教练',
        duration: 75,
        rating: 4.6,
        coverImage: '/assets/images/hot-yoga-class.jpg'
      }
    ],
    
    // 预约相关
    showBookingModal: false,
    selectedSchedule: null,
    paymentMethod: 'wechat',
    userPoints: 2500,
    hasDiscount: true,
    discountAmount: 18,
    finalPrice: 70
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Course detail page loaded');
    
    // 获取课程ID
    const courseId = options.id || 'course_001';
    
    // 加载课程数据
    this.loadCourseData(courseId);
    this.initScheduleFilter();
    this.loadUserInfo();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新预约状态
    this.refreshBookingStatus();
  },

  /**
   * 初始化页面数据
   */
  loadCourseData(courseId) {
    // 模拟从API加载课程数据
    console.log('Loading course data for:', courseId);
    
    // 检查是否已收藏
    this.checkFavoriteStatus();
    
    // 计算费用
    this.calculatePrice();
  },

  loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        // 检查是否为首次体验用户
        const hasDiscount = !userInfo.hasUsedTrialDiscount;
        this.setData({ hasDiscount });
        this.calculatePrice();
      }
    } catch (error) {
      console.error('Failed to load user info:', error);
    }
  },

  /**
   * 课程安排筛选
   */
  initScheduleFilter() {
    this.setData({ scheduleFilter: 'week' });
    this.updateFilteredSchedule();
  },

  onFilterChange(e) {
    const { filter } = e.currentTarget.dataset;
    this.setData({ scheduleFilter: filter });
    this.updateFilteredSchedule();
  },

  updateFilteredSchedule() {
    const { scheduleFilter, courseSchedule } = this.data;
    
    let filteredSchedule = courseSchedule;
    
    if (scheduleFilter === 'week') {
      // 显示本周课程
      const now = new Date();
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
      const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6));
      
      filteredSchedule = courseSchedule.filter(schedule => {
        const scheduleDate = new Date(schedule.fullDate);
        return scheduleDate >= weekStart && scheduleDate <= weekEnd;
      });
    }
    
    this.setData({ filteredSchedule });
  },

  /**
   * 收藏相关
   */
  checkFavoriteStatus() {
    // 从缓存检查收藏状态
    try {
      const favorites = wx.getStorageSync('favoriteCourses') || [];
      const isFavorite = favorites.includes(this.data.course.id);
      this.setData({ 
        'course.isFavorite': isFavorite 
      });
    } catch (error) {
      console.error('Failed to check favorite status:', error);
    }
  },

  onToggleFavorite() {
    const { course } = this.data;
    const newFavoriteStatus = !course.isFavorite;
    
    this.setData({ 
      'course.isFavorite': newFavoriteStatus 
    });
    
    // 更新缓存
    try {
      const favorites = wx.getStorageSync('favoriteCourses') || [];
      if (newFavoriteStatus) {
        if (!favorites.includes(course.id)) {
          favorites.push(course.id);
        }
      } else {
        const index = favorites.indexOf(course.id);
        if (index > -1) {
          favorites.splice(index, 1);
        }
      }
      wx.setStorageSync('favoriteCourses', favorites);
      
      wx.showToast({
        title: newFavoriteStatus ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    } catch (error) {
      console.error('Failed to update favorites:', error);
    }
  },

  /**
   * 课程预约
   */
  onScheduleItemTap(e) {
    const { schedule } = e.currentTarget.dataset;
    
    if (schedule.isPast) {
      wx.showToast({
        title: '课程已结束',
        icon: 'none'
      });
      return;
    }
    
    if (schedule.isFull && !schedule.isBooked) {
      wx.showToast({
        title: '课程已满员',
        icon: 'none'
      });
      return;
    }
    
    // 显示详细信息或预约选项
    this.showScheduleOptions(schedule);
  },

  showScheduleOptions(schedule) {
    const actions = [];
    
    if (schedule.isBooked) {
      actions.push('查看预约详情', '取消预约');
    } else if (!schedule.isFull) {
      actions.push('立即预约');
    }
    
    actions.push('课程详情');
    
    wx.showActionSheet({
      itemList: actions,
      success: (res) => {
        const action = actions[res.tapIndex];
        
        switch (action) {
          case '立即预约':
            this.openBookingModal(schedule);
            break;
          case '查看预约详情':
            this.viewBookingDetail(schedule);
            break;
          case '取消预约':
            this.cancelBooking(schedule);
            break;
          case '课程详情':
            this.viewScheduleDetail(schedule);
            break;
        }
      }
    });
  },

  onBookSchedule(e) {
    const { schedule } = e.currentTarget.dataset;
    this.openBookingModal(schedule);
  },

  openBookingModal(schedule) {
    this.setData({
      selectedSchedule: schedule,
      showBookingModal: true
    });
  },

  onCloseBookingModal() {
    this.setData({
      showBookingModal: false,
      selectedSchedule: null
    });
  },

  onBookCourse() {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '预约课程需要登录账户',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login?redirect=' + encodeURIComponent(getCurrentPages().pop().route)
            });
          }
        }
      });
      return;
    }
    
    // 选择第一个可用的课程安排
    const availableSchedule = this.data.filteredSchedule.find(
      schedule => !schedule.isBooked && !schedule.isFull && !schedule.isPast
    );
    
    if (availableSchedule) {
      this.openBookingModal(availableSchedule);
    } else {
      wx.showToast({
        title: '暂无可预约的课程',
        icon: 'none'
      });
    }
  },

  /**
   * 支付相关
   */
  calculatePrice() {
    const { course, hasDiscount } = this.data;
    let finalPrice = course.price;
    let discountAmount = 0;
    
    if (hasDiscount) {
      discountAmount = Math.round(course.price * 0.2); // 8折优惠
      finalPrice = course.price - discountAmount;
    }
    
    this.setData({
      discountAmount,
      finalPrice
    });
  },

  onSelectPayment(e) {
    const { method } = e.currentTarget.dataset;
    this.setData({ paymentMethod: method });
  },

  onConfirmBooking() {
    const { selectedSchedule, paymentMethod, finalPrice } = this.data;
    
    if (!selectedSchedule) return;
    
    wx.showLoading({ title: '处理中...' });
    
    // 模拟预约处理
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟支付
      if (paymentMethod === 'wechat') {
        this.processWechatPayment();
      } else {
        this.processPointsPayment();
      }
    }, 2000);
  },

  processWechatPayment() {
    // 模拟微信支付
    wx.requestPayment({
      timeStamp: Date.now().toString(),
      nonceStr: 'booking_' + Date.now(),
      package: 'prepay_id=mock_prepay_id',
      signType: 'MD5',
      paySign: 'mock_pay_sign',
      success: () => {
        this.onPaymentSuccess();
      },
      fail: () => {
        wx.showToast({
          title: '支付失败',
          icon: 'error'
        });
      }
    });
  },

  processPointsPayment() {
    const { finalPrice, userPoints } = this.data;
    const requiredPoints = finalPrice * 10; // 1元 = 10积分
    
    if (userPoints < requiredPoints) {
      wx.showToast({
        title: '积分不足',
        icon: 'none'
      });
      return;
    }
    
    // 模拟积分扣除
    setTimeout(() => {
      this.onPaymentSuccess();
    }, 1500);
  },

  onPaymentSuccess() {
    const { selectedSchedule } = this.data;
    
    // 更新预约状态
    const updatedSchedule = this.data.courseSchedule.map(schedule => {
      if (schedule.id === selectedSchedule.id) {
        return {
          ...schedule,
          isBooked: true,
          participants: schedule.participants + 1,
          status: 'booked'
        };
      }
      return schedule;
    });
    
    this.setData({
      courseSchedule: updatedSchedule,
      showBookingModal: false
    });
    
    this.updateFilteredSchedule();
    
    // 显示成功提示
    wx.showModal({
      title: '预约成功！',
      content: `您已成功预约 ${selectedSchedule.fullDate} ${selectedSchedule.startTime}-${selectedSchedule.endTime} 的课程，请准时参加。`,
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新store中的课程预约
        store.addCourseBooking({
          courseId: this.data.course.id,
          courseName: this.data.course.name,
          scheduleId: selectedSchedule.id,
          date: selectedSchedule.fullDate,
          time: `${selectedSchedule.startTime}-${selectedSchedule.endTime}`,
          location: selectedSchedule.location,
          instructor: this.data.course.instructor.name
        });
      }
    });
  },

  /**
   * 其他功能
   */
  cancelBooking(schedule) {
    wx.showModal({
      title: '取消预约',
      content: '确定要取消这节课的预约吗？取消后课程费用将原路退回。',
      success: (res) => {
        if (res.confirm) {
          this.performCancelBooking(schedule);
        }
      }
    });
  },

  performCancelBooking(schedule) {
    wx.showLoading({ title: '处理中...' });
    
    // 模拟取消预约
    setTimeout(() => {
      wx.hideLoading();
      
      // 更新预约状态
      const updatedSchedule = this.data.courseSchedule.map(s => {
        if (s.id === schedule.id) {
          return {
            ...s,
            isBooked: false,
            participants: Math.max(0, s.participants - 1),
            status: 'available'
          };
        }
        return s;
      });
      
      this.setData({ courseSchedule: updatedSchedule });
      this.updateFilteredSchedule();
      
      wx.showToast({
        title: '已取消预约',
        icon: 'success'
      });
    }, 1500);
  },

  refreshBookingStatus() {
    // 从store获取最新的预约状态
    const bookings = store.getState().courses?.bookings || [];
    
    // 更新课程安排中的预约状态
    const updatedSchedule = this.data.courseSchedule.map(schedule => {
      const booking = bookings.find(b => b.scheduleId === schedule.id);
      if (booking) {
        return {
          ...schedule,
          isBooked: true,
          status: 'booked'
        };
      }
      return schedule;
    });
    
    this.setData({ courseSchedule: updatedSchedule });
    this.updateFilteredSchedule();
  },

  onViewInstructor() {
    const { instructor } = this.data.course;
    wx.navigateTo({
      url: `/pages/instructor-detail/instructor-detail?id=${instructor.id}`
    });
  },

  onPreviewReviewImage(e) {
    const { images, current } = e.currentTarget.dataset;
    wx.previewImage({
      urls: images,
      current: current
    });
  },

  onViewAllReviews() {
    wx.navigateTo({
      url: `/pages/course-reviews/course-reviews?courseId=${this.data.course.id}`
    });
  },

  onRelatedCourseTap(e) {
    const { course } = e.currentTarget.dataset;
    wx.redirectTo({
      url: `/pages/course-detail/course-detail?id=${course.id}`
    });
  },

  onShareCourse() {
    const { course } = this.data;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  viewBookingDetail(schedule) {
    wx.navigateTo({
      url: `/pages/booking-detail/booking-detail?scheduleId=${schedule.id}`
    });
  },

  viewScheduleDetail(schedule) {
    wx.showModal({
      title: '课程安排详情',
      content: `时间：${schedule.fullDate} ${schedule.startTime}-${schedule.endTime}\n地点：${schedule.location}\n人数：${schedule.participants}/${schedule.maxParticipants}`,
      showCancel: false
    });
  },

  onBack() {
    wx.navigateBack();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadCourseData(this.data.course.id);
    this.refreshBookingStatus();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { course } = this.data;
    
    return {
      title: `${course.name} - 专业健身课程`,
      path: `/pages/course-detail/course-detail?id=${course.id}`,
      imageUrl: course.coverImage
    };
  }
}); 