package com.gym.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gym.entity.CommunityPost;
import com.gym.entity.FitnessCheckin;

import java.time.LocalDate;
import java.util.List;

/**
 * 社区服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface CommunityService extends IService<CommunityPost> {

    /**
     * 分页获取社区动态
     */
    IPage<CommunityPost> getCommunityPosts(Page<CommunityPost> page, Integer postType, String keyword, Long topicId);

    /**
     * 发布动态
     */
    boolean publishPost(CommunityPost post);

    /**
     * 获取用户动态
     */
    List<CommunityPost> getUserPosts(Long userId);

    /**
     * 获取热门动态
     */
    List<CommunityPost> getHotPosts(Integer limit);

    /**
     * 获取置顶动态
     */
    List<CommunityPost> getTopPosts();

    /**
     * 点赞/取消点赞动态
     */
    boolean toggleLikePost(Long userId, Long postId);

    /**
     * 分享动态
     */
    boolean sharePost(Long userId, Long postId);

    /**
     * 增加浏览量
     */
    boolean incrementViewCount(Long postId);

    /**
     * 健身打卡
     */
    boolean fitnessCheckin(FitnessCheckin checkin);

    /**
     * 获取用户打卡记录
     */
    List<FitnessCheckin> getUserCheckins(Long userId);

    /**
     * 获取用户今日打卡
     */
    FitnessCheckin getTodayCheckin(Long userId);

    /**
     * 获取用户连续打卡天数
     */
    Integer getContinuousCheckinDays(Long userId);

    /**
     * 获取公开打卡记录
     */
    List<FitnessCheckin> getPublicCheckins(Integer limit);

    /**
     * 根据类型获取打卡记录
     */
    List<FitnessCheckin> getCheckinsByType(Integer checkinType, Integer limit);

    /**
     * 点赞/取消点赞打卡
     */
    boolean toggleLikeCheckin(Long userId, Long checkinId);

    /**
     * 删除动态
     */
    boolean deletePost(Long userId, Long postId);

    /**
     * 获取用户打卡统计
     */
    Object getCheckinStats(Long userId, LocalDate startDate, LocalDate endDate);
} 