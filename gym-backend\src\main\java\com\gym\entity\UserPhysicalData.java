package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户体征数据实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("user_physical_data")
public class UserPhysicalData implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("height")
    private BigDecimal height;

    @TableField("weight")
    private BigDecimal weight;

    @TableField("body_fat_rate")
    private BigDecimal bodyFatRate;

    @TableField("muscle_mass")
    private BigDecimal muscleMass;

    @TableField("bmi")
    private BigDecimal bmi;

    @TableField("visceral_fat")
    private Integer visceralFat;

    @TableField("body_water")
    private BigDecimal bodyWater;

    @TableField("bone_mass")
    private BigDecimal boneMass;

    @TableField("measurement_date")
    private LocalDate measurementDate;

    @TableField("data_source")
    private Integer dataSource;

    @TableField("device_type")
    private String deviceType;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 计算BMI指数
     */
    public BigDecimal calculateBMI() {
        if (height != null && weight != null && height.compareTo(BigDecimal.ZERO) > 0) {
            // BMI = 体重(kg) / (身高(m))^2
            BigDecimal heightInMeters = height.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            return weight.divide(heightInMeters.multiply(heightInMeters), 2, BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    /**
     * 获取BMI状态
     */
    public String getBMIStatus() {
        BigDecimal calculatedBMI = bmi != null ? bmi : calculateBMI();
        if (calculatedBMI == null) {
            return "无数据";
        }
        
        if (calculatedBMI.compareTo(BigDecimal.valueOf(18.5)) < 0) {
            return "偏瘦";
        } else if (calculatedBMI.compareTo(BigDecimal.valueOf(24)) < 0) {
            return "正常";
        } else if (calculatedBMI.compareTo(BigDecimal.valueOf(28)) < 0) {
            return "超重";
        } else {
            return "肥胖";
        }
    }

    /**
     * 获取体脂率状态
     */
    public String getBodyFatStatus() {
        if (bodyFatRate == null) {
            return "无数据";
        }
        
        // 这里简化处理，实际应该根据性别和年龄来判断
        if (bodyFatRate.compareTo(BigDecimal.valueOf(10)) < 0) {
            return "过低";
        } else if (bodyFatRate.compareTo(BigDecimal.valueOf(20)) < 0) {
            return "正常";
        } else if (bodyFatRate.compareTo(BigDecimal.valueOf(25)) < 0) {
            return "偏高";
        } else {
            return "过高";
        }
    }

    /**
     * 获取数据来源名称
     */
    public String getDataSourceName() {
        if (dataSource == null) {
            return "手动录入";
        }
        switch (dataSource) {
            case 1: return "手动录入";
            case 2: return "智能设备";
            default: return "手动录入";
        }
    }

    /**
     * 获取内脂等级描述
     */
    public String getVisceralFatLevel() {
        if (visceralFat == null) {
            return "无数据";
        }
        if (visceralFat <= 9) {
            return "标准";
        } else if (visceralFat <= 14) {
            return "偏高";
        } else {
            return "过高";
        }
    }
}