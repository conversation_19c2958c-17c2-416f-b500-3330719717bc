package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程预约实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("course_booking")
public class CourseBooking implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("schedule_id")
    private Long scheduleId;

    @TableField("user_id")
    private Long userId;

    @TableField("booking_time")
    private LocalDateTime bookingTime;

    @TableField("booking_status")
    private Integer bookingStatus;

    @TableField("cancel_time")
    private LocalDateTime cancelTime;

    @TableField("cancel_reason")
    private String cancelReason;

    @TableField("check_in_time")
    private LocalDateTime checkInTime;

    @TableField("actual_fee")
    private BigDecimal actualFee;

    @TableField("payment_status")
    private Integer paymentStatus;

    @TableField("payment_time")
    private LocalDateTime paymentTime;

    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取预约状态名称
     */
    public String getBookingStatusName() {
        if (bookingStatus == null) {
            return "已预约";
        }
        switch (bookingStatus) {
            case 1: return "已预约";
            case 2: return "已签到";
            case 3: return "已完成";
            case 4: return "已取消";
            case 5: return "缺席";
            default: return "已预约";
        }
    }

    /**
     * 获取支付状态名称
     */
    public String getPaymentStatusName() {
        if (paymentStatus == null) {
            return "未支付";
        }
        switch (paymentStatus) {
            case 0: return "未支付";
            case 1: return "已支付";
            case 2: return "已退款";
            default: return "未支付";
        }
    }

    /**
     * 判断是否可以签到
     */
    public boolean canCheckIn() {
        return bookingStatus != null && bookingStatus == 1 && checkInTime == null;
    }

    /**
     * 判断是否可以取消
     */
    public boolean canCancel() {
        return bookingStatus != null && (bookingStatus == 1 || bookingStatus == 2) 
               && cancelTime == null;
    }

    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return bookingStatus != null && bookingStatus == 3;
    }

    /**
     * 判断是否需要支付
     */
    public boolean needPayment() {
        return paymentStatus != null && paymentStatus == 0 && actualFee != null 
               && actualFee.compareTo(BigDecimal.ZERO) > 0;
    }
} 