<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG to PNG 转换器 - 微信小程序图标</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .converter {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .icon-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        .icon-display {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .icon-info {
            flex: 1;
        }
        .icon-name {
            font-weight: 600;
            margin-bottom: 4px;
        }
        .icon-size {
            font-size: 14px;
            color: #6b7280;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #5a67d8;
        }
        .btn.secondary {
            background: #e5e7eb;
            color: #374151;
        }
        .btn.secondary:hover {
            background: #d1d5db;
        }
        .canvas-container {
            display: none;
        }
        .instructions {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }
        .progress {
            margin-top: 20px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 微信小程序图标转换工具</h1>
        <p>将 SVG 图标转换为 PNG 格式，用于微信小程序 tabBar</p>
    </div>

    <div class="converter">
        <!-- Home Icons -->
        <div class="icon-card">
            <div class="icon-preview">
                <div class="icon-display" id="home-preview"></div>
                <div class="icon-info">
                    <div class="icon-name">Home 主页</div>
                    <div class="icon-size">24x24 → 48x48 PNG</div>
                </div>
            </div>
            <button class="btn" onclick="convertIcon('home', false)">下载 home.png</button>
            <button class="btn secondary" onclick="convertIcon('home', true)" style="margin-top: 8px;">下载 home-active.png</button>
        </div>

        <!-- Dumbbell Icons -->
        <div class="icon-card">
            <div class="icon-preview">
                <div class="icon-display" id="dumbbell-preview"></div>
                <div class="icon-info">
                    <div class="icon-name">Dumbbell 训练</div>
                    <div class="icon-size">24x24 → 48x48 PNG</div>
                </div>
            </div>
            <button class="btn" onclick="convertIcon('dumbbell', false)">下载 dumbbell.png</button>
            <button class="btn secondary" onclick="convertIcon('dumbbell', true)" style="margin-top: 8px;">下载 dumbbell-active.png</button>
        </div>

        <!-- Chart Icons -->
        <div class="icon-card">
            <div class="icon-preview">
                <div class="icon-display" id="chart-preview"></div>
                <div class="icon-info">
                    <div class="icon-name">Chart 数据</div>
                    <div class="icon-size">24x24 → 48x48 PNG</div>
                </div>
            </div>
            <button class="btn" onclick="convertIcon('chart', false)">下载 chart.png</button>
            <button class="btn secondary" onclick="convertIcon('chart', true)" style="margin-top: 8px;">下载 chart-active.png</button>
        </div>

        <!-- Users Icons -->
        <div class="icon-card">
            <div class="icon-preview">
                <div class="icon-display" id="users-preview"></div>
                <div class="icon-info">
                    <div class="icon-name">Users 社区</div>
                    <div class="icon-size">24x24 → 48x48 PNG</div>
                </div>
            </div>
            <button class="btn" onclick="convertIcon('users', false)">下载 users.png</button>
            <button class="btn secondary" onclick="convertIcon('users', true)" style="margin-top: 8px;">下载 users-active.png</button>
        </div>

        <!-- User Icons -->
        <div class="icon-card">
            <div class="icon-preview">
                <div class="icon-display" id="user-preview"></div>
                <div class="icon-info">
                    <div class="icon-name">User 我的</div>
                    <div class="icon-size">24x24 → 48x48 PNG</div>
                </div>
            </div>
            <button class="btn" onclick="convertIcon('user', false)">下载 user.png</button>
            <button class="btn secondary" onclick="convertIcon('user', true)" style="margin-top: 8px;">下载 user-active.png</button>
        </div>

        <!-- Batch Download -->
        <div class="icon-card" style="grid-column: 1 / -1;">
            <div class="icon-info" style="text-align: center; margin-bottom: 20px;">
                <div class="icon-name">批量下载</div>
                <div class="icon-size">一键下载所有 PNG 图标</div>
            </div>
            <button class="btn" onclick="downloadAllIcons()">📦 下载全部图标</button>
        </div>
    </div>

    <div class="instructions">
        <h2>📋 使用说明</h2>
        <div class="step">
            <div class="step-number">1</div>
            <div>
                <strong>点击下载按钮</strong><br>
                点击上方图标卡片中的下载按钮，生成对应的 PNG 图标文件
            </div>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <div>
                <strong>保存到正确位置</strong><br>
                将下载的 PNG 文件保存到项目的 <code>miniprogram/assets/icons/</code> 目录中
            </div>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <div>
                <strong>重新编译项目</strong><br>
                在微信开发者工具中重新编译项目，tabBar 图标应该正常显示
            </div>
        </div>
        <div class="step">
            <div class="step-number">4</div>
            <div>
                <strong>验证结果</strong><br>
                检查微信开发者工具是否还有图标缺失的错误提示
            </div>
        </div>

        <div class="progress" id="progress" style="display: none;">
            <div>正在生成图标...</div>
        </div>
    </div>

    <div class="canvas-container">
        <canvas id="canvas" width="48" height="48"></canvas>
    </div>

    <script>
        // SVG 图标数据
        const iconSvgs = {
            home: {
                normal: '<svg viewBox="0 0 576 512" xmlns="http://www.w3.org/2000/svg"><path fill="#9ca3af" d="M575.8 255.5c0 18-15 32.1-32 32.1h-32l.7 160.2c0 2.7-.2 5.4-.5 8.1V472c0 22.1-17.9 40-40 40H456c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1H416 392c-22.1 0-40-17.9-40-40V448 384c0-17.7-14.3-32-32-32H256c-17.7 0-32 14.3-32 32v64 24c0 22.1-17.9 40-40 40H160 128.1c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2H104c-22.1 0-40-17.9-40-40V360c0-.9 0-1.9 .1-2.8V287.6H32c-18 0-32.1-14-32.1-32.1c0-9 3.8-17.6 10.4-23.7l432-405.2c6.4-6.1 14.7-9.7 23.5-9.7s17.1 3.6 23.5 9.7l432 405.2c6.6 6.2 10.4 14.8 10.4 23.7z"/></svg>',
                active: '<svg viewBox="0 0 576 512" xmlns="http://www.w3.org/2000/svg"><path fill="#667eea" d="M575.8 255.5c0 18-15 32.1-32 32.1h-32l.7 160.2c0 2.7-.2 5.4-.5 8.1V472c0 22.1-17.9 40-40 40H456c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1H416 392c-22.1 0-40-17.9-40-40V448 384c0-17.7-14.3-32-32-32H256c-17.7 0-32 14.3-32 32v64 24c0 22.1-17.9 40-40 40H160 128.1c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2H104c-22.1 0-40-17.9-40-40V360c0-.9 0-1.9 .1-2.8V287.6H32c-18 0-32.1-14-32.1-32.1c0-9 3.8-17.6 10.4-23.7l432-405.2c6.4-6.1 14.7-9.7 23.5-9.7s17.1 3.6 23.5 9.7l432 405.2c6.6 6.2 10.4 14.8 10.4 23.7z"/></svg>'
            },
            dumbbell: {
                normal: '<svg viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg"><path fill="#9ca3af" d="M112 96c0-17.7 14.3-32 32-32h16c17.7 0 32 14.3 32 32V224v64V416c0 17.7-14.3 32-32 32H144c-17.7 0-32-14.3-32-32V384H64c-17.7 0-32-14.3-32-32V288c0-17.7 14.3-32 32-32h48V224 96zm224 0c0-17.7 14.3-32 32-32h16c17.7 0 32 14.3 32 32V224v32H528c17.7 0 32 14.3 32 32v64c0 17.7-14.3 32-32 32H416v32 96c0 17.7-14.3 32-32 32H368c-17.7 0-32-14.3-32-32V416 288 224 96z"/></svg>',
                active: '<svg viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg"><path fill="#667eea" d="M112 96c0-17.7 14.3-32 32-32h16c17.7 0 32 14.3 32 32V224v64V416c0 17.7-14.3 32-32 32H144c-17.7 0-32-14.3-32-32V384H64c-17.7 0-32-14.3-32-32V288c0-17.7 14.3-32 32-32h48V224 96zm224 0c0-17.7 14.3-32 32-32h16c17.7 0 32 14.3 32 32V224v32H528c17.7 0 32 14.3 32 32v64c0 17.7-14.3 32-32 32H416v32 96c0 17.7-14.3 32-32 32H368c-17.7 0-32-14.3-32-32V416 288 224 96z"/></svg>'
            },
            chart: {
                normal: '<svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path fill="#9ca3af" d="M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64V400c0 44.2 35.8 80 80 80H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H80c-8.8 0-16-7.2-16-16V64zm406.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L320 210.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L240 221.3l57.4 57.4c12.5 12.5 32.8 12.5 45.3 0l128-128z"/></svg>',
                active: '<svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg"><path fill="#667eea" d="M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64V400c0 44.2 35.8 80 80 80H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H80c-8.8 0-16-7.2-16-16V64zm406.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L320 210.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L240 221.3l57.4 57.4c12.5 12.5 32.8 12.5 45.3 0l128-128z"/></svg>'
            },
            users: {
                normal: '<svg viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg"><path fill="#9ca3af" d="M144 0a80 80 0 1 1 0 160A80 80 0 1 1 144 0zM512 0a80 80 0 1 1 0 160A80 80 0 1 1 512 0zM0 298.7C0 239.8 47.8 192 106.7 192h42.7c15.9 0 31 3.5 44.6 9.7c-1.3 7.2-1.9 14.7-1.9 22.3c0 38.2 16.8 72.5 43.3 96c-.2 0-.4 0-.7 0H21.3C9.6 320 0 310.4 0 298.7zM405.3 320c-.2 0-.4 0-.7 0c26.6-23.5 43.3-57.8 43.3-96c0-7.6-.7-15-1.9-22.3c13.6-6.3 28.7-9.7 44.6-9.7h42.7C592.2 192 640 239.8 640 298.7c0 11.8-9.6 21.3-21.3 21.3H405.3zM224 224a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zM128 485.3C128 411.7 187.7 352 261.3 352H378.7C452.3 352 512 411.7 512 485.3c0 14.7-11.9 26.7-26.7 26.7H154.7c-14.7 0-26.7-11.9-26.7-26.7z"/></svg>',
                active: '<svg viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg"><path fill="#667eea" d="M144 0a80 80 0 1 1 0 160A80 80 0 1 1 144 0zM512 0a80 80 0 1 1 0 160A80 80 0 1 1 512 0zM0 298.7C0 239.8 47.8 192 106.7 192h42.7c15.9 0 31 3.5 44.6 9.7c-1.3 7.2-1.9 14.7-1.9 22.3c0 38.2 16.8 72.5 43.3 96c-.2 0-.4 0-.7 0H21.3C9.6 320 0 310.4 0 298.7zM405.3 320c-.2 0-.4 0-.7 0c26.6-23.5 43.3-57.8 43.3-96c0-7.6-.7-15-1.9-22.3c13.6-6.3 28.7-9.7 44.6-9.7h42.7C592.2 192 640 239.8 640 298.7c0 11.8-9.6 21.3-21.3 21.3H405.3zM224 224a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zM128 485.3C128 411.7 187.7 352 261.3 352H378.7C452.3 352 512 411.7 512 485.3c0 14.7-11.9 26.7-26.7 26.7H154.7c-14.7 0-26.7-11.9-26.7-26.7z"/></svg>'
            },
            user: {
                normal: '<svg viewBox="0 0 448 512" xmlns="http://www.w3.org/2000/svg"><path fill="#9ca3af" d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"/></svg>',
                active: '<svg viewBox="0 0 448 512" xmlns="http://www.w3.org/2000/svg"><path fill="#667eea" d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"/></svg>'
            }
        };

        // 初始化预览
        function initPreviews() {
            Object.keys(iconSvgs).forEach(iconName => {
                const previewEl = document.getElementById(`${iconName}-preview`);
                if (previewEl) {
                    previewEl.innerHTML = iconSvgs[iconName].normal;
                }
            });
        }

        // 转换单个图标
        function convertIcon(iconName, isActive) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            const svg = iconSvgs[iconName][isActive ? 'active' : 'normal'];
            
            // 清空画布
            ctx.clearRect(0, 0, 48, 48);
            
            // 创建图片元素
            const img = new Image();
            const svgBlob = new Blob([svg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, 48, 48);
                
                // 转换为PNG并下载
                canvas.toBlob(function(blob) {
                    const fileName = `${iconName}${isActive ? '-active' : ''}.png`;
                    downloadBlob(blob, fileName);
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };
            
            img.src = url;
        }

        // 下载所有图标
        function downloadAllIcons() {
            const progress = document.getElementById('progress');
            progress.style.display = 'block';
            
            const iconNames = Object.keys(iconSvgs);
            let completed = 0;
            const total = iconNames.length * 2; // 每个图标有两个版本
            
            iconNames.forEach(iconName => {
                // 下载普通版本
                setTimeout(() => {
                    convertIcon(iconName, false);
                    completed++;
                    updateProgress(completed, total);
                }, completed * 200);
                
                // 下载激活版本
                setTimeout(() => {
                    convertIcon(iconName, true);
                    completed++;
                    updateProgress(completed, total);
                }, (completed + 1) * 200);
            });
        }

        function updateProgress(completed, total) {
            const progress = document.getElementById('progress');
            progress.innerHTML = `<div>正在生成图标... ${completed}/${total}</div>`;
            
            if (completed === total) {
                setTimeout(() => {
                    progress.innerHTML = '<div>✅ 所有图标已生成完成！</div>';
                    setTimeout(() => {
                        progress.style.display = 'none';
                    }, 2000);
                }, 500);
            }
        }

        // 下载blob文件
        function downloadBlob(blob, fileName) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPreviews);
    </script>
</body>
</html> 