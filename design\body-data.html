<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 体测记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .metric-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .chart-container {
            height: 200px;
            width: 100%;
        }
        .floating-add-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
            z-index: 50;
        }
    </style>
</head>
<body class="bg-gray-50 h-screen overflow-hidden">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 overflow-y-auto" style="height: calc(100vh - 44px);">
        <!-- Header -->
        <div class="gradient-bg px-6 py-6 text-white">
            <div class="flex items-center justify-between mb-6">
                <button class="w-10 h-10 bg-white/20 backdrop-blur-lg rounded-full flex items-center justify-center">
                    <i class="fas fa-arrow-left text-white"></i>
                </button>
                <h1 class="text-xl font-semibold">体测记录</h1>
                <button class="w-10 h-10 bg-white/20 backdrop-blur-lg rounded-full flex items-center justify-center">
                    <i class="fas fa-calendar text-white"></i>
                </button>
            </div>

            <!-- Current Stats -->
            <div class="text-center mb-6">
                <div class="text-3xl font-bold mb-2">70.2 kg</div>
                <div class="text-white/80 text-sm mb-4">当前体重</div>
                <div class="flex items-center justify-center text-sm">
                    <i class="fas fa-arrow-down text-green-400 mr-1"></i>
                    <span class="text-green-400 font-medium">-0.5kg</span>
                    <span class="text-white/80 ml-2">较上次测量</span>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="grid grid-cols-3 gap-3">
                <div class="metric-card rounded-xl p-3 text-center">
                    <div class="text-lg font-bold text-gray-800 mb-1">15.8%</div>
                    <div class="text-gray-600 text-xs">体脂率</div>
                </div>
                <div class="metric-card rounded-xl p-3 text-center">
                    <div class="text-lg font-bold text-gray-800 mb-1">55.4kg</div>
                    <div class="text-gray-600 text-xs">肌肉量</div>
                </div>
                <div class="metric-card rounded-xl p-3 text-center">
                    <div class="text-lg font-bold text-gray-800 mb-1">22.1</div>
                    <div class="text-gray-600 text-xs">BMI</div>
                </div>
            </div>
        </div>

        <div class="px-6 pt-6">
            <!-- Weight Trend Chart -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">体重趋势</h3>
                    <div class="flex space-x-2">
                        <button class="bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-xs font-medium">1个月</button>
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs font-medium">3个月</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="weightChart"></canvas>
                </div>
            </div>

            <!-- Body Composition -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">身体成分分析</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                            <span class="text-gray-700 font-medium">体脂率</span>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold text-gray-900">15.8%</div>
                            <div class="text-green-600 text-xs flex items-center">
                                <i class="fas fa-arrow-down mr-1"></i>-1.2%
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-gray-700 font-medium">肌肉量</span>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold text-gray-900">55.4kg</div>
                            <div class="text-green-600 text-xs flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i>+0.3kg
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                            <span class="text-gray-700 font-medium">骨量</span>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold text-gray-900">2.8kg</div>
                            <div class="text-gray-500 text-xs">正常</div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-cyan-500 rounded-full mr-3"></div>
                            <span class="text-gray-700 font-medium">体水分</span>
                        </div>
                        <div class="text-right">
                            <div class="font-semibold text-gray-900">58.2%</div>
                            <div class="text-gray-500 text-xs">正常</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Health Score -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">健康评分</h3>
                <div class="flex items-center justify-between mb-4">
                    <div class="flex-1 mr-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-700 text-sm">综合健康</span>
                            <span class="font-semibold text-green-600">85分</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                    <div class="text-4xl">😊</div>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-lg font-bold text-green-600 mb-1">A</div>
                        <div class="text-gray-600 text-xs">体重控制</div>
                    </div>
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <div class="text-lg font-bold text-blue-600 mb-1">B+</div>
                        <div class="text-gray-600 text-xs">肌肉发展</div>
                    </div>
                </div>
            </div>

            <!-- Recent Records -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">测量记录</h3>
                    <button class="text-indigo-600 text-sm font-medium">查看全部</button>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900 text-sm">今天测量</div>
                            <div class="text-gray-500 text-xs">09:30 · 手动录入</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">70.2kg</div>
                            <div class="text-green-600 text-xs">-0.5kg</div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900 text-sm">1周前</div>
                            <div class="text-gray-500 text-xs">08:45 · 智能体脂秤</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">70.7kg</div>
                            <div class="text-red-600 text-xs">+0.2kg</div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900 text-sm">2周前</div>
                            <div class="text-gray-500 text-xs">09:15 · 智能体脂秤</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">70.5kg</div>
                            <div class="text-green-600 text-xs">-0.3kg</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-6 border border-blue-100">
                <div class="flex items-start">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 mt-1">
                        <i class="fas fa-lightbulb text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-2">健身建议</h4>
                        <p class="text-gray-700 text-sm mb-3">
                            恭喜！您的体重控制得很好。建议继续保持当前的运动频率，可以适当增加力量训练来提升肌肉量。
                        </p>
                        <div class="space-y-2">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                <span>每周3-4次力量训练</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                <span>保持充足的蛋白质摄入</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Add Button -->
    <button class="floating-add-btn flex items-center justify-center">
        <i class="fas fa-plus text-white text-xl"></i>
    </button>

    <!-- Add Record Modal (Hidden by default) -->
    <div id="addModal" class="fixed inset-0 bg-black/50 flex items-end justify-center hidden">
        <div class="bg-white rounded-t-2xl p-6 w-full max-w-sm">
            <div class="w-8 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6 text-center">添加体测记录</h3>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">体重 (kg)</label>
                    <input type="number" class="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="70.2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">体脂率 (%)</label>
                    <input type="number" class="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="15.8">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">肌肉量 (kg)</label>
                    <input type="number" class="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="55.4">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">测量时间</label>
                    <input type="datetime-local" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                </div>
            </div>
            
            <div class="flex space-x-3 mt-6">
                <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-lg font-medium">取消</button>
                <button class="flex-1 bg-indigo-600 text-white py-3 rounded-lg font-medium">保存</button>
            </div>
        </div>
    </div>

    <script>
        // Weight Chart
        const ctx = document.getElementById('weightChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],
                datasets: [{
                    label: '体重(kg)',
                    data: [71.2, 70.8, 70.5, 70.7, 70.2],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 70,
                        max: 72,
                        grid: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Show add modal
        document.querySelector('.floating-add-btn').addEventListener('click', () => {
            document.getElementById('addModal').classList.remove('hidden');
        });

        // Hide add modal
        document.querySelector('#addModal .bg-gray-100').addEventListener('click', () => {
            document.getElementById('addModal').classList.add('hidden');
        });
    </script>
</body>
</html> 