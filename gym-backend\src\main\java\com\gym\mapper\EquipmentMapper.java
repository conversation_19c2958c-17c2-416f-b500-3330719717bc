package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.EquipmentInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备信息 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface EquipmentMapper extends BaseMapper<EquipmentInfo> {

    /**
     * 根据设备编号查询设备
     */
    EquipmentInfo selectByEquipmentCode(@Param("equipmentCode") String equipmentCode);

    /**
     * 根据设备类型查询设备列表
     */
    List<EquipmentInfo> selectByType(@Param("equipmentType") Integer equipmentType);

    /**
     * 查询可用设备列表
     */
    List<EquipmentInfo> selectAvailableEquipments();

    /**
     * 根据房间查询设备
     */
    List<EquipmentInfo> selectByRoomId(@Param("roomId") Long roomId);

    /**
     * 更新设备状态
     */
    int updateStatus(@Param("equipmentId") Long equipmentId, @Param("status") Integer status);

    /**
     * 查询需要维护的设备
     */
    List<EquipmentInfo> selectNeedMaintenance();

    /**
     * 根据二维码查询设备
     */
    EquipmentInfo selectByQrCode(@Param("qrCode") String qrCode);
} 