<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 社区</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 83px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 10px;
            font-weight: 500;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-icon {
            font-size: 22px;
            margin-bottom: 4px;
        }
        .post-image {
            border-radius: 12px;
            overflow: hidden;
        }
        .post-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            border-radius: 12px;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-50 h-screen overflow-hidden">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 overflow-y-auto" style="height: calc(100vh - 127px);">
        <!-- Header -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">健身社区</h1>
                <button class="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-plus text-white"></i>
                </button>
            </div>
            
            <!-- Category Tabs -->
            <div class="flex space-x-4 mt-4">
                <button class="bg-indigo-600 text-white px-4 py-2 rounded-full text-sm font-medium">推荐</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">关注</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">打卡</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">健身</button>
            </div>
        </div>

        <div class="px-6 pt-4">
            <!-- Featured Story -->
            <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-6 mb-6 text-white relative overflow-hidden">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -mr-12 -mt-12"></div>
                <div class="relative z-10">
                    <h3 class="text-lg font-bold mb-2">健身挑战赛</h3>
                    <p class="text-white/90 text-sm mb-4">30天健身打卡挑战，赢取专属徽章和奖品</p>
                    <button class="bg-white text-purple-600 px-4 py-2 rounded-full font-medium text-sm">
                        立即参与
                    </button>
                </div>
            </div>

            <!-- Posts Feed -->
            <div class="space-y-6">
                <!-- Post 1 -->
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-center mb-3">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b77c?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover mr-3">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <span class="font-semibold text-gray-900 text-sm">健身达人小李</span>
                                <span class="ml-2 bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full text-xs font-medium">VIP</span>
                            </div>
                            <div class="text-gray-500 text-xs">2小时前</div>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                    
                    <p class="text-gray-800 text-sm mb-3">
                        今天完成了3个番茄钟的力量训练！感觉整个人都充满了能量 💪 
                        #健身打卡 #力量训练 #番茄钟健身
                    </p>
                    
                    <div class="post-image mb-3">
                        <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=200&fit=crop" 
                             class="w-full h-48 object-cover">
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-heart text-red-500"></i>
                                <span class="text-sm">128</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm">24</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-share"></i>
                                <span class="text-sm">8</span>
                            </button>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-bookmark"></i>
                        </button>
                    </div>
                </div>

                <!-- Post 2 -->
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-center mb-3">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover mr-3">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <span class="font-semibold text-gray-900 text-sm">健身教练王磊</span>
                                <span class="ml-2 bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full text-xs font-medium">教练</span>
                            </div>
                            <div class="text-gray-500 text-xs">4小时前</div>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                    
                    <p class="text-gray-800 text-sm mb-3">
                        分享一些健康饮食搭配，蛋白质+碳水化合物+蔬菜的黄金比例 🥗
                        健身三分练七分吃，营养搭配很重要！
                    </p>
                    
                    <div class="post-grid mb-3">
                        <img src="https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=200&h=200&fit=crop" 
                             class="w-full h-24 object-cover">
                        <img src="https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=200&h=200&fit=crop" 
                             class="w-full h-24 object-cover">
                        <img src="https://images.unsplash.com/photo-1547496614-67c7af348ac3?w=200&h=200&fit=crop" 
                             class="w-full h-24 object-cover">
                        <img src="https://images.unsplash.com/photo-1455619452474-d2be8b1e70cd?w=200&h=200&fit=crop" 
                             class="w-full h-24 object-cover">
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="far fa-heart"></i>
                                <span class="text-sm">89</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm">15</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-share"></i>
                                <span class="text-sm">12</span>
                            </button>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-bookmark"></i>
                        </button>
                    </div>
                </div>

                <!-- Post 3 - Check-in -->
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-center mb-3">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover mr-3">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <span class="font-semibold text-gray-900 text-sm">瑜伽爱好者小美</span>
                                <span class="ml-2 bg-green-100 text-green-600 px-2 py-0.5 rounded-full text-xs font-medium">7天</span>
                            </div>
                            <div class="text-gray-500 text-xs">6小时前 · 瑜伽室A</div>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                    
                    <div class="bg-gradient-to-r from-green-400 to-blue-500 rounded-xl p-4 mb-3 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-lg font-bold">连续打卡 7 天</div>
                                <div class="text-white/80 text-sm">瑜伽训练 · 45分钟</div>
                            </div>
                            <div class="text-3xl">🧘‍♀️</div>
                        </div>
                        <div class="mt-3 flex items-center space-x-2">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                            <span class="text-white/80 text-xs">坚持就是胜利！</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-heart text-red-500"></i>
                                <span class="text-sm">56</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm">8</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-share"></i>
                                <span class="text-sm">3</span>
                            </button>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-bookmark"></i>
                        </button>
                    </div>
                </div>

                <!-- Post 4 -->
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-center mb-3">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover mr-3">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <span class="font-semibold text-gray-900 text-sm">跑步达人小张</span>
                                <span class="ml-2 bg-yellow-100 text-yellow-600 px-2 py-0.5 rounded-full text-xs font-medium">21天</span>
                            </div>
                            <div class="text-gray-500 text-xs">1天前</div>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                    
                    <p class="text-gray-800 text-sm mb-3">
                        晨跑5公里完成！今天的配速比昨天快了30秒 🏃‍♂️
                        坚持番茄钟训练法真的很有效果！
                    </p>
                    
                    <div class="bg-blue-50 rounded-xl p-4 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-running text-blue-600 mr-2"></i>
                                <span class="font-semibold text-gray-900">晨跑记录</span>
                            </div>
                            <span class="text-blue-600 text-sm font-medium">5.2 km</span>
                        </div>
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-lg font-bold text-gray-900">26:40</div>
                                <div class="text-gray-500 text-xs">用时</div>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-gray-900">5:08</div>
                                <div class="text-gray-500 text-xs">配速</div>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-gray-900">340</div>
                                <div class="text-gray-500 text-xs">卡路里</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="far fa-heart"></i>
                                <span class="text-sm">42</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm">6</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-share"></i>
                                <span class="text-sm">2</span>
                            </button>
                        </div>
                        <button class="text-gray-400">
                            <i class="fas fa-bookmark"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span>主页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-dumbbell tab-icon"></i>
            <span>训练</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span>数据</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-users tab-icon"></i>
            <span>社区</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 