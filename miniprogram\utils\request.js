/**
 * 网络请求工具类
 * 基于微信小程序wx.request封装
 */

const app = getApp()

// 请求基础配置
const config = {
  baseURL: 'https://api.fitfocus.com',
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}

/**
 * 网络请求主方法
 * @param {Object} options 请求配置
 */
export function request(options = {}) {
  return new Promise((resolve, reject) => {
    // 显示loading
    if (options.loading !== false) {
      wx.showLoading({
        title: options.loadingText || '加载中...',
        mask: true
      })
    }

    // 构建请求URL
    const url = options.url.startsWith('http') 
      ? options.url 
      : `${config.baseURL}${options.url}`

    // 构建请求头
    const header = {
      ...config.header,
      ...options.header
    }

    // 添加授权token
    if (app.globalData.token) {
      header['Authorization'] = `Bearer ${app.globalData.token}`
    }

    // 发起请求
    wx.request({
      url,
      method: options.method || 'GET',
      data: options.data || {},
      header,
      timeout: options.timeout || config.timeout,
      success(res) {
        console.log(`API请求成功 [${options.method || 'GET'}] ${url}:`, res.data)
        
        // 隐藏loading
        if (options.loading !== false) {
          wx.hideLoading()
        }

        // 处理响应
        if (res.statusCode === 200) {
          const responseData = res.data
          
          // 统一响应格式 {code, message, data}
          if (responseData.code === 200) {
            resolve(responseData)
          } else {
            // 业务错误处理
            handleBusinessError(responseData, options)
            reject(responseData)
          }
        } else {
          // HTTP状态错误
          const error = {
            code: res.statusCode,
            message: getHttpErrorMessage(res.statusCode),
            data: null
          }
          handleHttpError(error, options)
          reject(error)
        }
      },
      fail(error) {
        console.error(`API请求失败 [${options.method || 'GET'}] ${url}:`, error)
        
        // 隐藏loading
        if (options.loading !== false) {
          wx.hideLoading()
        }

        // 网络错误处理
        const networkError = {
          code: -1,
          message: getNetworkErrorMessage(error.errMsg),
          data: null
        }
        handleNetworkError(networkError, options)
        reject(networkError)
      }
    })
  })
}

/**
 * 处理业务错误
 */
function handleBusinessError(error, options) {
  if (options.silent) return

  switch (error.code) {
    case 401:
      // 未授权，清除登录状态
      wx.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      setTimeout(() => {
        app.logout()
      }, 1500)
      break
    case 403:
      wx.showToast({
        title: '没有访问权限',
        icon: 'none'
      })
      break
    case 404:
      wx.showToast({
        title: '请求的资源不存在',
        icon: 'none'
      })
      break
    default:
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      })
  }
}

/**
 * 处理HTTP状态错误
 */
function handleHttpError(error, options) {
  if (options.silent) return
  
  wx.showToast({
    title: error.message,
    icon: 'none'
  })
}

/**
 * 处理网络错误
 */
function handleNetworkError(error, options) {
  if (options.silent) return
  
  wx.showToast({
    title: error.message,
    icon: 'none'
  })
}

/**
 * 获取HTTP错误信息
 */
function getHttpErrorMessage(statusCode) {
  const messages = {
    400: '请求参数错误',
    401: '未授权访问',
    403: '禁止访问',
    404: '请求资源不存在',
    405: '请求方法不允许',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  }
  return messages[statusCode] || `请求失败 (${statusCode})`
}

/**
 * 获取网络错误信息
 */
function getNetworkErrorMessage(errMsg) {
  if (errMsg.includes('timeout')) {
    return '请求超时，请检查网络连接'
  }
  if (errMsg.includes('fail')) {
    return '网络连接失败，请检查网络设置'
  }
  return '网络异常，请稍后重试'
}

// GET请求
export function get(url, params = {}, options = {}) {
  const queryString = Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&')
  
  const requestUrl = queryString ? `${url}?${queryString}` : url
  
  return request({
    url: requestUrl,
    method: 'GET',
    ...options
  })
}

// POST请求
export function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
export function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
export function del(url, options = {}) {
  return request({
    url,
    method: 'DELETE',
    ...options
  })
}

// 文件上传
export function uploadFile(filePath, url, formData = {}, options = {}) {
  return new Promise((resolve, reject) => {
    if (options.loading !== false) {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })
    }

    wx.uploadFile({
      url: url.startsWith('http') ? url : `${config.baseURL}${url}`,
      filePath,
      name: 'file',
      formData,
      header: {
        'Authorization': app.globalData.token ? `Bearer ${app.globalData.token}` : ''
      },
      success(res) {
        if (options.loading !== false) {
          wx.hideLoading()
        }

        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            resolve(data)
          } else {
            reject(data)
          }
        } catch (error) {
          reject({
            code: -1,
            message: '上传失败',
            data: null
          })
        }
      },
      fail(error) {
        if (options.loading !== false) {
          wx.hideLoading()
        }
        reject({
          code: -1,
          message: '上传失败',
          data: error
        })
      }
    })
  })
}

export default {
  request,
  get,
  post,
  put,
  del,
  uploadFile
} 