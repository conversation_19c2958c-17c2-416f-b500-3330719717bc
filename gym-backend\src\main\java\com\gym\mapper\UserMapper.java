package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.UserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户信息 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface UserMapper extends BaseMapper<UserInfo> {

    /**
     * 根据openid查询用户
     */
    UserInfo selectByOpenid(@Param("openid") String openid);

    /**
     * 根据手机号查询用户
     */
    UserInfo selectByPhone(@Param("phone") String phone);

    /**
     * 更新用户最后登录时间
     */
    int updateLastLoginTime(@Param("userId") Long userId);

    /**
     * 更新用户积分
     */
    int updateUserPoints(@Param("userId") Long userId, 
                        @Param("totalPoints") Integer totalPoints, 
                        @Param("availablePoints") Integer availablePoints);
} 