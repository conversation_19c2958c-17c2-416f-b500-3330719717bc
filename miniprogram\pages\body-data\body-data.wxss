/* 身体数据页面样式 */
.container {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 32rpx 20rpx;
  margin-top: var(--status-bar-height, 44px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1rpx solid #e5e7eb;
}

.back-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.back-btn:active {
  transform: scale(0.9);
  background: #e5e7eb;
}

.back-icon {
  color: #374151;
  font-size: 32rpx;
  font-weight: bold;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.action-btns {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.action-icon {
  font-size: 32rpx;
}

/* 数据概览 */
.data-overview {
  margin-top: 160rpx;
  padding: 32rpx;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.overview-date {
  font-size: 24rpx;
  color: #6b7280;
}

.data-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.data-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.data-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.data-card.weight::before {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.data-card.bmi::before {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.data-card.body-fat::before {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
}

.data-card.muscle::before {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.card-icon {
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.card-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
  line-height: 1;
}

.card-unit {
  font-size: 24rpx;
  color: #6b7280;
  align-self: flex-start;
  margin-top: -8rpx;
  margin-left: 4rpx;
}

.card-change {
  font-size: 20rpx;
  font-weight: 500;
  margin-top: 8rpx;
}

.card-change.positive {
  color: #16a34a;
}

.card-change.negative {
  color: #dc2626;
}

.card-status {
  font-size: 20rpx;
  font-weight: 500;
  margin-top: 8rpx;
}

.card-status.normal {
  color: #16a34a;
}

.card-status.overweight {
  color: #f59e0b;
}

.card-status.underweight {
  color: #3b82f6;
}

.card-label {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 8rpx;
}

/* 趋势图表 */
.chart-section {
  margin: 32rpx 32rpx 48rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.chart-filter {
  display: flex;
}

.filter-tabs {
  display: flex;
  background: #f3f4f6;
  border-radius: 12rpx;
  padding: 4rpx;
}

.filter-tab {
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.filter-tab.active {
  background: #667eea;
}

.tab-text {
  font-size: 24rpx;
  color: #6b7280;
}

.filter-tab.active .tab-text {
  color: #ffffff;
}

.chart-types {
  display: flex;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.chart-type {
  padding: 12rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.chart-type.active {
  border-color: #667eea;
  background: #f0f9ff;
}

.chart-type:active {
  transform: scale(0.95);
}

.type-text {
  font-size: 24rpx;
  color: #6b7280;
}

.chart-type.active .type-text {
  color: #667eea;
}

.chart-container {
  position: relative;
  margin-bottom: 24rpx;
}

.trend-chart {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
}

.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s ease;
  z-index: 10;
}

.chart-tooltip.show {
  opacity: 1;
}

.tooltip-date {
  display: block;
  margin-bottom: 4rpx;
}

.tooltip-value {
  display: block;
  font-weight: bold;
}

.chart-summary {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.summary-item {
  text-align: center;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
}

.summary-label {
  display: block;
  font-size: 20rpx;
  color: #9ca3af;
  margin-bottom: 8rpx;
}

.summary-value {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #1f2937;
}

.summary-value.positive {
  color: #16a34a;
}

.summary-value.negative {
  color: #dc2626;
}

/* 目标设置 */
.goals-section {
  margin: 0 32rpx 48rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.edit-goals-btn {
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.edit-goals-btn:active {
  background: #e5e7eb;
}

.edit-text {
  font-size: 24rpx;
  color: #667eea;
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.goal-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
}

.goal-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.goal-label {
  font-size: 24rpx;
  color: #6b7280;
}

.goal-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.goal-progress {
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-width: 160rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #6b7280;
  font-weight: 500;
  min-width: 48rpx;
  text-align: right;
}

/* 历史记录 */
.history-section {
  margin: 0 32rpx 48rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.view-all-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.view-all-btn:active {
  background: #e5e7eb;
}

.view-all-text {
  font-size: 24rpx;
  color: #667eea;
}

.view-all-arrow {
  font-size: 20rpx;
  color: #667eea;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.history-item:active {
  transform: translateY(2rpx);
  background: #f3f4f6;
}

.history-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
  padding: 12rpx;
  background: #ffffff;
  border-radius: 12rpx;
}

.date-day {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  line-height: 1;
}

.date-month {
  font-size: 20rpx;
  color: #9ca3af;
  margin-top: 4rpx;
}

.history-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.data-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.data-label {
  font-size: 24rpx;
  color: #6b7280;
  min-width: 80rpx;
}

.data-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
  min-width: 100rpx;
}

.data-change {
  font-size: 20rpx;
  font-weight: 500;
}

.data-change.positive {
  color: #16a34a;
}

.data-change.negative {
  color: #dc2626;
}

.history-arrow {
  padding: 8rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #9ca3af;
}

.empty-history {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #9ca3af;
  margin-bottom: 32rpx;
}

.add-data-btn {
  padding: 20rpx 40rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 20rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.2s ease;
}

.add-data-btn:active {
  transform: scale(0.95);
  background: #5a67d8;
}

.btn-text {
  color: #ffffff;
}

/* 智能分析 */
.analysis-section {
  margin: 0 32rpx 48rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.refresh-analysis-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.refresh-analysis-btn:active {
  transform: rotate(180deg);
  background: #e5e7eb;
}

.refresh-icon {
  font-size: 24rpx;
}

.analysis-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.analysis-card {
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border-left: 6rpx solid #667eea;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.card-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.card-status.positive {
  background: #f0fdf4;
  color: #16a34a;
}

.card-status.warning {
  background: #fef3c7;
  color: #d97706;
}

.card-status.info {
  background: #eff6ff;
  color: #2563eb;
}

.card-content {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 弹窗通用样式 */
.add-data-modal,
.goals-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.add-data-modal.show,
.goals-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 85vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 0;
  position: sticky;
  top: 0;
  background: #ffffff;
  z-index: 10;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.modal-body {
  padding: 40rpx;
}

.modal-actions {
  display: flex;
  gap: 24rpx;
  padding: 0 40rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #667eea;
  color: #ffffff;
}

.modal-btn.secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.modal-btn:active {
  transform: scale(0.98);
}

.modal-btn:disabled {
  opacity: 0.5;
  transform: none !important;
}

/* 添加数据弹窗特有样式 */
.input-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.input-label {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.data-input {
  height: 88rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.note-input {
  min-height: 120rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 0 24rpx;
}

.date-text {
  font-size: 28rpx;
  color: #1f2937;
}

.date-arrow {
  font-size: 24rpx;
  color: #9ca3af;
}

.calculated-info {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #f0f9ff;
  border-radius: 16rpx;
  border: 2rpx solid #e0f2fe;
}

.info-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #0369a1;
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: #0369a1;
}

.info-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #0c4a6e;
}

.info-status {
  font-size: 20rpx;
  font-weight: 500;
}

.info-status.normal {
  color: #16a34a;
}

.info-status.overweight {
  color: #f59e0b;
}

.info-status.underweight {
  color: #3b82f6;
}

/* 目标设置弹窗特有样式 */
.goals-inputs {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.goal-input-group {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.goal-label {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.goal-input {
  height: 88rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .data-cards {
    grid-template-columns: 1fr;
  }
  
  .chart-summary {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .data-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4rpx;
  }
  
  .data-label {
    min-width: auto;
  }
  
  .data-value {
    min-width: auto;
  }
} 