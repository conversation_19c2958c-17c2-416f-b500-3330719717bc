/* 训练页面样式 */
.container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 0 32rpx 120rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 0 32rpx;
  margin-top: var(--status-bar-height, 44px);
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: #5a67d8;
}

.btn-icon {
  color: #ffffff;
  font-size: 32rpx;
}

/* 当前训练状态 */
.current-workout {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  color: #ffffff;
}

.workout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.workout-title {
  font-size: 36rpx;
  font-weight: 600;
}

.workout-timer {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
}

.timer-text {
  font-size: 32rpx;
  font-weight: bold;
}

.workout-progress {
  margin-bottom: 32rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: #ffffff;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  opacity: 0.9;
}

.workout-actions {
  display: flex;
  gap: 24rpx;
}

.workout-actions .action-btn {
  flex: 1;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: #ffffff;
}

.workout-actions .action-btn.pause {
  background: rgba(251, 191, 36, 0.8);
}

.workout-actions .action-btn.stop {
  background: rgba(239, 68, 68, 0.8);
}

.btn-text {
  font-size: 24rpx;
}

/* 快速开始训练 */
.quick-start {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.view-all {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.view-all-text {
  font-size: 28rpx;
  color: #667eea;
}

.arrow {
  font-size: 24rpx;
  color: #667eea;
}

.workout-templates {
  white-space: nowrap;
}

.template-list {
  display: flex;
  gap: 24rpx;
  padding-bottom: 16rpx;
}

.template-card {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  min-width: 200rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.template-card:active {
  transform: translateY(4rpx);
}

.template-icon {
  font-size: 48rpx;
}

.template-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

.template-duration {
  font-size: 24rpx;
  color: #6b7280;
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 48rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

/* 今日训练计划 */
.today-plan {
  margin-bottom: 48rpx;
}

.edit-plan {
  background: #f3f4f6;
  border-radius: 16rpx;
  padding: 12rpx 24rpx;
}

.edit-text {
  font-size: 24rpx;
  color: #667eea;
}

.plan-list {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.plan-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f3f4f6;
  transition: all 0.2s ease;
}

.plan-item:last-child {
  border-bottom: none;
}

.plan-item:active {
  background: #f8fafc;
}

.plan-item.completed {
  opacity: 0.6;
}

.plan-indicator {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 4rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.plan-item.completed .plan-indicator {
  background: #10b981;
  border-color: #10b981;
}

.indicator-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

.plan-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.plan-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

.plan-item.completed .plan-name {
  text-decoration: line-through;
}

.plan-details {
  font-size: 24rpx;
  color: #6b7280;
}

.plan-action {
  flex-shrink: 0;
}

.action-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

.plan-item.completed .action-text {
  color: #10b981;
}

/* 训练历史 */
.workout-history {
  margin-bottom: 48rpx;
}

.filter-tabs {
  display: flex;
  gap: 8rpx;
}

.tab-item {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background: #f3f4f6;
  transition: all 0.2s ease;
}

.tab-item.active {
  background: #667eea;
}

.tab-text {
  font-size: 24rpx;
  color: #6b7280;
}

.tab-item.active .tab-text {
  color: #ffffff;
}

.history-list {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.history-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f3f4f6;
  transition: all 0.2s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #f8fafc;
}

.history-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.history-icon .icon {
  font-size: 32rpx;
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

.history-details {
  font-size: 24rpx;
  color: #6b7280;
}

.history-date {
  font-size: 24rpx;
  color: #9ca3af;
}

.history-rating {
  flex-shrink: 0;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 28rpx;
  color: #e5e7eb;
}

.star.filled {
  color: #fbbf24;
}

.load-more {
  text-align: center;
  padding: 32rpx;
  margin-top: 24rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #667eea;
}

/* 弹窗样式 */
.create-workout-modal,
.workout-complete-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.create-workout-modal.show,
.workout-complete-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.modal-body {
  padding: 40rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 0 24rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #1f2937;
}

.picker-arrow {
  font-size: 24rpx;
  color: #6b7280;
}

.modal-actions {
  display: flex;
  gap: 24rpx;
  padding: 0 40rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #667eea;
  color: #ffffff;
}

.modal-btn.secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.modal-btn:active {
  transform: scale(0.98);
}

/* 训练完成弹窗 */
.complete-header {
  text-align: center;
  padding: 40rpx 40rpx 32rpx;
}

.complete-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.complete-emoji {
  font-size: 80rpx;
}

.complete-stats {
  display: flex;
  justify-content: space-around;
  padding: 0 40rpx 32rpx;
}

.complete-stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-unit {
  font-size: 24rpx;
  color: #6b7280;
}

.rating-section {
  text-align: center;
  padding: 32rpx 40rpx;
  border-top: 2rpx solid #f3f4f6;
}

.rating-label {
  display: block;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 24rpx;
}

.rating-section .rating-stars {
  justify-content: center;
  gap: 16rpx;
}

.rating-section .star {
  font-size: 48rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rating-section .star:active {
  transform: scale(1.2);
} 