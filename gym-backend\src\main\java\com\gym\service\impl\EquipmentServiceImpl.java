package com.gym.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gym.entity.EquipmentInfo;
import com.gym.entity.EquipmentUsageLog;
import com.gym.mapper.EquipmentMapper;
import com.gym.mapper.EquipmentUsageLogMapper;
import com.gym.service.EquipmentService;
import com.gym.util.QRCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class EquipmentServiceImpl extends ServiceImpl<EquipmentMapper, EquipmentInfo> implements EquipmentService {

    @Autowired
    private EquipmentMapper equipmentMapper;

    @Autowired
    private EquipmentUsageLogMapper equipmentUsageLogMapper;

    @Override
    public EquipmentInfo getEquipmentByCode(String equipmentCode) {
        return equipmentMapper.selectByEquipmentCode(equipmentCode);
    }

    @Override
    public EquipmentInfo getEquipmentByQrCode(String qrCode) {
        return equipmentMapper.selectByQrCode(qrCode);
    }

    @Override
    public List<EquipmentInfo> getEquipmentsByType(Integer equipmentType) {
        return equipmentMapper.selectByType(equipmentType);
    }

    @Override
    public List<EquipmentInfo> getAvailableEquipments() {
        return equipmentMapper.selectAvailableEquipments();
    }

    @Override
    public List<EquipmentInfo> getEquipmentsByRoom(Long roomId) {
        return equipmentMapper.selectByRoomId(roomId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startEquipment(Long userId, String qrCode) {
        // 解析二维码
        String equipmentCode = QRCodeUtil.parseEquipmentCode(qrCode);
        if (equipmentCode == null) {
            throw new RuntimeException("无效的设备二维码");
        }

        // 验证二维码是否有效（5分钟有效期）
        if (!QRCodeUtil.isQRCodeValid(qrCode, 5 * 60 * 1000)) {
            throw new RuntimeException("二维码已过期");
        }

        // 查找设备
        EquipmentInfo equipment = equipmentMapper.selectByEquipmentCode(equipmentCode);
        if (equipment == null) {
            throw new RuntimeException("设备不存在");
        }

        // 检查设备状态
        if (!equipment.isAvailable()) {
            throw new RuntimeException("设备不可用: " + equipment.getStatusName());
        }

        // 检查用户是否已在使用其他设备
        EquipmentUsageLog userInProgress = equipmentUsageLogMapper.selectInProgressByUserId(userId);
        if (userInProgress != null) {
            throw new RuntimeException("您正在使用其他设备，请先结束使用");
        }

        // 检查设备是否被其他用户使用
        EquipmentUsageLog equipmentInProgress = equipmentUsageLogMapper.selectInProgressByEquipmentId(equipment.getId());
        if (equipmentInProgress != null) {
            throw new RuntimeException("设备正在被其他用户使用");
        }

        // 更新设备状态为使用中
        int updateResult = equipmentMapper.updateStatus(equipment.getId(), 2);
        if (updateResult == 0) {
            throw new RuntimeException("设备状态更新失败");
        }

        // 创建使用记录
        EquipmentUsageLog usageLog = new EquipmentUsageLog();
        usageLog.setUserId(userId);
        usageLog.setEquipmentId(equipment.getId());
        usageLog.setStartTime(LocalDateTime.now());
        usageLog.setStatus(1); // 正常使用

        int insertResult = equipmentUsageLogMapper.insert(usageLog);
        
        if (insertResult > 0) {
            log.info("用户 {} 成功启动设备 {}", userId, equipmentCode);
            return true;
        }
        
        throw new RuntimeException("启动设备失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean endEquipment(Long userId, Long usageLogId) {
        // 查找使用记录
        EquipmentUsageLog usageLog = equipmentUsageLogMapper.selectById(usageLogId);
        if (usageLog == null || !usageLog.getUserId().equals(userId)) {
            throw new RuntimeException("使用记录不存在");
        }

        if (usageLog.isCompleted()) {
            throw new RuntimeException("设备使用已结束");
        }

        // 计算使用时长
        LocalDateTime endTime = LocalDateTime.now();
        usageLog.setEndTime(endTime);
        usageLog.setDuration(usageLog.calculateDuration());

        // 更新使用记录
        int updateLogResult = equipmentUsageLogMapper.updateById(usageLog);
        
        // 更新设备状态为空闲
        int updateEquipmentResult = equipmentMapper.updateStatus(usageLog.getEquipmentId(), 1);
        
        if (updateLogResult > 0 && updateEquipmentResult > 0) {
            log.info("用户 {} 成功结束设备使用，使用时长: {} 分钟", userId, usageLog.getDuration());
            return true;
        }
        
        throw new RuntimeException("结束使用失败");
    }

    @Override
    public EquipmentUsageLog getUserInProgressEquipment(Long userId) {
        return equipmentUsageLogMapper.selectInProgressByUserId(userId);
    }

    @Override
    public EquipmentUsageLog getEquipmentCurrentUsage(Long equipmentId) {
        return equipmentUsageLogMapper.selectInProgressByEquipmentId(equipmentId);
    }

    @Override
    public List<EquipmentUsageLog> getUserUsageHistory(Long userId) {
        return equipmentUsageLogMapper.selectByUserId(userId);
    }

    @Override
    public List<EquipmentUsageLog> getEquipmentUsageHistory(Long equipmentId) {
        return equipmentUsageLogMapper.selectByEquipmentId(equipmentId);
    }

    @Override
    public Integer getUserUsageDuration(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        Integer duration = equipmentUsageLogMapper.sumDurationByUserId(userId, startTime, endTime);
        return duration != null ? duration : 0;
    }

    @Override
    public Integer getEquipmentUsageCount(Long equipmentId, LocalDateTime startTime, LocalDateTime endTime) {
        Integer count = equipmentUsageLogMapper.countUsageByEquipmentId(equipmentId, startTime, endTime);
        return count != null ? count : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEquipmentStatus(Long equipmentId, Integer status) {
        return equipmentMapper.updateStatus(equipmentId, status) > 0;
    }

    @Override
    public List<EquipmentInfo> getNeedMaintenanceEquipments() {
        return equipmentMapper.selectNeedMaintenance();
    }
} 