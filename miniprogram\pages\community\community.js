const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 导航标签
    navTabs: [
      { key: 'all', label: '全部', icon: '🏠' },
      { key: 'checkin', label: '打卡', icon: '✅' },
      { key: 'workout', label: '训练', icon: '💪' },
      { key: 'food', label: '饮食', icon: '🍎' }
    ],
    activeTab: 'all',
    
    // 用户统计
    userStats: {
      checkinStreak: 7,
      totalPosts: 25,
      followers: 128,
      following: 86
    },
    
    // 打卡类型
    checkinTypes: [
      { type: 'workout', name: '运动', icon: '🏃‍♂️', completed: true },
      { type: 'diet', name: '饮食', icon: '🥗', completed: false },
      { type: 'sleep', name: '作息', icon: '😴', completed: true },
      { type: 'water', name: '喝水', icon: '💧', completed: false }
    ],
    
    // 筛选选项
    filterOptions: [
      { label: '最新发布', value: 'latest' },
      { label: '最多点赞', value: 'popular' },
      { label: '仅关注', value: 'following' },
      { label: '附近的人', value: 'nearby' }
    ],
    currentFilter: { label: '最新发布', value: 'latest' },
    showFilterMenu: false,
    
    // 社区动态
    communityPosts: [
      {
        id: 1,
        type: 'workout',
        typeLabel: '运动打卡',
        user: {
          id: 101,
          name: '健身达人小王',
          avatar: '/assets/images/avatar1.jpg'
        },
        content: '今天完成了30分钟的HIIT训练，感觉超级棒！坚持就是胜利 💪',
        images: ['/assets/images/workout1.jpg'],
        workoutData: {
          type: 'HIIT',
          duration: '30分钟',
          calories: '350卡路里'
        },
        location: '健身房·力量区',
        timeAgo: '2小时前',
        likeCount: 24,
        commentCount: 8,
        isLiked: false,
        timestamp: Date.now() - 2 * 60 * 60 * 1000
      },
      {
        id: 2,
        type: 'food',
        typeLabel: '饮食分享',
        user: {
          id: 102,
          name: '营养师小美',
          avatar: '/assets/images/avatar2.jpg'
        },
        content: '今日健康早餐：牛油果吐司配水煮蛋，营养均衡又美味！',
        images: ['/assets/images/food1.jpg', '/assets/images/food2.jpg'],
        foodData: {
          name: '牛油果早餐',
          calories: 420
        },
        timeAgo: '4小时前',
        likeCount: 18,
        commentCount: 5,
        isLiked: true,
        timestamp: Date.now() - 4 * 60 * 60 * 1000
      },
      {
        id: 3,
        type: 'moment',
        typeLabel: '生活动态',
        user: {
          id: 103,
          name: '跑步爱好者',
          avatar: '/assets/images/avatar3.jpg'
        },
        content: '晨跑5公里完成！清晨的空气特别清新，新的一天充满活力！',
        images: ['/assets/images/run1.jpg'],
        location: '公园跑道',
        timeAgo: '6小时前',
        likeCount: 32,
        commentCount: 12,
        isLiked: false,
        timestamp: Date.now() - 6 * 60 * 60 * 1000
      },
      {
        id: 4,
        type: 'checkin',
        typeLabel: '每日打卡',
        user: {
          id: 104,
          name: '健康生活',
          avatar: '/assets/images/avatar4.jpg'
        },
        content: '第15天打卡！今天的目标全部完成，继续保持这个节奏 ✨',
        timeAgo: '1天前',
        likeCount: 15,
        commentCount: 3,
        isLiked: true,
        timestamp: Date.now() - 24 * 60 * 60 * 1000
      }
    ],
    
    filteredPosts: [],
    hasMorePosts: true,
    
    // 发布动态相关
    showPublishModal: false,
    postTypes: [
      { label: '运动', value: 'workout', icon: '💪' },
      { label: '饮食', value: 'food', icon: '🍎' },
      { label: '动态', value: 'moment', icon: '📝' }
    ],
    workoutTypes: [
      { label: '有氧运动', value: 'cardio' },
      { label: '力量训练', value: 'strength' },
      { label: 'HIIT', value: 'hiit' },
      { label: '瑜伽', value: 'yoga' },
      { label: '跑步', value: 'running' }
    ],
    newPost: {
      type: 'workout',
      content: '',
      images: [],
      workoutTypeIndex: 0,
      workoutData: {
        duration: '',
        calories: ''
      },
      location: ''
    },
    
    // 评论相关
    showCommentsModal: false,
    currentPostId: null,
    currentPostComments: [],
    newComment: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Community page loaded');
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新数据
    this.loadCommunityData();
    this.updateFilteredPosts();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 加载社区数据
    this.loadCommunityData();
    this.updateFilteredPosts();
    
    // 从store获取用户状态
    this.loadUserState();
  },

  /**
   * 加载用户状态
   */
  loadUserState() {
    const state = store.getState();
    if (state.community) {
      this.setData({
        userStats: state.community.userStats || this.data.userStats,
        checkinTypes: state.community.checkinTypes || this.data.checkinTypes
      });
    }
  },

  /**
   * 导航标签切换
   */
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset;
    this.setData({ activeTab: tab });
    this.updateFilteredPosts();
  },

  /**
   * 快速打卡
   */
  onQuickCheckin(e) {
    const { type } = e.currentTarget.dataset;
    const checkinTypes = [...this.data.checkinTypes];
    const targetIndex = checkinTypes.findIndex(item => item.type === type);
    
    if (targetIndex !== -1) {
      checkinTypes[targetIndex].completed = !checkinTypes[targetIndex].completed;
      this.setData({ checkinTypes });
      
      // 更新连续打卡天数
      const completedCount = checkinTypes.filter(item => item.completed).length;
      if (completedCount === checkinTypes.length) {
        this.setData({
          'userStats.checkinStreak': this.data.userStats.checkinStreak + 1
        });
        
        wx.showToast({
          title: '全部打卡完成！',
          icon: 'success'
        });
      }
      
      // 保存到store
      store.addCommunityPost({
        checkinTypes: checkinTypes,
        userStats: this.data.userStats
      });
      
      // 触觉反馈
      wx.vibrateShort();
    }
  },

  /**
   * 筛选功能
   */
  onShowFilterMenu() {
    this.setData({ showFilterMenu: true });
  },

  onHideFilterMenu() {
    this.setData({ showFilterMenu: false });
  },

  onSelectFilter(e) {
    const { filter } = e.currentTarget.dataset;
    this.setData({ 
      currentFilter: filter,
      showFilterMenu: false 
    });
    this.updateFilteredPosts();
  },

  updateFilteredPosts() {
    let posts = [...this.data.communityPosts];
    
    // 根据活跃标签筛选
    if (this.data.activeTab !== 'all') {
      posts = posts.filter(post => post.type === this.data.activeTab);
    }
    
    // 根据筛选条件排序
    switch (this.data.currentFilter.value) {
      case 'popular':
        posts.sort((a, b) => b.likeCount - a.likeCount);
        break;
      case 'following':
        // 简化处理，仅显示部分帖子
        posts = posts.slice(0, 2);
        break;
      case 'nearby':
        // 简化处理，显示有位置信息的帖子
        posts = posts.filter(post => post.location);
        break;
      default: // latest
        posts.sort((a, b) => b.timestamp - a.timestamp);
    }
    
    this.setData({ filteredPosts: posts });
  },

  /**
   * 动态交互
   */
  onPostDetail(e) {
    const { post } = e.currentTarget.dataset;
    wx.showModal({
      title: '动态详情',
      content: `${post.user.name}: ${post.content}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  onToggleLike(e) {
    const { postId } = e.currentTarget.dataset;
    const posts = [...this.data.communityPosts];
    const filteredPosts = [...this.data.filteredPosts];
    
    // 更新原始数据
    const postIndex = posts.findIndex(post => post.id === postId);
    if (postIndex !== -1) {
      posts[postIndex].isLiked = !posts[postIndex].isLiked;
      posts[postIndex].likeCount += posts[postIndex].isLiked ? 1 : -1;
    }
    
    // 更新筛选后的数据
    const filteredIndex = filteredPosts.findIndex(post => post.id === postId);
    if (filteredIndex !== -1) {
      filteredPosts[filteredIndex].isLiked = posts[postIndex].isLiked;
      filteredPosts[filteredIndex].likeCount = posts[postIndex].likeCount;
    }
    
    this.setData({ 
      communityPosts: posts,
      filteredPosts: filteredPosts 
    });
    
    // 触觉反馈
    wx.vibrateShort();
    
    // 保存到store
    store.addCommunityPost({ posts: posts });
  },

  onShowComments(e) {
    const { postId } = e.currentTarget.dataset;
    
    // 模拟加载评论数据
    const mockComments = [
      {
        id: 1,
        user: { name: '运动达人', avatar: '/assets/images/avatar5.jpg' },
        content: '太棒了！坚持就是胜利！',
        timeAgo: '1小时前'
      },
      {
        id: 2,
        user: { name: '健身新手', avatar: '/assets/images/avatar6.jpg' },
        content: '请问有什么训练建议吗？',
        timeAgo: '30分钟前'
      }
    ];
    
    this.setData({
      showCommentsModal: true,
      currentPostId: postId,
      currentPostComments: mockComments
    });
  },

  onCloseCommentsModal() {
    this.setData({
      showCommentsModal: false,
      currentPostId: null,
      currentPostComments: [],
      newComment: ''
    });
  },

  onCommentInput(e) {
    this.setData({ newComment: e.detail.value });
  },

  onSendComment() {
    const { newComment, currentPostComments, currentPostId } = this.data;
    
    if (!newComment.trim()) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }
    
    // 添加新评论
    const newCommentObj = {
      id: Date.now(),
      user: { name: '我', avatar: '/assets/images/my-avatar.jpg' },
      content: newComment.trim(),
      timeAgo: '刚刚'
    };
    
    const updatedComments = [...currentPostComments, newCommentObj];
    
    // 更新评论数量
    const posts = [...this.data.communityPosts];
    const filteredPosts = [...this.data.filteredPosts];
    
    const postIndex = posts.findIndex(post => post.id === currentPostId);
    if (postIndex !== -1) {
      posts[postIndex].commentCount += 1;
    }
    
    const filteredIndex = filteredPosts.findIndex(post => post.id === currentPostId);
    if (filteredIndex !== -1) {
      filteredPosts[filteredIndex].commentCount += 1;
    }
    
    this.setData({
      currentPostComments: updatedComments,
      newComment: '',
      communityPosts: posts,
      filteredPosts: filteredPosts
    });
    
    wx.showToast({
      title: '评论成功',
      icon: 'success'
    });
  },

  onSharePost(e) {
    const { post } = e.currentTarget.dataset;
    
    wx.showShareMenu({
      withShareTicket: true,
      success: () => {
        wx.showToast({
          title: '分享成功！',
          icon: 'success'
        });
      }
    });
  },

  onPreviewImage(e) {
    const { images, current } = e.currentTarget.dataset;
    
    wx.previewImage({
      urls: images,
      current: images[current]
    });
  },

  /**
   * 发布动态
   */
  onPublishPost() {
    this.setData({ showPublishModal: true });
  },

  onClosePublishModal() {
    this.setData({
      showPublishModal: false,
      newPost: {
        type: 'workout',
        content: '',
        images: [],
        workoutTypeIndex: 0,
        workoutData: {
          duration: '',
          calories: ''
        },
        location: ''
      }
    });
  },

  onSelectPostType(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({ 'newPost.type': type });
  },

  onContentInput(e) {
    this.setData({ 'newPost.content': e.detail.value });
  },

  onChooseImage() {
    wx.chooseImage({
      count: 9 - this.data.newPost.images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = [...this.data.newPost.images, ...res.tempFilePaths];
        this.setData({ 'newPost.images': newImages });
      }
    });
  },

  onRemoveImage(e) {
    const { index } = e.currentTarget.dataset;
    const images = [...this.data.newPost.images];
    images.splice(index, 1);
    this.setData({ 'newPost.images': images });
  },

  onWorkoutTypeChange(e) {
    this.setData({ 'newPost.workoutTypeIndex': parseInt(e.detail.value) });
  },

  onWorkoutDurationInput(e) {
    this.setData({ 'newPost.workoutData.duration': e.detail.value });
  },

  onWorkoutCaloriesInput(e) {
    this.setData({ 'newPost.workoutData.calories': e.detail.value });
  },

  onChooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({ 'newPost.location': res.name });
      },
      fail: () => {
        wx.showToast({
          title: '获取位置失败',
          icon: 'none'
        });
      }
    });
  },

  onConfirmPublish() {
    const { newPost } = this.data;
    
    if (!newPost.content.trim()) {
      wx.showToast({
        title: '请输入动态内容',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '发布中...' });
    
    // 模拟发布过程
    setTimeout(() => {
      const newPostObj = {
        id: Date.now(),
        type: newPost.type,
        typeLabel: this.getTypeLabel(newPost.type),
        user: {
          id: 999,
          name: '我',
          avatar: '/assets/images/my-avatar.jpg'
        },
        content: newPost.content,
        images: newPost.images,
        workoutData: newPost.type === 'workout' ? {
          type: this.data.workoutTypes[newPost.workoutTypeIndex].label,
          duration: newPost.workoutData.duration + '分钟',
          calories: newPost.workoutData.calories + '卡路里'
        } : null,
        location: newPost.location,
        timeAgo: '刚刚',
        likeCount: 0,
        commentCount: 0,
        isLiked: false,
        timestamp: Date.now()
      };
      
      const updatedPosts = [newPostObj, ...this.data.communityPosts];
      
      this.setData({
        communityPosts: updatedPosts,
        showPublishModal: false
      });
      
      this.updateFilteredPosts();
      this.onClosePublishModal();
      
      wx.hideLoading();
      wx.showToast({
        title: '发布成功！',
        icon: 'success'
      });
      
      // 保存到store
      store.addCommunityPost({ posts: updatedPosts });
      
    }, 2000);
  },

  getTypeLabel(type) {
    const typeMap = {
      workout: '运动打卡',
      food: '饮食分享',
      moment: '生活动态'
    };
    return typeMap[type] || '动态';
  },

  /**
   * 加载更多动态
   */
  onLoadMorePosts() {
    if (!this.data.hasMorePosts) return;
    
    wx.showLoading({ title: '加载中...' });
    
    // 模拟加载更多数据
    setTimeout(() => {
      const morePosts = [
        {
          id: Date.now(),
          type: 'workout',
          typeLabel: '运动打卡',
          user: {
            id: 105,
            name: '健身新人',
            avatar: '/assets/images/avatar7.jpg'
          },
          content: '第一次完成5公里跑步，虽然很累但是很有成就感！',
          timeAgo: '2天前',
          likeCount: 8,
          commentCount: 2,
          isLiked: false,
          timestamp: Date.now() - 2 * 24 * 60 * 60 * 1000
        }
      ];
      
      this.setData({
        communityPosts: [...this.data.communityPosts, ...morePosts],
        hasMorePosts: false
      });
      
      this.updateFilteredPosts();
      
      wx.hideLoading();
    }, 1000);
  },

  /**
   * 加载社区数据
   */
  loadCommunityData() {
    try {
      const savedData = wx.getStorageSync('communityData');
      if (savedData) {
        this.setData({
          communityPosts: savedData.posts || this.data.communityPosts,
          userStats: savedData.userStats || this.data.userStats,
          checkinTypes: savedData.checkinTypes || this.data.checkinTypes
        });
      }
    } catch (error) {
      console.error('Failed to load community data:', error);
    }
  },

  /**
   * 保存社区数据
   */
  saveCommunityData() {
    try {
      const dataToSave = {
        posts: this.data.communityPosts,
        userStats: this.data.userStats,
        checkinTypes: this.data.checkinTypes,
        timestamp: Date.now()
      };
      
      wx.setStorageSync('communityData', dataToSave);
    } catch (error) {
      console.error('Failed to save community data:', error);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('Refreshing community page...');
    
    // 重新加载数据
    this.loadCommunityData();
    this.updateFilteredPosts();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMorePosts) {
      this.onLoadMorePosts();
    }
  },

  /**
   * 页面隐藏时保存数据
   */
  onHide() {
    this.saveCommunityData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'FitFocus健身社区 - 一起健身，共同进步！',
      path: '/pages/community/community',
      imageUrl: '/assets/images/community-share.jpg'
    };
  }
}); 