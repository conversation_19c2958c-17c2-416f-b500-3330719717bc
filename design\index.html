<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 健身番茄钟App原型设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-frame {
            width: 393px;
            height: 852px;
            border-radius: 47px;
            box-shadow: 0 0 50px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }
        .phone-bezel {
            background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
            padding: 8px;
            border-radius: 47px;
        }
        .screen {
            width: 100%;
            height: 100%;
            border-radius: 39px;
            overflow: hidden;
            background: #000;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
            gap: 40px;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .prototype-card {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .prototype-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        iframe {
            border: none;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="min-h-screen bg-gray-100">
        <!-- Header -->
        <header class="bg-white shadow-lg py-6">
            <div class="container mx-auto px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800">FitFocus App</h1>
                        <p class="text-gray-600 mt-1">健身番茄钟 - 高保真原型设计</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-mobile-alt mr-2"></i>iPhone 15 Pro
                        </div>
                        <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-palette mr-2"></i>高保真设计
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Prototype Grid -->
        <div class="prototype-grid">
            <!-- 主页 - 番茄钟 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-home mr-2"></i>主页 - 专注健身
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="home.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    融合番茄钟专注训练的首页设计，包含今日目标和快捷操作
                </p>
            </div>

            <!-- 训练页面 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-dumbbell mr-2"></i>训练 - 课程与设备
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="workout.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    课程预约、设备状态查看和快速扫码功能集成页面
                </p>
            </div>

            <!-- 数据分析页面 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-chart-line mr-2"></i>数据 - 进度分析
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="analytics.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    健身数据可视化展示，包含番茄钟统计和体测报告
                </p>
            </div>

            <!-- 社区页面 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-users mr-2"></i>社区 - 健身分享
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="community.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    健身社区互动，包含打卡分享、动态评论和挑战赛
                </p>
            </div>

            <!-- 个人中心 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-user mr-2"></i>我的 - 个人中心
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="profile.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    个人信息管理、成就徽章和会员权益展示页面
                </p>
            </div>

            <!-- 课程详情页 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-calendar-alt mr-2"></i>课程详情
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="course-detail.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    详细的课程信息展示，包含教练介绍和用户评价
                </p>
            </div>

            <!-- 设备扫码页 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-qrcode mr-2"></i>设备扫码
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="scanner.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    动态二维码扫描界面，支持快速设备启动和历史记录
                </p>
            </div>

            <!-- 体测记录页 -->
            <div class="prototype-card">
                <h3 class="prototype-title">
                    <i class="fas fa-weight mr-2"></i>体测记录
                </h3>
                <div class="phone-bezel">
                    <div class="phone-frame">
                        <div class="screen">
                            <iframe src="body-data.html"></iframe>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-3 text-center">
                    智能体测数据分析，包含趋势图表和健康建议
                </p>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-gray-800 text-white py-8">
            <div class="container mx-auto px-6 text-center">
                <h3 class="text-xl font-semibold mb-4">FitFocus - 让健身更专注</h3>
                <p class="text-gray-400 mb-4">融合番茄工作法的智能健身管理应用</p>
                <div class="flex justify-center space-x-6 text-sm flex-wrap">
                    <span><i class="fas fa-stopwatch mr-2"></i>番茄钟专注训练</span>
                    <span><i class="fas fa-chart-bar mr-2"></i>智能数据分析</span>
                    <span><i class="fas fa-users mr-2"></i>健身社区互动</span>
                    <span><i class="fas fa-medal mr-2"></i>成就激励系统</span>
                </div>
                <div class="mt-6 pt-6 border-t border-gray-700">
                    <h4 class="text-lg font-medium mb-3">产品特色</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div class="bg-gray-700 rounded-lg p-4">
                            <i class="fas fa-clock text-indigo-400 text-xl mb-2"></i>
                            <h5 class="font-medium mb-1">番茄钟专注</h5>
                            <p class="text-gray-300">25分钟专注训练，科学时间管理</p>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <i class="fas fa-qrcode text-blue-400 text-xl mb-2"></i>
                            <h5 class="font-medium mb-1">智能设备联动</h5>
                            <p class="text-gray-300">扫码启动健身设备，无缝连接</p>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <i class="fas fa-chart-line text-green-400 text-xl mb-2"></i>
                            <h5 class="font-medium mb-1">数据可视化</h5>
                            <p class="text-gray-300">训练数据图表化，进度清晰可见</p>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <i class="fas fa-heart text-red-400 text-xl mb-2"></i>
                            <h5 class="font-medium mb-1">社区激励</h5>
                            <p class="text-gray-300">健身打卡分享，好友互相激励</p>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html> 