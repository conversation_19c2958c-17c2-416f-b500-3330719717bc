/* 数据分析页面样式 */
.container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 0 32rpx 120rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 0 32rpx;
  margin-top: var(--status-bar-height, 44px);
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: #5a67d8;
}

.btn-icon {
  color: #ffffff;
  font-size: 32rpx;
}

/* 时间筛选 */
.time-filter {
  margin-bottom: 48rpx;
}

.filter-tabs {
  display: flex;
  gap: 8rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 8rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.2s ease;
}

.tab-item.active {
  background: #667eea;
}

.tab-text {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #ffffff;
}

/* 核心数据统计 */
.stats-overview {
  margin-bottom: 48rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.stat-card:active {
  transform: translateY(2rpx);
}

.stat-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 20rpx;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

.stat-change {
  font-size: 20rpx;
  color: #10b981;
  font-weight: 500;
}

/* 图表部分 */
.chart-section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.chart-legend {
  display: flex;
  gap: 24rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 4rpx;
}

.legend-color.workout {
  background: #667eea;
}

.legend-text {
  font-size: 24rpx;
  color: #6b7280;
}

.chart-container {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.chart-canvas {
  width: 100%;
  height: 400rpx;
}

.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s ease;
  z-index: 10;
}

.chart-tooltip.show {
  opacity: 1;
}

.tooltip-date {
  display: block;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.tooltip-value {
  display: block;
  font-size: 20rpx;
  color: #d1d5db;
}

/* 体征数据 */
.body-data-section {
  margin-bottom: 48rpx;
}

.data-selector {
  background: #f3f4f6;
  border-radius: 16rpx;
  padding: 12rpx 24rpx;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-text {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

.picker-arrow {
  font-size: 20rpx;
  color: #667eea;
}

.body-data-chart {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.latest-data {
  display: flex;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.data-item {
  flex: 1;
  text-align: center;
}

.data-label {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.data-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.data-value.positive {
  color: #10b981;
}

.data-value.negative {
  color: #ef4444;
}

/* 训练类型分布 */
.workout-distribution {
  margin-bottom: 48rpx;
}

.distribution-chart {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 32rpx;
  align-items: center;
}

.pie-chart {
  width: 300rpx;
  height: 300rpx;
  flex-shrink: 0;
}

.distribution-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.distribution-legend .legend-item {
  justify-content: space-between;
  padding: 8rpx 0;
}

.legend-percent {
  font-size: 24rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 成就与里程碑 */
.achievements-section {
  margin-bottom: 48rpx;
}

.view-all {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.view-all-text {
  font-size: 28rpx;
  color: #667eea;
}

.arrow {
  font-size: 24rpx;
  color: #667eea;
}

.achievements-scroll {
  white-space: nowrap;
  margin-bottom: 16rpx;
}

.achievements-list {
  display: flex;
  gap: 24rpx;
  padding-bottom: 16rpx;
}

.achievement-card {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  min-width: 280rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;
}

.achievement-card:active {
  transform: translateY(4rpx);
}

.achievement-card.locked {
  opacity: 0.6;
}

.achievement-icon {
  font-size: 64rpx;
  margin-bottom: 8rpx;
}

.achievement-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 8rpx;
}

.achievement-desc {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
  line-height: 1.4;
}

.achievement-progress {
  width: 100%;
  margin-top: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f3f4f6;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #9ca3af;
  text-align: center;
}

.achievement-date {
  margin-top: 16rpx;
}

.date-text {
  font-size: 20rpx;
  color: #10b981;
}

/* 个人记录 */
.personal-records {
  margin-bottom: 48rpx;
}

.records-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}

.record-item {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.record-item:active {
  transform: translateY(2rpx);
}

.record-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.record-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.record-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

.record-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

.record-date {
  font-size: 24rpx;
  color: #9ca3af;
}

.record-trend {
  flex-shrink: 0;
}

.trend-icon {
  font-size: 32rpx;
}

/* 数据导出 */
.export-section {
  margin-bottom: 48rpx;
}

.export-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.export-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: none;
  transition: all 0.2s ease;
}

.export-btn:active {
  transform: translateY(4rpx);
}

.export-btn .btn-icon {
  font-size: 48rpx;
  color: #667eea;
}

.btn-text {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .export-options {
    grid-template-columns: 1fr;
  }
  
  .distribution-chart {
    flex-direction: column;
    text-align: center;
  }
  
  .pie-chart {
    width: 250rpx;
    height: 250rpx;
  }
} 