<component name="libraryTable">
  <library name="Maven: org.springframework.security:spring-security-config:5.7.10">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-config/5.7.10/spring-security-config-5.7.10.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-config/5.7.10/spring-security-config-5.7.10-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-config/5.7.10/spring-security-config-5.7.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>