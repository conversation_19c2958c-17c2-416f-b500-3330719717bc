package com.gym.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gym.common.result.Result;
import com.gym.entity.CommunityPost;
import com.gym.entity.FitnessCheckin;
import com.gym.service.CommunityService;
import com.gym.util.JwtUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 社区控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/community")
public class CommunityController {

    @Autowired
    private CommunityService communityService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 分页获取社区动态
     */
    @GetMapping("/posts")
    public Result<IPage<CommunityPost>> getCommunityPosts(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer postType,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long topicId) {
        try {
            Page<CommunityPost> pageParam = new Page<>(page, size);
            IPage<CommunityPost> result = communityService.getCommunityPosts(pageParam, postType, keyword, topicId);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("获取社区动态失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 发布动态
     */
    @PostMapping("/posts")
    public Result<Void> publishPost(@RequestHeader("Authorization") String authHeader,
                                   @Valid @RequestBody PublishPostRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            CommunityPost post = new CommunityPost();
            post.setUserId(userId);
            post.setPostType(request.getPostType());
            post.setTitle(request.getTitle());
            post.setContent(request.getContent());
            post.setImages(request.getImages());
            post.setTags(request.getTags());
            post.setTopicId(request.getTopicId());
            
            boolean success = communityService.publishPost(post);
            if (success) {
                return Result.success("发布成功");
            } else {
                return Result.error("发布失败");
            }
        } catch (Exception e) {
            log.error("发布动态失败: {}", e.getMessage(), e);
            return Result.error("发布失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户动态
     */
    @GetMapping("/my-posts")
    public Result<List<CommunityPost>> getMyPosts(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            List<CommunityPost> posts = communityService.getUserPosts(userId);
            return Result.success("查询成功", posts);
        } catch (Exception e) {
            log.error("获取用户动态失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取热门动态
     */
    @GetMapping("/hot-posts")
    public Result<List<CommunityPost>> getHotPosts(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<CommunityPost> posts = communityService.getHotPosts(limit);
            return Result.success("查询成功", posts);
        } catch (Exception e) {
            log.error("获取热门动态失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取置顶动态
     */
    @GetMapping("/top-posts")
    public Result<List<CommunityPost>> getTopPosts() {
        try {
            List<CommunityPost> posts = communityService.getTopPosts();
            return Result.success("查询成功", posts);
        } catch (Exception e) {
            log.error("获取置顶动态失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 点赞动态
     */
    @PostMapping("/posts/{postId}/like")
    public Result<Void> likePost(@RequestHeader("Authorization") String authHeader,
                                @PathVariable Long postId) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = communityService.toggleLikePost(userId, postId);
            if (success) {
                return Result.success("操作成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("点赞动态失败: {}", e.getMessage(), e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 分享动态
     */
    @PostMapping("/posts/{postId}/share")
    public Result<Void> sharePost(@RequestHeader("Authorization") String authHeader,
                                 @PathVariable Long postId) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = communityService.sharePost(userId, postId);
            if (success) {
                return Result.success("分享成功");
            } else {
                return Result.error("分享失败");
            }
        } catch (Exception e) {
            log.error("分享动态失败: {}", e.getMessage(), e);
            return Result.error("分享失败: " + e.getMessage());
        }
    }

    /**
     * 删除动态
     */
    @DeleteMapping("/posts/{postId}")
    public Result<Void> deletePost(@RequestHeader("Authorization") String authHeader,
                                  @PathVariable Long postId) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = communityService.deletePost(userId, postId);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除动态失败: {}", e.getMessage(), e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 健身打卡
     */
    @PostMapping("/checkin")
    public Result<Void> fitnessCheckin(@RequestHeader("Authorization") String authHeader,
                                      @Valid @RequestBody CheckinRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            FitnessCheckin checkin = new FitnessCheckin();
            checkin.setUserId(userId);
            checkin.setCheckinType(request.getCheckinType());
            checkin.setWorkoutType(request.getWorkoutType());
            checkin.setDuration(request.getDuration());
            checkin.setCalories(request.getCalories());
            checkin.setMood(request.getMood());
            checkin.setContent(request.getContent());
            checkin.setImages(request.getImages());
            checkin.setLocation(request.getLocation());
            checkin.setIsPublic(request.getIsPublic());
            
            boolean success = communityService.fitnessCheckin(checkin);
            if (success) {
                return Result.success("打卡成功");
            } else {
                return Result.error("打卡失败");
            }
        } catch (Exception e) {
            log.error("健身打卡失败: {}", e.getMessage(), e);
            return Result.error("打卡失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户打卡记录
     */
    @GetMapping("/my-checkins")
    public Result<List<FitnessCheckin>> getMyCheckins(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            List<FitnessCheckin> checkins = communityService.getUserCheckins(userId);
            return Result.success("查询成功", checkins);
        } catch (Exception e) {
            log.error("获取打卡记录失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取今日打卡
     */
    @GetMapping("/today-checkin")
    public Result<FitnessCheckin> getTodayCheckin(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            FitnessCheckin checkin = communityService.getTodayCheckin(userId);
            return Result.success("查询成功", checkin);
        } catch (Exception e) {
            log.error("获取今日打卡失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取连续打卡天数
     */
    @GetMapping("/continuous-days")
    public Result<Integer> getContinuousCheckinDays(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            Integer days = communityService.getContinuousCheckinDays(userId);
            return Result.success("查询成功", days);
        } catch (Exception e) {
            log.error("获取连续打卡天数失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取公开打卡记录
     */
    @GetMapping("/public-checkins")
    public Result<List<FitnessCheckin>> getPublicCheckins(@RequestParam(defaultValue = "20") Integer limit) {
        try {
            List<FitnessCheckin> checkins = communityService.getPublicCheckins(limit);
            return Result.success("查询成功", checkins);
        } catch (Exception e) {
            log.error("获取公开打卡记录失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 根据类型获取打卡记录
     */
    @GetMapping("/checkins/type/{checkinType}")
    public Result<List<FitnessCheckin>> getCheckinsByType(@PathVariable Integer checkinType,
                                                          @RequestParam(defaultValue = "20") Integer limit) {
        try {
            List<FitnessCheckin> checkins = communityService.getCheckinsByType(checkinType, limit);
            return Result.success("查询成功", checkins);
        } catch (Exception e) {
            log.error("根据类型获取打卡记录失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 点赞打卡
     */
    @PostMapping("/checkins/{checkinId}/like")
    public Result<Void> likeCheckin(@RequestHeader("Authorization") String authHeader,
                                   @PathVariable Long checkinId) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = communityService.toggleLikeCheckin(userId, checkinId);
            if (success) {
                return Result.success("操作成功");
            } else {
                return Result.error("操作失败");
            }
        } catch (Exception e) {
            log.error("点赞打卡失败: {}", e.getMessage(), e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取打卡统计
     */
    @GetMapping("/checkin-stats")
    public Result<Object> getCheckinStats(@RequestHeader("Authorization") String authHeader,
                                         @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                         @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 默认查询最近30天
            if (startDate == null) startDate = LocalDate.now().minusDays(30);
            if (endDate == null) endDate = LocalDate.now();
            
            Object stats = communityService.getCheckinStats(userId, startDate, endDate);
            return Result.success("查询成功", stats);
        } catch (Exception e) {
            log.error("获取打卡统计失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 从令牌中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        String token = jwtUtil.extractTokenFromHeader(authHeader);
        if (token == null || !jwtUtil.isTokenValid(token)) {
            throw new RuntimeException("令牌无效");
        }
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 发布动态请求对象
     */
    @Data
    public static class PublishPostRequest {
        private Integer postType;
        private String title;
        
        @NotBlank(message = "内容不能为空")
        private String content;
        
        private String images;
        private String tags;
        private Long topicId;
    }

    /**
     * 健身打卡请求对象
     */
    @Data
    public static class CheckinRequest {
        @NotNull(message = "打卡类型不能为空")
        private Integer checkinType;
        
        private String workoutType;
        private Integer duration;
        private Integer calories;
        private Integer mood;
        
        @NotBlank(message = "打卡内容不能为空")
        private String content;
        
        private String images;
        private String location;
        private Integer isPublic;
    }
} 