package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 健身打卡实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("fitness_checkin")
public class FitnessCheckin implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("checkin_date")
    private LocalDate checkinDate;

    @TableField("checkin_type")
    private Integer checkinType;

    @TableField("workout_type")
    private String workoutType;

    @TableField("duration")
    private Integer duration;

    @TableField("calories")
    private Integer calories;

    @TableField("mood")
    private Integer mood;

    @TableField("content")
    private String content;

    @TableField("images")
    private String images;

    @TableField("location")
    private String location;

    @TableField("is_public")
    private Integer isPublic;

    @TableField("like_count")
    private Integer likeCount;

    @TableField("comment_count")
    private Integer commentCount;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取打卡类型名称
     */
    public String getCheckinTypeName() {
        if (checkinType == null) {
            return "到店打卡";
        }
        switch (checkinType) {
            case 1: return "到店打卡";
            case 2: return "运动打卡";
            case 3: return "饮食打卡";
            default: return "到店打卡";
        }
    }

    /**
     * 获取心情描述
     */
    public String getMoodDescription() {
        if (mood == null) {
            return "一般";
        }
        switch (mood) {
            case 1: return "很差";
            case 2: return "较差";
            case 3: return "一般";
            case 4: return "较好";
            case 5: return "很好";
            default: return "一般";
        }
    }

    /**
     * 获取心情表情
     */
    public String getMoodEmoji() {
        if (mood == null) {
            return "😐";
        }
        switch (mood) {
            case 1: return "😞";
            case 2: return "☹️";
            case 3: return "😐";
            case 4: return "😊";
            case 5: return "😄";
            default: return "😐";
        }
    }

    /**
     * 判断是否公开
     */
    public boolean isPublicCheckin() {
        return isPublic != null && isPublic == 1;
    }

    /**
     * 判断是否运动打卡
     */
    public boolean isWorkoutCheckin() {
        return checkinType != null && checkinType == 2;
    }

    /**
     * 判断是否饮食打卡
     */
    public boolean isDietCheckin() {
        return checkinType != null && checkinType == 3;
    }

    /**
     * 获取运动强度等级
     */
    public String getWorkoutIntensity() {
        if (!isWorkoutCheckin() || duration == null) {
            return "无";
        }
        if (duration < 30) {
            return "轻度";
        } else if (duration < 60) {
            return "中等";
        } else {
            return "高强度";
        }
    }
} 