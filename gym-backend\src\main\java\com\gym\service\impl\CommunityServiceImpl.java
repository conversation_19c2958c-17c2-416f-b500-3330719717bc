package com.gym.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gym.entity.CommunityPost;
import com.gym.entity.FitnessCheckin;
import com.gym.mapper.CommunityPostMapper;
import com.gym.mapper.FitnessCheckinMapper;
import com.gym.service.CommunityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 社区服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class CommunityServiceImpl extends ServiceImpl<CommunityPostMapper, CommunityPost> implements CommunityService {

    @Autowired
    private CommunityPostMapper communityPostMapper;

    @Autowired
    private FitnessCheckinMapper fitnessCheckinMapper;

    @Override
    public IPage<CommunityPost> getCommunityPosts(Page<CommunityPost> page, Integer postType, String keyword, Long topicId) {
        return communityPostMapper.selectPageWithCondition(page, postType, keyword, topicId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishPost(CommunityPost post) {
        if (post.getUserId() == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        // 设置默认值
        post.setLikeCount(0);
        post.setCommentCount(0);
        post.setShareCount(0);
        post.setViewCount(0);
        post.setIsTop(0);
        post.setIsHot(0);
        post.setStatus(1); // 正常状态
        
        return communityPostMapper.insert(post) > 0;
    }

    @Override
    public List<CommunityPost> getUserPosts(Long userId) {
        return communityPostMapper.selectByUserId(userId);
    }

    @Override
    public List<CommunityPost> getHotPosts(Integer limit) {
        return communityPostMapper.selectHotPosts(limit);
    }

    @Override
    public List<CommunityPost> getTopPosts() {
        return communityPostMapper.selectTopPosts();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleLikePost(Long userId, Long postId) {
        // 这里简化处理，实际应该有like_record表来记录点赞状态
        // 暂时只增加点赞数
        return communityPostMapper.updateLikeCount(postId, 1) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sharePost(Long userId, Long postId) {
        return communityPostMapper.updateShareCount(postId, 1) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementViewCount(Long postId) {
        return communityPostMapper.updateViewCount(postId, 1) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fitnessCheckin(FitnessCheckin checkin) {
        if (checkin.getUserId() == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        // 检查今日是否已打卡
        FitnessCheckin todayCheckin = fitnessCheckinMapper.selectTodayCheckinByUserId(checkin.getUserId());
        if (todayCheckin != null) {
            throw new RuntimeException("今日已打卡");
        }
        
        // 设置默认值
        if (checkin.getCheckinDate() == null) {
            checkin.setCheckinDate(LocalDate.now());
        }
        checkin.setLikeCount(0);
        checkin.setCommentCount(0);
        
        return fitnessCheckinMapper.insert(checkin) > 0;
    }

    @Override
    public List<FitnessCheckin> getUserCheckins(Long userId) {
        return fitnessCheckinMapper.selectByUserId(userId);
    }

    @Override
    public FitnessCheckin getTodayCheckin(Long userId) {
        return fitnessCheckinMapper.selectTodayCheckinByUserId(userId);
    }

    @Override
    public Integer getContinuousCheckinDays(Long userId) {
        return fitnessCheckinMapper.selectContinuousCheckinDays(userId);
    }

    @Override
    public List<FitnessCheckin> getPublicCheckins(Integer limit) {
        return fitnessCheckinMapper.selectPublicCheckins(limit);
    }

    @Override
    public List<FitnessCheckin> getCheckinsByType(Integer checkinType, Integer limit) {
        return fitnessCheckinMapper.selectByCheckinType(checkinType, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleLikeCheckin(Long userId, Long checkinId) {
        // 这里简化处理，实际应该有like_record表来记录点赞状态
        return fitnessCheckinMapper.updateLikeCount(checkinId, 1) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePost(Long userId, Long postId) {
        CommunityPost post = communityPostMapper.selectById(postId);
        if (post == null) {
            throw new RuntimeException("动态不存在");
        }
        
        if (!post.getUserId().equals(userId)) {
            throw new RuntimeException("只能删除自己的动态");
        }
        
        // 软删除
        post.setStatus(3);
        return communityPostMapper.updateById(post) > 0;
    }

    @Override
    public Object getCheckinStats(Long userId, LocalDate startDate, LocalDate endDate) {
        List<FitnessCheckin> checkins = fitnessCheckinMapper.selectByDateRange(userId, startDate, endDate);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCheckins", checkins.size());
        stats.put("period", startDate + " 至 " + endDate);
        
        // 计算打卡频率
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        double frequency = checkins.size() / (double) days * 100;
        stats.put("checkinFrequency", Math.round(frequency * 10.0) / 10.0 + "%");
        
        // 按类型统计
        Map<String, Integer> typeStats = new HashMap<>();
        for (FitnessCheckin checkin : checkins) {
            String typeName = checkin.getCheckinTypeName();
            typeStats.put(typeName, typeStats.getOrDefault(typeName, 0) + 1);
        }
        stats.put("typeStats", typeStats);
        
        // 连续打卡天数
        Integer continuousDays = getContinuousCheckinDays(userId);
        stats.put("continuousCheckinDays", continuousDays != null ? continuousDays : 0);
        
        return stats;
    }
} 