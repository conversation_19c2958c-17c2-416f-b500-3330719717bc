package com.gym.controller;

import com.gym.common.result.Result;
import com.gym.entity.UserPhysicalData;
import com.gym.service.AnalyticsService;
import com.gym.util.JwtUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 数据分析控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/analytics")
public class AnalyticsController {

    @Autowired
    private AnalyticsService analyticsService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取用户运动数据统计
     */
    @GetMapping("/workout-stats")
    public Result<Map<String, Object>> getWorkoutStats(@RequestHeader("Authorization") String authHeader,
                                                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 默认查询最近30天
            if (startDate == null) startDate = LocalDate.now().minusDays(30);
            if (endDate == null) endDate = LocalDate.now();
            
            Map<String, Object> stats = analyticsService.getUserWorkoutStats(userId, startDate, endDate);
            return Result.success("查询成功", stats);
        } catch (Exception e) {
            log.error("获取运动统计失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户体征数据趋势
     */
    @GetMapping("/physical-trends")
    public Result<Map<String, Object>> getPhysicalTrends(@RequestHeader("Authorization") String authHeader,
                                                         @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                         @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 默认查询最近90天
            if (startDate == null) startDate = LocalDate.now().minusDays(90);
            if (endDate == null) endDate = LocalDate.now();
            
            Map<String, Object> trends = analyticsService.getUserPhysicalTrends(userId, startDate, endDate);
            return Result.success("查询成功", trends);
        } catch (Exception e) {
            log.error("获取体征趋势失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户课程参与统计
     */
    @GetMapping("/course-stats")
    public Result<Map<String, Object>> getCourseStats(@RequestHeader("Authorization") String authHeader,
                                                      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 默认查询最近30天
            if (startDate == null) startDate = LocalDate.now().minusDays(30);
            if (endDate == null) endDate = LocalDate.now();
            
            Map<String, Object> stats = analyticsService.getUserCourseStats(userId, startDate, endDate);
            return Result.success("查询成功", stats);
        } catch (Exception e) {
            log.error("获取课程统计失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户设备使用统计
     */
    @GetMapping("/equipment-stats")
    public Result<Map<String, Object>> getEquipmentStats(@RequestHeader("Authorization") String authHeader,
                                                         @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                         @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 默认查询最近30天
            if (startDate == null) startDate = LocalDate.now().minusDays(30);
            if (endDate == null) endDate = LocalDate.now();
            
            Map<String, Object> stats = analyticsService.getUserEquipmentStats(userId, startDate, endDate);
            return Result.success("查询成功", stats);
        } catch (Exception e) {
            log.error("获取设备使用统计失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户体征数据历史
     */
    @GetMapping("/physical-history")
    public Result<List<UserPhysicalData>> getPhysicalHistory(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            List<UserPhysicalData> history = analyticsService.getUserPhysicalHistory(userId);
            return Result.success("查询成功", history);
        } catch (Exception e) {
            log.error("获取体征历史失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 添加用户体征数据
     */
    @PostMapping("/physical-data")
    public Result<Void> addPhysicalData(@RequestHeader("Authorization") String authHeader,
                                       @Valid @RequestBody AddPhysicalDataRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            UserPhysicalData physicalData = new UserPhysicalData();
            physicalData.setUserId(userId);
            physicalData.setHeight(request.getHeight());
            physicalData.setWeight(request.getWeight());
            physicalData.setBodyFatRate(request.getBodyFatRate());
            physicalData.setMuscleMass(request.getMuscleMass());
            physicalData.setVisceralFat(request.getVisceralFat());
            physicalData.setBodyWater(request.getBodyWater());
            physicalData.setBoneMass(request.getBoneMass());
            physicalData.setMeasurementDate(request.getMeasurementDate() != null ? request.getMeasurementDate() : LocalDate.now());
            physicalData.setDataSource(request.getDataSource());
            physicalData.setDeviceType(request.getDeviceType());
            
            boolean success = analyticsService.addUserPhysicalData(physicalData);
            if (success) {
                return Result.success("添加成功");
            } else {
                return Result.error("添加失败");
            }
        } catch (Exception e) {
            log.error("添加体征数据失败: {}", e.getMessage(), e);
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户最新体征数据
     */
    @GetMapping("/latest-physical")
    public Result<UserPhysicalData> getLatestPhysicalData(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            UserPhysicalData data = analyticsService.getLatestPhysicalData(userId);
            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("获取最新体征数据失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户健身目标达成情况
     */
    @GetMapping("/goal-progress")
    public Result<Map<String, Object>> getGoalProgress(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            Map<String, Object> progress = analyticsService.getFitnessGoalProgress(userId);
            return Result.success("查询成功", progress);
        } catch (Exception e) {
            log.error("获取目标进度失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户运动强度分析
     */
    @GetMapping("/intensity-analysis")
    public Result<Map<String, Object>> getIntensityAnalysis(@RequestHeader("Authorization") String authHeader,
                                                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 默认查询最近30天
            if (startDate == null) startDate = LocalDate.now().minusDays(30);
            if (endDate == null) endDate = LocalDate.now();
            
            Map<String, Object> analysis = analyticsService.getWorkoutIntensityAnalysis(userId, startDate, endDate);
            return Result.success("查询成功", analysis);
        } catch (Exception e) {
            log.error("获取强度分析失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户健身报告
     */
    @GetMapping("/fitness-report")
    public Result<Map<String, Object>> getFitnessReport(@RequestHeader("Authorization") String authHeader,
                                                        @RequestParam(defaultValue = "monthly") String reportType) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            Map<String, Object> report = analyticsService.getFitnessReport(userId, reportType);
            return Result.success("查询成功", report);
        } catch (Exception e) {
            log.error("获取健身报告失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 从令牌中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        String token = jwtUtil.extractTokenFromHeader(authHeader);
        if (token == null || !jwtUtil.isTokenValid(token)) {
            throw new RuntimeException("令牌无效");
        }
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 添加体征数据请求对象
     */
    @Data
    public static class AddPhysicalDataRequest {
        private BigDecimal height;
        
        @NotNull(message = "体重不能为空")
        private BigDecimal weight;
        
        private BigDecimal bodyFatRate;
        private BigDecimal muscleMass;
        private Integer visceralFat;
        private BigDecimal bodyWater;
        private BigDecimal boneMass;
        
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate measurementDate;
        
        private Integer dataSource;
        private String deviceType;
    }
} 