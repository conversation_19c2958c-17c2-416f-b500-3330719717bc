package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.FitnessCheckin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 健身打卡 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface FitnessCheckinMapper extends BaseMapper<FitnessCheckin> {

    /**
     * 根据用户ID查询打卡记录
     */
    List<FitnessCheckin> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据日期范围查询打卡记录
     */
    List<FitnessCheckin> selectByDateRange(@Param("userId") Long userId,
                                          @Param("startDate") LocalDate startDate,
                                          @Param("endDate") LocalDate endDate);

    /**
     * 查询用户今日打卡记录
     */
    FitnessCheckin selectTodayCheckinByUserId(@Param("userId") Long userId);

    /**
     * 查询用户连续打卡天数
     */
    Integer selectContinuousCheckinDays(@Param("userId") Long userId);

    /**
     * 查询公开的打卡记录
     */
    List<FitnessCheckin> selectPublicCheckins(@Param("limit") Integer limit);

    /**
     * 根据打卡类型查询
     */
    List<FitnessCheckin> selectByCheckinType(@Param("checkinType") Integer checkinType,
                                            @Param("limit") Integer limit);

    /**
     * 更新点赞数
     */
    int updateLikeCount(@Param("checkinId") Long checkinId, @Param("increment") Integer increment);

    /**
     * 更新评论数
     */
    int updateCommentCount(@Param("checkinId") Long checkinId, @Param("increment") Integer increment);
} 