<!--健身房小程序首页-->
<view class="container">
  <!-- 顶部用户信息区域 -->
  <view class="user-info-section">
    <view class="user-avatar">
      <image src="{{userInfo.avatar || '/static/images/default-avatar.png'}}" class="avatar-img"></image>
    </view>
    <view class="user-details">
      <view class="username">{{userInfo.nickname || '健身爱好者'}}</view>
      <view class="member-level">{{userInfo.memberLevelName || '普通用户'}}</view>
    </view>
    <view class="checkin-btn" bindtap="onCheckin">
      <text class="checkin-text">{{isCheckedIn ? '已打卡' : '打卡'}}</text>
    </view>
  </view>

  <!-- 数据统计区域 -->
  <view class="stats-section">
    <view class="stat-item">
      <view class="stat-number">{{stats.weeklyVisits || 0}}</view>
      <view class="stat-label">本周到店</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{stats.totalHours || 0}}</view>
      <view class="stat-label">累计时长</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{stats.calories || 0}}</view>
      <view class="stat-label">消耗卡路里</view>
    </view>
  </view>

  <!-- 快捷功能区域 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="navigateTo" data-page="/pages/course/list">
      <image src="/static/icons/course.png" class="action-icon"></image>
      <text class="action-text">课程预约</text>
    </view>
    <view class="action-item" bindtap="navigateTo" data-page="/pages/equipment/scanner">
      <image src="/static/icons/scanner.png" class="action-icon"></image>
      <text class="action-text">扫码健身</text>
    </view>
    <view class="action-item" bindtap="navigateTo" data-page="/pages/profile/physical-data">
      <image src="/static/icons/body-data.png" class="action-icon"></image>
      <text class="action-text">体测记录</text>
    </view>
    <view class="action-item" bindtap="navigateTo" data-page="/pages/report/report">
      <image src="/static/icons/report.png" class="action-icon"></image>
      <text class="action-text">运动报告</text>
    </view>
  </view>

  <!-- 今日课程推荐 -->
  <view class="today-courses">
    <view class="section-title">
      <text>今日推荐课程</text>
      <text class="more-btn" bindtap="navigateTo" data-page="/pages/course/list">更多 ></text>
    </view>
    <scroll-view class="course-scroll" scroll-x="true">
      <view class="course-item" wx:for="{{todayCourses}}" wx:key="id" bindtap="navigateTo" data-page="/pages/course/detail" data-id="{{item.id}}">
        <image src="{{item.courseImage}}" class="course-image"></image>
        <view class="course-info">
          <view class="course-name">{{item.courseName}}</view>
          <view class="course-time">{{item.startTime}}</view>
          <view class="course-instructor">{{item.instructorName}}</view>
          <view class="course-capacity">{{item.currentBookings}}/{{item.maxCapacity}}人</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 设备状态区域 -->
  <view class="equipment-status">
    <view class="section-title">
      <text>设备使用状态</text>
      <text class="more-btn" bindtap="navigateTo" data-page="/pages/equipment/list">查看全部 ></text>
    </view>
    <view class="equipment-grid">
      <view class="equipment-category" wx:for="{{equipmentStatus}}" wx:key="type">
        <view class="category-name">{{item.typeName}}</view>
        <view class="status-bar">
          <view class="available-count">可用 {{item.available}}</view>
          <view class="total-count">/{{item.total}}</view>
        </view>
        <view class="usage-bar">
          <view class="usage-fill" style="width: {{item.usageRate}}%"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 社区动态预览 -->
  <view class="community-preview">
    <view class="section-title">
      <text>健身动态</text>
      <text class="more-btn" bindtap="navigateTo" data-page="/pages/community/community">进入社区 ></text>
    </view>
    <view class="post-item" wx:for="{{communityPosts}}" wx:key="id" wx:if="{{index < 3}}">
      <image src="{{item.userAvatar}}" class="post-avatar"></image>
      <view class="post-content">
        <view class="post-user">{{item.nickname}}</view>
        <view class="post-text">{{item.content}}</view>
        <view class="post-meta">
          <text class="post-time">{{item.timeAgo}}</text>
          <text class="post-likes">♥ {{item.likeCount}}</text>
        </view>
      </view>
    </view>
  </view>
</view> 