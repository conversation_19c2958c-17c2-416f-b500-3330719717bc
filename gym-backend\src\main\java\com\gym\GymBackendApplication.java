package com.gym;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 健身房微信小程序后端服务启动类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@SpringBootApplication
@MapperScan("com.gym.mapper")
@EnableAsync
@EnableScheduling
public class GymBackendApplication {

    public static void main(String[] args) {
        SpringApplication.run(GymBackendApplication.class, args);
        System.out.println("=================================");
        System.out.println("健身房微信小程序后端服务启动成功！");
        System.out.println("=================================");
    }

} 