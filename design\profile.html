<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 我的</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 83px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 10px;
            font-weight: 500;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-icon {
            font-size: 22px;
            margin-bottom: 4px;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .achievement-badge {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
    </style>
</head>
<body class="bg-gray-50 h-screen overflow-hidden">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 overflow-y-auto" style="height: calc(100vh - 127px);">
        <!-- Profile Header -->
        <div class="gradient-bg px-6 py-8 text-white">
            <div class="flex justify-between items-start mb-6">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" 
                         class="w-20 h-20 rounded-full object-cover border-4 border-white/30 mr-4">
                    <div>
                        <h2 class="text-2xl font-bold mb-1">健身达人</h2>
                        <div class="flex items-center mb-2">
                            <span class="bg-orange-400 text-white px-3 py-1 rounded-full text-xs font-medium mr-2">VIP会员</span>
                            <span class="text-white/80 text-sm">连续打卡 12 天</span>
                        </div>
                        <div class="text-white/80 text-sm">加入 FitFocus 365 天</div>
                    </div>
                </div>
                <button class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <i class="fas fa-cog text-white"></i>
                </button>
            </div>
            
            <!-- Stats -->
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold mb-1">156</div>
                    <div class="text-white/80 text-xs">训练天数</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold mb-1">482</div>
                    <div class="text-white/80 text-xs">番茄钟</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold mb-1">28.5k</div>
                    <div class="text-white/80 text-xs">消耗卡路里</div>
                </div>
            </div>
        </div>

        <div class="px-6 pt-6">
            <!-- Quick Actions -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <button class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 text-center">
                    <i class="fas fa-calendar-check text-indigo-600 text-2xl mb-2"></i>
                    <div class="text-gray-900 font-medium text-sm">我的预约</div>
                    <div class="text-gray-500 text-xs mt-1">3个即将到来</div>
                </button>
                <button class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 text-center">
                    <i class="fas fa-weight text-green-600 text-2xl mb-2"></i>
                    <div class="text-gray-900 font-medium text-sm">体测记录</div>
                    <div class="text-gray-500 text-xs mt-1">查看进度</div>
                </button>
            </div>

            <!-- Achievements -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">成就徽章</h3>
                    <button class="text-indigo-600 text-sm font-medium">查看全部</button>
                </div>
                <div class="grid grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="achievement-badge bg-yellow-100 text-yellow-600 mx-auto mb-2">🏆</div>
                        <div class="text-xs text-gray-600">7天打卡</div>
                    </div>
                    <div class="text-center">
                        <div class="achievement-badge bg-green-100 text-green-600 mx-auto mb-2">💪</div>
                        <div class="text-xs text-gray-600">力量之星</div>
                    </div>
                    <div class="text-center">
                        <div class="achievement-badge bg-blue-100 text-blue-600 mx-auto mb-2">🔥</div>
                        <div class="text-xs text-gray-600">燃脂达人</div>
                    </div>
                    <div class="text-center">
                        <div class="achievement-badge bg-purple-100 text-purple-600 mx-auto mb-2">⏰</div>
                        <div class="text-xs text-gray-600">时间管理</div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">最近活动</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-900 text-sm">完成瑜伽课程</div>
                            <div class="text-gray-500 text-xs">今天 09:30 · 获得20积分</div>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-trophy text-blue-600"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-900 text-sm">获得新徽章</div>
                            <div class="text-gray-500 text-xs">昨天 16:45 · 连续打卡7天</div>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-heart text-purple-600"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-900 text-sm">动态获得点赞</div>
                            <div class="text-gray-500 text-xs">昨天 14:20 · 128个点赞</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Menu Options -->
            <div class="bg-white rounded-xl shadow-sm mb-6">
                <div class="divide-y divide-gray-100">
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-crown text-orange-500 mr-3"></i>
                            <span class="text-gray-900 font-medium">会员权益</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-ticket-alt text-green-500 mr-3"></i>
                            <div>
                                <div class="text-gray-900 font-medium">我的优惠券</div>
                                <div class="text-gray-500 text-xs">3张可用</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-coins text-yellow-500 mr-3"></i>
                            <div>
                                <div class="text-gray-900 font-medium">积分商城</div>
                                <div class="text-gray-500 text-xs">当前积分: 1,280</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-user-friends text-blue-500 mr-3"></i>
                            <span class="text-gray-900 font-medium">邀请好友</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>
            </div>

            <!-- Settings -->
            <div class="bg-white rounded-xl shadow-sm mb-6">
                <div class="divide-y divide-gray-100">
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-bell text-gray-500 mr-3"></i>
                            <span class="text-gray-900 font-medium">消息通知</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-gray-500 mr-3"></i>
                            <span class="text-gray-900 font-medium">隐私设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-question-circle text-gray-500 mr-3"></i>
                            <span class="text-gray-900 font-medium">帮助中心</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button class="w-full flex items-center justify-between p-4 text-left">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-gray-500 mr-3"></i>
                            <span class="text-gray-900 font-medium">关于我们</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span>主页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-dumbbell tab-icon"></i>
            <span>训练</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span>数据</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-users tab-icon"></i>
            <span>社区</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-user tab-icon"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 