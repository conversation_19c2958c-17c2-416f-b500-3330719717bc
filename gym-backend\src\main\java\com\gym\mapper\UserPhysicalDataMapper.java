package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.UserPhysicalData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户体征数据 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface UserPhysicalDataMapper extends BaseMapper<UserPhysicalData> {

    /**
     * 根据用户ID查询体征数据
     */
    List<UserPhysicalData> selectByUserId(@Param("userId") Long userId);

    /**
     * 获取用户最新体征数据
     */
    UserPhysicalData selectLatestByUserId(@Param("userId") Long userId);

    /**
     * 根据日期范围查询体征数据
     */
    List<UserPhysicalData> selectByDateRange(@Param("userId") Long userId, 
                                            @Param("startDate") LocalDate startDate, 
                                            @Param("endDate") LocalDate endDate);

    /**
     * 根据数据来源查询
     */
    List<UserPhysicalData> selectByDataSource(@Param("userId") Long userId, 
                                             @Param("dataSource") Integer dataSource);
} 