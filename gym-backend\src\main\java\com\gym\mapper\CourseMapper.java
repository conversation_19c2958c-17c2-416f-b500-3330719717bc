package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gym.entity.CourseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程信息 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface CourseMapper extends BaseMapper<CourseInfo> {

    /**
     * 根据课程类型查询课程列表
     */
    List<CourseInfo> selectByType(@Param("courseType") Integer courseType);

    /**
     * 查询热门课程
     */
    List<CourseInfo> selectHotCourses(@Param("limit") Integer limit);

    /**
     * 根据教练ID查询课程
     */
    List<CourseInfo> selectByInstructorId(@Param("instructorId") Long instructorId);

    /**
     * 分页查询课程（带搜索条件）
     */
    IPage<CourseInfo> selectPageWithCondition(Page<CourseInfo> page, 
                                             @Param("courseType") Integer courseType,
                                             @Param("difficultyLevel") Integer difficultyLevel,
                                             @Param("keyword") String keyword);
} 