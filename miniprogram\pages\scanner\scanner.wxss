/* 扫码页面样式 */
.container {
  min-height: 100vh;
  background: #000000;
  position: relative;
  overflow: hidden;
}

/* 页面标题 */
.page-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 32rpx 20rpx;
  margin-top: var(--status-bar-height, 44px);
  background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent);
}

.back-btn, .help-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.back-btn:active, .help-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.back-icon, .help-icon {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 扫码区域 */
.scanner-section {
  position: relative;
  width: 100%;
  height: 100vh;
}

.scanner-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.camera-view {
  width: 100%;
  height: 100%;
}

/* 扫描框 */
.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 500rpx;
  height: 500rpx;
  margin-top: -250rpx;
  margin-left: -250rpx;
  border: 4rpx solid transparent;
}

.frame-corner {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #667eea;
}

.corner-tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.corner-tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.corner-bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.corner-br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #667eea, transparent);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% {
    top: 0;
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

/* 扫描提示 */
.scan-tip {
  position: absolute;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  padding: 16rpx 32rpx;
  backdrop-filter: blur(10px);
}

.tip-text {
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
}

/* 相机控制 */
.camera-controls {
  position: absolute;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 40rpx;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.control-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.scan-btn {
  background: rgba(102, 126, 234, 0.8);
}

.control-icon {
  font-size: 32rpx;
}

.control-text {
  color: #ffffff;
  font-size: 20rpx;
  text-align: center;
}

/* 摄像头占位 */
.camera-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
  background: #1f2937;
}

.placeholder-icon {
  font-size: 120rpx;
  color: #6b7280;
}

.placeholder-text {
  font-size: 32rpx;
  color: #9ca3af;
  text-align: center;
}

.enable-camera-btn {
  padding: 20rpx 40rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 20rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.2s ease;
}

.enable-camera-btn:active {
  transform: scale(0.95);
  background: #5a67d8;
}

/* 功能区域 */
.function-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 60vh;
  overflow-y: auto;
  z-index: 50;
}

/* 快速功能 */
.quick-functions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  padding: 40rpx 32rpx 32rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 16rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.function-item:active {
  transform: scale(0.95);
  background: #e5e7eb;
}

.function-icon {
  font-size: 48rpx;
}

.function-text {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
  text-align: center;
}

/* 扫码历史 */
.scan-history {
  padding: 0 32rpx 40rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.history-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.clear-history {
  padding: 12rpx 20rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
}

.clear-text {
  font-size: 24rpx;
  color: #6b7280;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.history-item:active {
  transform: translateY(2rpx);
  background: #f3f4f6;
}

.history-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  flex-shrink: 0;
}

.history-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.history-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

.history-desc {
  font-size: 24rpx;
  color: #6b7280;
}

.history-time {
  font-size: 20rpx;
  color: #9ca3af;
}

.history-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.history-status.success {
  background: #f0fdf4;
}

.history-status.success .status-text {
  color: #16a34a;
}

.history-status.error {
  background: #fef2f2;
}

.history-status.error .status-text {
  color: #dc2626;
}

.empty-history {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 弹窗通用样式 */
.manual-input-modal,
.device-modal,
.help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.manual-input-modal.show,
.device-modal.show,
.help-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.modal-body {
  padding: 40rpx;
}

.modal-actions {
  display: flex;
  gap: 24rpx;
  padding: 0 40rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #667eea;
  color: #ffffff;
}

.modal-btn.secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.modal-btn:active {
  transform: scale(0.98);
}

.modal-btn:disabled {
  opacity: 0.5;
  transform: none !important;
}

/* 手动输入特定样式 */
.input-section {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.device-input {
  width: 100%;
  height: 88rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.recent-codes {
  margin-top: 24rpx;
}

.recent-title {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
}

.codes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.code-item {
  padding: 12rpx 20rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.code-item:active {
  background: #e5e7eb;
}

.code-text {
  font-size: 24rpx;
  color: #374151;
}

/* 设备信息特定样式 */
.device-info {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.device-main-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.device-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  flex-shrink: 0;
}

.device-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.device-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.device-location {
  font-size: 24rpx;
  color: #6b7280;
}

.device-code {
  font-size: 20rpx;
  color: #9ca3af;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.device-status.available {
  background: #f0fdf4;
}

.device-status.available .status-text {
  color: #16a34a;
}

.device-status.busy {
  background: #fef2f2;
}

.device-status.busy .status-text {
  color: #dc2626;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: currentColor;
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
}

.device-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8fafc;
  border-radius: 12rpx;
}

.feature-name {
  font-size: 24rpx;
  color: #374151;
}

.feature-value {
  font-size: 24rpx;
  font-weight: 500;
  color: #1f2937;
}

.device-usage {
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
}

.usage-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

.usage-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.usage-item {
  text-align: center;
}

.usage-label {
  display: block;
  font-size: 20rpx;
  color: #9ca3af;
  margin-bottom: 8rpx;
}

.usage-value {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 帮助内容特定样式 */
.help-content {
  max-height: 500rpx;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 32rpx;
}

.help-section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.help-text {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .scan-frame {
    width: 400rpx;
    height: 400rpx;
    margin-top: -200rpx;
    margin-left: -200rpx;
  }
  
  .quick-functions {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .camera-controls {
    gap: 20rpx;
  }
  
  .control-btn {
    min-width: 100rpx;
    padding: 16rpx;
  }
} 