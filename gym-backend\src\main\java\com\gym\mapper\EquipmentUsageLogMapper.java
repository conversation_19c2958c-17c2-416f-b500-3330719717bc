package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.EquipmentUsageLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备使用记录 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface EquipmentUsageLogMapper extends BaseMapper<EquipmentUsageLog> {

    /**
     * 根据用户ID查询使用记录
     */
    List<EquipmentUsageLog> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据设备ID查询使用记录
     */
    List<EquipmentUsageLog> selectByEquipmentId(@Param("equipmentId") Long equipmentId);

    /**
     * 查询用户正在使用的设备
     */
    EquipmentUsageLog selectInProgressByUserId(@Param("userId") Long userId);

    /**
     * 查询设备当前使用记录
     */
    EquipmentUsageLog selectInProgressByEquipmentId(@Param("equipmentId") Long equipmentId);

    /**
     * 根据时间范围查询使用记录
     */
    List<EquipmentUsageLog> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户使用时长
     */
    Integer sumDurationByUserId(@Param("userId") Long userId, 
                               @Param("startTime") LocalDateTime startTime, 
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 统计设备使用次数
     */
    Integer countUsageByEquipmentId(@Param("equipmentId") Long equipmentId, 
                                   @Param("startTime") LocalDateTime startTime, 
                                   @Param("endTime") LocalDateTime endTime);
} 