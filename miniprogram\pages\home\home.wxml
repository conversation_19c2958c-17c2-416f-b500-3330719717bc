<!--主页 - 专注健身界面-->
<view class="container">
  <!-- 顶部问候 -->
  <view class="header">
    <view class="greeting">
      <text class="title">专注健身</text>
      <text class="subtitle">今天已完成 {{completedToday}} 个番茄钟</text>
    </view>
    <view class="avatar-container">
      <image class="avatar" src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"></image>
    </view>
  </view>

  <!-- 番茄钟计时器 -->
  <view class="pomodoro-section">
    <view class="pomodoro-circle {{timerRunning ? 'running' : ''}}">
      <view class="pomodoro-inner">
        <text class="timer-display">{{formattedTime}}</text>
        <text class="timer-label">{{currentSession === 'focus' ? '专注训练' : '休息时间'}}</text>
        <view class="timer-controls">
          <button class="control-btn {{timerRunning ? 'pause' : 'play'}}" 
                  bindtap="toggleTimer">
            <text class="iconfont">{{timerRunning ? '⏸' : '▶'}}</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日目标 -->
  <view class="goals-section">
    <view class="section-header">
      <text class="section-title">今日目标</text>
      <text class="progress-text">{{completedGoals}}/{{totalGoals}} 完成</text>
    </view>
    <view class="goals-list">
      <view wx:for="{{todayGoals}}" wx:key="id" 
            class="goal-item {{item.completed ? 'completed' : ''}}">
        <view class="goal-indicator"></view>
        <text class="goal-text">{{item.name}} - {{item.duration}}分钟</text>
        <text wx:if="{{item.completed}}" class="goal-check">✓</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <button class="action-btn scan-btn" bindtap="navigateToScanner">
      <text class="btn-icon">📷</text>
      <text class="btn-text">扫码健身</text>
    </button>
    <button class="action-btn booking-btn" bindtap="navigateToWorkout">
      <text class="btn-icon">📅</text>
      <text class="btn-text">预约课程</text>
    </button>
  </view>

  <!-- 今日推荐课程 -->
  <view class="recommended-section" wx:if="{{recommendedCourse}}">
    <view class="section-header">
      <text class="section-title">今日推荐</text>
      <text class="more-btn" bindtap="navigateToWorkout">查看更多</text>
    </view>
    <view class="course-card" bindtap="navigateToCourseDetail" data-id="{{recommendedCourse.id}}">
      <image class="course-image" src="{{recommendedCourse.image}}" mode="aspectFill"></image>
      <view class="course-info">
        <text class="course-name">{{recommendedCourse.name}}</text>
        <text class="course-instructor">{{recommendedCourse.instructor}} · {{recommendedCourse.room}}</text>
        <view class="course-meta">
          <text class="course-time">{{recommendedCourse.time}}</text>
          <text class="course-spots">{{recommendedCourse.availableSpots}}/{{recommendedCourse.totalSpots}}人</text>
        </view>
      </view>
      <view class="course-action">
        <button class="book-btn" bindtap="bookCourse" data-id="{{recommendedCourse.id}}">预约</button>
      </view>
    </view>
  </view>

  <!-- 设备状态概览 -->
  <view class="equipment-overview">
    <view class="section-header">
      <text class="section-title">设备状态</text>
    </view>
    <view class="equipment-grid">
      <view wx:for="{{equipmentList}}" wx:key="type" 
            class="equipment-item {{item.status}}" 
            bindtap="navigateToWorkout">
        <text class="equipment-icon">{{item.icon}}</text>
        <text class="equipment-name">{{item.name}}</text>
        <text class="equipment-count">{{item.available}}/{{item.total}} 台</text>
        <view class="status-indicator {{item.status}}"></view>
      </view>
    </view>
  </view>

  <!-- 浮动成就提示 -->
  <view class="achievement-toast {{showAchievement ? 'show' : ''}}" wx:if="{{achievementData}}">
    <view class="toast-content">
      <text class="achievement-icon">🏆</text>
      <view class="achievement-text">
        <text class="achievement-title">{{achievementData.title}}</text>
        <text class="achievement-desc">{{achievementData.description}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 番茄钟完成弹窗 -->
<view class="pomodoro-complete-modal {{showCompleteModal ? 'show' : ''}}" wx:if="{{showCompleteModal}}">
  <view class="modal-mask" bindtap="closeCompleteModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">🎉 专注时间完成！</text>
    </view>
    <view class="modal-body">
      <text class="modal-text">恭喜完成 {{sessionDuration}} 分钟的专注训练</text>
      <text class="modal-subtext">消耗卡路里: {{sessionCalories}}卡</text>
    </view>
    <view class="modal-actions">
      <button class="modal-btn secondary" bindtap="startBreak">开始休息</button>
      <button class="modal-btn primary" bindtap="continueTraining">继续训练</button>
    </view>
  </view>
</view> 