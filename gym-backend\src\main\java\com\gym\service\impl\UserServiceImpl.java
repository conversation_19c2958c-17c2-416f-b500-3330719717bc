package com.gym.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gym.entity.UserInfo;
import com.gym.mapper.UserMapper;
import com.gym.service.UserService;
import com.gym.util.WechatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息 Service 实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserInfo> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private WechatUtil wechatUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserInfo wechatLogin(String code, String nickname, String avatar) {
        // 调用微信接口获取openid
        WechatUtil.WechatLoginResult loginResult = wechatUtil.code2Session(code);
        
        if (!loginResult.isSuccess()) {
            log.error("微信登录失败: {}", loginResult.getErrmsg());
            throw new RuntimeException("微信登录失败: " + loginResult.getErrmsg());
        }

        String openid = loginResult.getOpenid();
        String unionid = loginResult.getUnionid();

        // 查询用户是否已存在
        UserInfo existUser = userMapper.selectByOpenid(openid);
        
        if (existUser != null) {
            // 用户已存在，更新登录时间和基本信息
            existUser.setLastLoginTime(LocalDateTime.now());
            if (nickname != null && !nickname.trim().isEmpty()) {
                existUser.setNickname(nickname);
            }
            if (avatar != null && !avatar.trim().isEmpty()) {
                existUser.setAvatar(avatar);
            }
            userMapper.updateById(existUser);
            log.info("用户登录: {}", openid);
            return existUser;
        } else {
            // 新用户注册
            UserInfo newUser = new UserInfo();
            newUser.setOpenid(openid);
            newUser.setUnionId(unionid);
            newUser.setNickname(nickname);
            newUser.setAvatar(avatar);
            newUser.setMemberLevel(1); // 默认铜卡会员
            newUser.setTotalPoints(0);
            newUser.setAvailablePoints(0);
            newUser.setStatus(1); // 正常状态
            newUser.setLastLoginTime(LocalDateTime.now());
            
            userMapper.insert(newUser);
            log.info("新用户注册: {}", openid);
            return newUser;
        }
    }

    @Override
    public UserInfo getUserByOpenid(String openid) {
        return userMapper.selectByOpenid(openid);
    }

    @Override
    public UserInfo getUserById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserInfo(UserInfo userInfo) {
        if (userInfo == null || userInfo.getId() == null) {
            return false;
        }
        return userMapper.updateById(userInfo) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindPhone(Long userId, String phone) {
        if (userId == null || phone == null || phone.trim().isEmpty()) {
            return false;
        }

        // 检查手机号是否已被其他用户绑定
        UserInfo existUser = userMapper.selectByPhone(phone);
        if (existUser != null && !existUser.getId().equals(userId)) {
            throw new RuntimeException("该手机号已被其他用户绑定");
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setId(userId);
        userInfo.setPhone(phone);
        return userMapper.updateById(userInfo) > 0;
    }

    @Override
    public boolean updateLastLoginTime(Long userId) {
        return userMapper.updateLastLoginTime(userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUserPoints(Long userId, Integer points, String reason) {
        if (userId == null || points == null || points <= 0) {
            return false;
        }

        UserInfo user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        Integer newTotalPoints = (user.getTotalPoints() != null ? user.getTotalPoints() : 0) + points;
        Integer newAvailablePoints = (user.getAvailablePoints() != null ? user.getAvailablePoints() : 0) + points;

        int result = userMapper.updateUserPoints(userId, newTotalPoints, newAvailablePoints);
        
        if (result > 0) {
            log.info("用户 {} 增加积分 {}，原因: {}", userId, points, reason);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductUserPoints(Long userId, Integer points, String reason) {
        if (userId == null || points == null || points <= 0) {
            return false;
        }

        UserInfo user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        Integer availablePoints = user.getAvailablePoints() != null ? user.getAvailablePoints() : 0;
        if (availablePoints < points) {
            throw new RuntimeException("用户可用积分不足");
        }

        Integer newAvailablePoints = availablePoints - points;
        int result = userMapper.updateUserPoints(userId, user.getTotalPoints(), newAvailablePoints);
        
        if (result > 0) {
            log.info("用户 {} 扣除积分 {}，原因: {}", userId, points, reason);
            return true;
        }
        return false;
    }

    @Override
    public boolean isValidMember(Long userId) {
        UserInfo user = userMapper.selectById(userId);
        return user != null && user.isValidMember();
    }

    @Override
    public boolean isVipMember(Long userId) {
        UserInfo user = userMapper.selectById(userId);
        return user != null && user.isVipMember();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upgradeMemberLevel(Long userId, Integer newLevel) {
        if (userId == null || newLevel == null || newLevel < 1 || newLevel > 3) {
            return false;
        }

        UserInfo user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        user.setMemberLevel(newLevel);
        // 根据等级设置会员过期时间（这里简化处理，设置为一年后）
        user.setMemberExpireDate(LocalDate.now().plusYears(1));
        
        boolean result = userMapper.updateById(user) > 0;
        if (result) {
            log.info("用户 {} 会员等级升级为 {}", userId, newLevel);
        }
        return result;
    }
} 