{"description": "健身房微信小程序项目配置", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "2.32.3", "appid": "wxe123456789abcdef", "projectname": "gym-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}