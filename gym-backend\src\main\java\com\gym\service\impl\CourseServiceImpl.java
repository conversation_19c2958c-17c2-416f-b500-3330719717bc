package com.gym.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gym.entity.*;
import com.gym.mapper.*;
import com.gym.service.CourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, CourseInfo> implements CourseService {

    @Autowired
    private CourseMapper courseMapper;

    @Autowired
    private CourseScheduleMapper courseScheduleMapper;

    @Autowired
    private CourseBookingMapper courseBookingMapper;

    @Autowired
    private InstructorMapper instructorMapper;

    @Override
    public IPage<CourseInfo> getCourseList(Page<CourseInfo> page, Integer courseType, 
                                          Integer difficultyLevel, String keyword) {
        return courseMapper.selectPageWithCondition(page, courseType, difficultyLevel, keyword);
    }

    @Override
    public List<CourseInfo> getCoursesByType(Integer courseType) {
        return courseMapper.selectByType(courseType);
    }

    @Override
    public List<CourseInfo> getHotCourses(Integer limit) {
        return courseMapper.selectHotCourses(limit);
    }

    @Override
    public CourseInfo getCourseDetail(Long courseId) {
        return courseMapper.selectById(courseId);
    }

    @Override
    public List<CourseSchedule> getCourseSchedules(Long courseId, LocalDate startDate, LocalDate endDate) {
        QueryWrapper<CourseSchedule> wrapper = new QueryWrapper<>();
        wrapper.eq("course_id", courseId)
               .ge("start_time", startDate.atStartOfDay())
               .le("start_time", endDate.atTime(23, 59, 59))
               .eq("status", 1)
               .orderByAsc("start_time");
        return courseScheduleMapper.selectList(wrapper);
    }

    @Override
    public List<CourseSchedule> getAvailableSchedules(LocalDate startDate, LocalDate endDate) {
        return courseScheduleMapper.selectAvailableSchedules(startDate, endDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bookCourse(Long userId, Long scheduleId) {
        // 检查排期是否存在
        CourseSchedule schedule = courseScheduleMapper.selectById(scheduleId);
        if (schedule == null) {
            throw new RuntimeException("课程排期不存在");
        }

        // 检查是否可以预约
        if (!schedule.canBook()) {
            throw new RuntimeException("课程不可预约");
        }

        // 检查用户是否已预约
        CourseBooking existBooking = courseBookingMapper.selectByUserAndSchedule(userId, scheduleId);
        if (existBooking != null) {
            throw new RuntimeException("您已预约过该课程");
        }

        // 检查是否已满员
        if (schedule.getCurrentBookings() >= schedule.getMaxCapacity()) {
            throw new RuntimeException("课程已满员");
        }

        // 创建预约记录
        CourseBooking booking = new CourseBooking();
        booking.setScheduleId(scheduleId);
        booking.setUserId(userId);
        booking.setBookingStatus(1); // 已预约
        booking.setPaymentStatus(0); // 未支付
        booking.setActualFee(schedule.getActualFee());

        int insertResult = courseBookingMapper.insert(booking);
        
        // 更新排期预约人数
        int updateResult = courseScheduleMapper.updateCurrentBookings(scheduleId, 1);
        
        if (insertResult > 0 && updateResult > 0) {
            log.info("用户 {} 成功预约课程排期 {}", userId, scheduleId);
            return true;
        }
        
        throw new RuntimeException("预约失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelBooking(Long userId, Long bookingId) {
        CourseBooking booking = courseBookingMapper.selectById(bookingId);
        if (booking == null || !booking.getUserId().equals(userId)) {
            throw new RuntimeException("预约记录不存在");
        }

        if (!booking.canCancel()) {
            throw new RuntimeException("该预约不可取消");
        }

        // 获取排期信息
        CourseSchedule schedule = courseScheduleMapper.selectById(booking.getScheduleId());
        if (schedule != null && !schedule.canCancel()) {
            throw new RuntimeException("超过取消时间，无法取消");
        }

        // 更新预约状态
        booking.setBookingStatus(4); // 已取消
        booking.setCancelTime(LocalDateTime.now());
        booking.setCancelReason("用户主动取消");

        int updateResult = courseBookingMapper.updateById(booking);
        
        // 减少排期预约人数
        int decrementResult = courseScheduleMapper.updateCurrentBookings(booking.getScheduleId(), -1);
        
        if (updateResult > 0 && decrementResult > 0) {
            log.info("用户 {} 成功取消预约 {}", userId, bookingId);
            return true;
        }
        
        throw new RuntimeException("取消失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean checkIn(Long userId, Long bookingId) {
        CourseBooking booking = courseBookingMapper.selectById(bookingId);
        if (booking == null || !booking.getUserId().equals(userId)) {
            throw new RuntimeException("预约记录不存在");
        }

        if (!booking.canCheckIn()) {
            throw new RuntimeException("无法签到");
        }

        // 更新签到状态
        booking.setBookingStatus(2); // 已签到
        booking.setCheckInTime(LocalDateTime.now());

        int result = courseBookingMapper.updateById(booking);
        
        if (result > 0) {
            log.info("用户 {} 成功签到，预约ID: {}", userId, bookingId);
            return true;
        }
        
        return false;
    }

    @Override
    public List<CourseBooking> getUserBookings(Long userId) {
        return courseBookingMapper.selectByUserId(userId);
    }

    @Override
    public List<CourseBooking> getUserValidBookings(Long userId) {
        return courseBookingMapper.selectValidBookingsByUserId(userId);
    }

    @Override
    public List<InstructorInfo> getInstructors() {
        return instructorMapper.selectAvailableInstructors();
    }

    @Override
    public InstructorInfo getInstructorDetail(Long instructorId) {
        return instructorMapper.selectById(instructorId);
    }

    @Override
    public List<InstructorInfo> getInstructorsBySpecialty(String specialty) {
        return instructorMapper.selectBySpecialties(specialty);
    }

    @Override
    public List<InstructorInfo> getTopRatedInstructors(Integer limit) {
        return instructorMapper.selectTopRatedInstructors(limit);
    }
} 