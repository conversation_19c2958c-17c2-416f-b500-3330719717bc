<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 主页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .pomodoro-circle {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            background: conic-gradient(#ff6b6b 0deg 90deg, #f8f9fa 90deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .pomodoro-inner {
            width: 220px;
            height: 220px;
            border-radius: 50%;
            background: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 83px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 10px;
            font-weight: 500;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-icon {
            font-size: 22px;
            margin-bottom: 4px;
        }
        .quick-action {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body class="bg-gray-100 h-screen overflow-hidden">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 px-6 pt-6 pb-4" style="height: calc(100vh - 127px);">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-white">专注健身</h1>
                <p class="text-white/80 text-sm mt-1">今天已完成 3 个番茄钟</p>
            </div>
            <div class="w-12 h-12 rounded-full border-2 border-white/30 flex items-center justify-center">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face" 
                     class="w-10 h-10 rounded-full object-cover" alt="用户头像">
            </div>
        </div>

        <!-- Pomodoro Timer -->
        <div class="flex justify-center mb-8">
            <div class="pomodoro-circle">
                <div class="pomodoro-inner">
                    <div class="text-3xl font-bold text-gray-800 mb-1">25:00</div>
                    <div class="text-sm text-gray-600 mb-3">专注训练</div>
                    <button class="w-16 h-16 bg-gradient-to-r from-red-400 to-pink-500 rounded-full flex items-center justify-center text-white shadow-lg">
                        <i class="fas fa-play text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Today's Goal -->
        <div class="bg-white/20 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/30">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-white font-semibold">今日目标</h3>
                <span class="text-white/80 text-sm">3/4 完成</span>
            </div>
            <div class="space-y-3">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-400 rounded-full mr-3"></div>
                    <span class="text-white text-sm">胸肌训练 - 25分钟</span>
                    <i class="fas fa-check text-green-400 ml-auto"></i>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-400 rounded-full mr-3"></div>
                    <span class="text-white text-sm">有氧运动 - 25分钟</span>
                    <i class="fas fa-check text-green-400 ml-auto"></i>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-400 rounded-full mr-3"></div>
                    <span class="text-white text-sm">拉伸放松 - 25分钟</span>
                    <i class="fas fa-check text-green-400 ml-auto"></i>
                </div>
                <div class="flex items-center opacity-60">
                    <div class="w-4 h-4 bg-white/30 rounded-full mr-3"></div>
                    <span class="text-white text-sm">核心训练 - 25分钟</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-2 gap-4">
            <button class="quick-action rounded-xl p-4 text-center">
                <i class="fas fa-qrcode text-white text-2xl mb-2"></i>
                <div class="text-white text-sm font-medium">扫码健身</div>
            </button>
            <button class="quick-action rounded-xl p-4 text-center">
                <i class="fas fa-calendar-alt text-white text-2xl mb-2"></i>
                <div class="text-white text-sm font-medium">预约课程</div>
            </button>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home tab-icon"></i>
            <span>主页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-dumbbell tab-icon"></i>
            <span>训练</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span>数据</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-users tab-icon"></i>
            <span>社区</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 