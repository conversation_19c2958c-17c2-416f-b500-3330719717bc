const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {
      id: '202312001',
      nickname: 'FitFocus用户',
      avatar: '/assets/images/default-avatar.png',
      memberLevel: '银牌',
      isVip: true,
      phone: '138****8888',
      gender: 1, // 0: 女, 1: 男
      birthday: '1990-01-01',
      totalPoints: 2500,
      availablePoints: 850
    },
    
    // 用户统计数据
    userStats: [
      { key: 'workouts', icon: '🏃‍♂️', value: '42', label: '训练次数' },
      { key: 'days', icon: '📅', value: '15', label: '连续天数' },
      { key: 'calories', icon: '🔥', value: '8450', label: '消耗卡路里' },
      { key: 'time', icon: '⏰', value: '32.5', label: '训练时长(小时)' }
    ],
    
    // 功能菜单
    menuItems: [
      { key: 'edit_profile', icon: '👤', title: '编辑资料', extra: '' },
      { key: 'body_data', icon: '📊', title: '体征数据', extra: '最新：68.5kg' },
      { key: 'workout_plan', icon: '📋', title: '训练计划', extra: '5个计划' },
      { key: 'achievements', icon: '🏆', title: '成就徽章', extra: '12个' },
      { key: 'points_history', icon: '💎', title: '积分记录', extra: '850积分' },
      { key: 'favorites', icon: '❤️', title: '我的收藏', extra: '18个' },
      { key: 'help', icon: '❓', title: '帮助中心', extra: '' },
      { key: 'feedback', icon: '📝', title: '意见反馈', extra: '' }
    ],
    
    // 设置相关
    showSettingsModal: false,
    settingsItems: [
      { key: 'notifications', icon: '🔔', title: '消息通知', type: 'switch', value: true },
      { key: 'privacy', icon: '🔒', title: '隐私设置', type: 'link' },
      { key: 'language', icon: '🌐', title: '语言设置', type: 'link', extra: '简体中文' },
      { key: 'cache', icon: '🗂️', title: '清理缓存', type: 'link' },
      { key: 'about', icon: 'ℹ️', title: '关于我们', type: 'link' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Profile page loaded');
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新用户数据
    this.loadUserInfo();
    this.loadUserStats();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 从全局数据或缓存加载用户信息
    this.loadUserInfo();
    this.loadUserStats();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    try {
      // 从缓存获取用户信息
      const savedUserInfo = wx.getStorageSync('userInfo');
      if (savedUserInfo) {
        this.setData({
          userInfo: { ...this.data.userInfo, ...savedUserInfo }
        });
      }
      
      // 从store获取用户信息
      const state = store.getState();
      if (state.user) {
        this.setData({
          userInfo: { ...this.data.userInfo, ...state.user }
        });
      }
    } catch (error) {
      console.error('Failed to load user info:', error);
    }
  },

  /**
   * 加载用户统计数据
   */
  loadUserStats() {
    try {
      const savedStats = wx.getStorageSync('userStats');
      if (savedStats) {
        this.setData({ userStats: savedStats });
      }
    } catch (error) {
      console.error('Failed to load user stats:', error);
    }
  },

  /**
   * 头像相关
   */
  onChangeAvatar() {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['album'] : ['camera'];
        
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType,
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            
            // 显示加载状态
            wx.showLoading({ title: '上传中...' });
            
            // 模拟上传过程
            setTimeout(() => {
              this.setData({
                'userInfo.avatar': tempFilePath
              });
              
              this.saveUserInfo();
              
              wx.hideLoading();
              wx.showToast({
                title: '头像更新成功',
                icon: 'success'
              });
            }, 2000);
          }
        });
      }
    });
  },

  /**
   * 统计数据相关
   */
  onViewAllStats() {
    wx.navigateTo({
      url: '/pages/analytics/analytics?tab=overview'
    });
  },

  /**
   * 菜单项点击
   */
  onMenuItemTap(e) {
    const { item } = e.currentTarget.dataset;
    
    switch (item.key) {
      case 'edit_profile':
        this.onEditProfile();
        break;
      case 'body_data':
        wx.navigateTo({
          url: '/pages/body-data/body-data'
        });
        break;
      case 'workout_plan':
        wx.navigateTo({
          url: '/pages/workout/workout?tab=plans'
        });
        break;
      case 'achievements':
        wx.navigateTo({
          url: '/pages/analytics/analytics?tab=achievements'
        });
        break;
      case 'points_history':
        this.onPointsHistory();
        break;
      case 'favorites':
        this.onFavorites();
        break;
      case 'help':
        this.onHelp();
        break;
      case 'feedback':
        this.onFeedback();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  onEditProfile() {
    wx.showToast({
      title: '编辑功能开发中',
      icon: 'none'
    });
  },

  onPointsHistory() {
    wx.showModal({
      title: '积分记录',
      content: '当前可用积分：850分\n累计获得积分：2500分\n\n最近记录：\n• 完成训练 +50分\n• 连续打卡7天 +100分\n• 分享动态 +20分',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  onFavorites() {
    wx.showToast({
      title: '收藏功能开发中',
      icon: 'none'
    });
  },

  onHelp() {
    wx.showModal({
      title: '帮助中心',
      content: '• 如何开始训练？\n• 如何预约课程？\n• 会员权益说明\n• 积分规则介绍\n• 设备使用指南',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  onFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '请通过以下方式联系我们：\n\n客服电话：400-123-4567\n微信客服：fitfocus2024\n邮箱：<EMAIL>',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 设置相关
   */
  onSettings() {
    this.setData({ showSettingsModal: true });
  },

  onCloseSettingsModal() {
    this.setData({ showSettingsModal: false });
  },

  onSettingToggle(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新设置项
    const settingsItems = [...this.data.settingsItems];
    const targetIndex = settingsItems.findIndex(item => item.key === key);
    
    if (targetIndex !== -1) {
      settingsItems[targetIndex].value = value;
      this.setData({ settingsItems });
      
      // 保存设置
      this.saveSettings();
      
      // 处理特定设置
      switch (key) {
        case 'notifications':
          if (value) {
            this.enableNotifications();
          } else {
            this.disableNotifications();
          }
          break;
      }
    }
  },

  enableNotifications() {
    wx.showToast({
      title: '通知已开启',
      icon: 'success'
    });
  },

  disableNotifications() {
    wx.showToast({
      title: '通知已关闭',
      icon: 'none'
    });
  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performLogout();
        }
      }
    });
  },

  performLogout() {
    wx.showLoading({ title: '退出中...' });
    
    // 清除本地数据
    try {
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('token');
      wx.removeStorageSync('userStats');
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
    
    // 清除store数据
    store.setState({ user: null });
    
    setTimeout(() => {
      wx.hideLoading();
      
      // 重定向到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }, 1000);
  },

  /**
   * 保存用户信息
   */
  saveUserInfo() {
    try {
      wx.setStorageSync('userInfo', this.data.userInfo);
      
      // 更新store
      store.setState({ user: this.data.userInfo });
    } catch (error) {
      console.error('Failed to save user info:', error);
    }
  },

  /**
   * 保存设置
   */
  saveSettings() {
    try {
      const settingsData = {};
      this.data.settingsItems.forEach(item => {
        if (item.type === 'switch') {
          settingsData[item.key] = item.value;
        }
      });
      
      wx.setStorageSync('appSettings', settingsData);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  },

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const savedSettings = wx.getStorageSync('appSettings');
      if (savedSettings) {
        const settingsItems = [...this.data.settingsItems];
        
        settingsItems.forEach(item => {
          if (item.type === 'switch' && savedSettings.hasOwnProperty(item.key)) {
            item.value = savedSettings[item.key];
          }
        });
        
        this.setData({ settingsItems });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('Refreshing profile page...');
    
    // 重新加载数据
    this.loadUserInfo();
    this.loadUserStats();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { userInfo } = this.data;
    return {
      title: `${userInfo.nickname}邀请你一起使用FitFocus健身！`,
      path: '/pages/home/<USER>',
      imageUrl: '/assets/images/profile-share.jpg'
    };
  },

  /**
   * 页面隐藏时保存数据
   */
  onHide() {
    this.saveUserInfo();
    this.saveSettings();
  }
}); 