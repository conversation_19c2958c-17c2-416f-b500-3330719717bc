package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 教练信息实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("instructor_info")
public class InstructorInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("name")
    private String name;

    @TableField("gender")
    private Integer gender;

    @TableField("phone")
    private String phone;

    @TableField("avatar")
    private String avatar;

    @TableField("specialties")
    private String specialties;

    @TableField("experience_years")
    private Integer experienceYears;

    @TableField("certifications")
    private String certifications;

    @TableField("introduction")
    private String introduction;

    @TableField("hourly_rate")
    private BigDecimal hourlyRate;

    @TableField("rating")
    private BigDecimal rating;

    @TableField("total_students")
    private Integer totalStudents;

    @TableField("status")
    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取性别名称
     */
    public String getGenderName() {
        if (gender == null) {
            return "未知";
        }
        switch (gender) {
            case 1: return "男";
            case 2: return "女";
            default: return "未知";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "在职";
        }
        switch (status) {
            case 1: return "在职";
            case 2: return "休假";
            case 3: return "离职";
            default: return "在职";
        }
    }

    /**
     * 判断是否可预约
     */
    public boolean isAvailable() {
        return status != null && status == 1;
    }

    /**
     * 获取经验等级
     */
    public String getExperienceLevel() {
        if (experienceYears == null) {
            return "新手教练";
        }
        if (experienceYears < 2) {
            return "新手教练";
        } else if (experienceYears < 5) {
            return "资深教练";
        } else {
            return "专家级教练";
        }
    }
} 