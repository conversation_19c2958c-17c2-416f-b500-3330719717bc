<!-- 训练页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的训练</text>
    <view class="header-actions">
      <view class="action-btn" bindtap="onAddWorkout">
        <text class="btn-icon">➕</text>
      </view>
    </view>
  </view>

  <!-- 当前训练状态 -->
  <view class="current-workout" wx:if="{{currentWorkout.isActive}}">
    <view class="workout-header">
      <text class="workout-title">{{currentWorkout.name}}</text>
      <view class="workout-timer">
        <text class="timer-text">{{currentWorkout.duration}}</text>
      </view>
    </view>
    
    <view class="workout-progress">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{currentWorkout.progress}}%"></view>
      </view>
      <text class="progress-text">{{currentWorkout.completedExercises}}/{{currentWorkout.totalExercises}} 个动作</text>
    </view>
    
    <view class="workout-actions">
      <button class="action-btn pause" bindtap="onPauseWorkout">
        <text class="btn-icon">⏸️</text>
        <text class="btn-text">暂停</text>
      </button>
      <button class="action-btn stop" bindtap="onStopWorkout">
        <text class="btn-icon">⏹️</text>
        <text class="btn-text">结束</text>
      </button>
    </view>
  </view>

  <!-- 快速开始训练 -->
  <view class="quick-start" wx:else>
    <view class="section-header">
      <text class="section-title">快速开始</text>
    </view>
    
    <scroll-view class="workout-templates" scroll-x="true">
      <view class="template-list">
        <view 
          class="template-card"
          wx:for="{{workoutTemplates}}"
          wx:key="id"
          data-template="{{item}}"
          bindtap="onStartTemplate"
        >
          <view class="template-icon">{{item.icon}}</view>
          <text class="template-name">{{item.name}}</text>
          <text class="template-duration">{{item.duration}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="section-header">
      <text class="section-title">本周统计</text>
      <view class="view-all" bindtap="onViewAllStats">
        <text class="view-all-text">查看详细</text>
        <text class="arrow">〉</text>
      </view>
    </view>
    
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{weeklyStats.workouts}}</text>
        <text class="stat-label">训练次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{weeklyStats.duration}}</text>
        <text class="stat-label">训练时长</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{weeklyStats.calories}}</text>
        <text class="stat-label">消耗卡路里</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{weeklyStats.streak}}</text>
        <text class="stat-label">连续天数</text>
      </view>
    </view>
  </view>

  <!-- 今日训练计划 -->
  <view class="today-plan">
    <view class="section-header">
      <text class="section-title">今日计划</text>
      <view class="edit-plan" bindtap="onEditPlan">
        <text class="edit-text">编辑</text>
      </view>
    </view>
    
    <view class="plan-list">
      <view 
        class="plan-item {{item.completed ? 'completed' : ''}}"
        wx:for="{{todayPlan}}"
        wx:key="id"
        data-index="{{index}}"
        bindtap="onTogglePlanItem"
      >
        <view class="plan-indicator">
          <text class="indicator-icon" wx:if="{{item.completed}}">✓</text>
        </view>
        <view class="plan-content">
          <text class="plan-name">{{item.name}}</text>
          <text class="plan-details">{{item.sets}} 组 × {{item.reps}} 次</text>
        </view>
        <view class="plan-action">
          <text class="action-text">{{item.completed ? '已完成' : '开始'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 训练历史 -->
  <view class="workout-history">
    <view class="section-header">
      <text class="section-title">训练记录</text>
      <view class="filter-tabs">
        <view 
          class="tab-item {{historyFilter === item.value ? 'active' : ''}}"
          wx:for="{{filterTabs}}"
          wx:key="value"
          data-filter="{{item.value}}"
          bindtap="onFilterChange"
        >
          <text class="tab-text">{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="history-list">
      <view 
        class="history-item"
        wx:for="{{filteredHistory}}"
        wx:key="id"
        data-workout="{{item}}"
        bindtap="onViewWorkoutDetail"
      >
        <view class="history-icon">
          <text class="icon">{{item.icon}}</text>
        </view>
        <view class="history-content">
          <text class="history-name">{{item.name}}</text>
          <text class="history-details">{{item.duration}} · {{item.exercises}}个动作 · {{item.calories}}卡路里</text>
          <text class="history-date">{{item.date}}</text>
        </view>
        <view class="history-rating">
          <view class="rating-stars">
            <text 
              class="star {{index < item.rating ? 'filled' : ''}}"
              wx:for="{{5}}" 
              wx:for-item="star"
              wx:for-index="index"
            >★</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMoreHistory}}" bindtap="onLoadMoreHistory">
      <text class="load-more-text">加载更多</text>
    </view>
  </view>

  <!-- 创建训练弹窗 -->
  <view class="create-workout-modal {{showCreateModal ? 'show' : ''}}" catchtap="onCloseCreateModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">创建训练</text>
        <view class="close-btn" bindtap="onCloseCreateModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">训练名称</text>
          <input 
            class="form-input" 
            placeholder="输入训练名称" 
            value="{{newWorkout.name}}"
            bindinput="onWorkoutNameInput"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">训练类型</text>
          <picker 
            mode="selector" 
            range="{{workoutTypes}}" 
            range-key="name"
            value="{{newWorkout.typeIndex}}"
            bindchange="onWorkoutTypeChange"
          >
            <view class="picker-display">
              <text class="picker-text">{{workoutTypes[newWorkout.typeIndex].name}}</text>
              <text class="picker-arrow">〉</text>
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">预计时长（分钟）</text>
          <input 
            class="form-input" 
            type="number" 
            placeholder="30" 
            value="{{newWorkout.duration}}"
            bindinput="onWorkoutDurationInput"
          />
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onCloseCreateModal">取消</button>
        <button class="modal-btn primary" bindtap="onCreateWorkout">创建</button>
      </view>
    </view>
  </view>

  <!-- 训练完成弹窗 -->
  <view class="workout-complete-modal {{showCompleteModal ? 'show' : ''}}" catchtap="">
    <view class="modal-content">
      <view class="complete-header">
        <text class="complete-title">训练完成！</text>
        <text class="complete-emoji">🎉</text>
      </view>
      
      <view class="complete-stats">
        <view class="complete-stat">
          <text class="stat-number">{{completedWorkout.duration}}</text>
          <text class="stat-unit">分钟</text>
        </view>
        <view class="complete-stat">
          <text class="stat-number">{{completedWorkout.exercises}}</text>
          <text class="stat-unit">动作</text>
        </view>
        <view class="complete-stat">
          <text class="stat-number">{{completedWorkout.calories}}</text>
          <text class="stat-unit">卡路里</text>
        </view>
      </view>
      
      <view class="rating-section">
        <text class="rating-label">为本次训练评分</text>
        <view class="rating-stars">
          <text 
            class="star {{index < completedWorkout.rating ? 'filled' : ''}}"
            wx:for="{{5}}" 
            wx:for-item="star"
            wx:for-index="index"
            data-rating="{{index + 1}}"
            bindtap="onRateWorkout"
          >★</text>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onShareWorkout">分享成果</button>
        <button class="modal-btn primary" bindtap="onCloseCompleteModal">完成</button>
      </view>
    </view>
  </view>
</view> 