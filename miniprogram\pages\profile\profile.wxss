/* 个人资料页面样式 */
.container {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

/* 用户头部 */
.user-header {
  position: relative;
  padding: 60rpx 32rpx 40rpx;
  margin-top: var(--status-bar-height, 44px);
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 0 40rpx 40rpx;
}

.user-info {
  position: relative;
  display: flex;
  align-items: center;
  gap: 24rpx;
  z-index: 2;
}

.avatar-section {
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.edit-icon {
  font-size: 20rpx;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-level {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}

.user-id {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

.settings-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.settings-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.settings-icon {
  font-size: 32rpx;
}

/* 统计数据 */
.stats-section {
  margin: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.view-detail {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #667eea;
}

.arrow {
  font-size: 24rpx;
  color: #667eea;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.stat-card:active {
  transform: translateY(2rpx);
}

.stat-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

/* 功能菜单 */
.menu-section {
  margin: 32rpx;
}

.menu-group {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f3f4f6;
  transition: all 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8fafc;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f3f4f6;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.extra-text {
  font-size: 28rpx;
  color: #9ca3af;
}

.menu-arrow {
  font-size: 24rpx;
  color: #d1d5db;
}

/* 设置弹窗 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.settings-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.settings-list {
  padding: 40rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-icon {
  width: 64rpx;
  height: 64rpx;
  background: #f3f4f6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.setting-text {
  flex: 1;
  font-size: 28rpx;
  color: #1f2937;
}

.setting-control {
  display: flex;
  align-items: center;
}

.setting-arrow {
  font-size: 24rpx;
  color: #d1d5db;
}

.logout-section {
  padding: 40rpx;
  border-top: 2rpx solid #f3f4f6;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ef4444;
  color: #ffffff;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.logout-btn:active {
  transform: scale(0.98);
  background: #dc2626;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .user-details {
    align-items: center;
  }
  
  .settings-btn {
    position: absolute;
    top: 20rpx;
    right: 32rpx;
  }
} 