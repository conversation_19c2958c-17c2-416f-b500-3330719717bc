<!-- 登录页面 -->
<view class="container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="bg-circle circle-1"></view>
    <view class="bg-circle circle-2"></view>
    <view class="bg-circle circle-3"></view>
  </view>

  <!-- 应用LOGO和标题 -->
  <view class="app-header">
    <image class="app-logo" src="/assets/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">FitFocus</text>
    <text class="app-slogan">专注健身，精彩生活</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 微信快速登录 -->
    <view class="quick-login-section">
      <button class="wechat-login-btn" open-type="getUserInfo" bindgetuserinfo="onWechatLogin">
        <image class="wechat-icon" src="/assets/icons/wechat.png" mode="aspectFit"></image>
        <text class="login-text">微信快速登录</text>
      </button>
    </view>

    <!-- 分割线 -->
    <view class="divider">
      <view class="divider-line"></view>
      <text class="divider-text">或</text>
      <view class="divider-line"></view>
    </view>

    <!-- 手机号登录 -->
    <view class="phone-login-section">
      <view class="input-group">
        <view class="input-item">
          <view class="input-icon">📱</view>
          <input 
            class="input-field"
            type="number"
            placeholder="请输入手机号"
            value="{{phoneNumber}}"
            bindinput="onPhoneInput"
            maxlength="11"
          />
        </view>
        
        <view class="input-item">
          <view class="input-icon">🔐</view>
          <input 
            class="input-field code-input"
            type="number"
            placeholder="请输入验证码"
            value="{{verifyCode}}"
            bindinput="onCodeInput"
            maxlength="6"
          />
          <button 
            class="get-code-btn {{canGetCode ? '' : 'disabled'}}"
            bindtap="onGetCode"
            disabled="{{!canGetCode}}"
          >
            {{codeButtonText}}
          </button>
        </view>
      </view>

      <button 
        class="phone-login-btn {{canLogin ? '' : 'disabled'}}"
        bindtap="onPhoneLogin"
        disabled="{{!canLogin}}"
      >
        登录
      </button>
    </view>

    <!-- 用户协议 -->
    <view class="agreement-section">
      <view class="agreement-checkbox">
        <checkbox 
          checked="{{agreedToTerms}}" 
          bindchange="onAgreementChange"
          color="#667eea"
        />
        <text class="agreement-text">
          我已阅读并同意
          <text class="agreement-link" bindtap="onViewUserAgreement">《用户协议》</text>
          和
          <text class="agreement-link" bindtap="onViewPrivacyPolicy">《隐私政策》</text>
        </text>
      </view>
    </view>
  </view>

  <!-- 其他登录方式 -->
  <view class="other-login-section">
    <view class="section-title">其他登录方式</view>
    <view class="other-login-buttons">
      <button class="other-login-btn" bindtap="onVisitorLogin">
        <view class="other-login-icon">👤</view>
        <text class="other-login-text">游客体验</text>
      </button>
    </view>
  </view>

  <!-- 底部信息 */
  <view class="footer-info">
    <text class="footer-text">健身从此变得简单有趣</text>
    <text class="version-text">v1.0.0</text>
  </view>

  <!-- 用户协议弹窗 -->
  <view class="agreement-modal {{showAgreementModal ? 'show' : ''}}" catchtap="onCloseAgreementModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">{{agreementModalTitle}}</text>
        <view class="close-btn" bindtap="onCloseAgreementModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <scroll-view class="modal-body" scroll-y="true">
        <view class="agreement-content">
          <text class="agreement-content-text" wx:if="{{agreementType === 'user'}}">
            《FitFocus用户协议》

            1. 服务条款
            1.1 FitFocus是一款专注于健身的移动应用程序，为用户提供健身训练、数据记录、社区交流等服务。
            1.2 用户在使用本应用前应仔细阅读本协议的全部内容。

            2. 账户管理
            2.1 用户应当保证注册信息的真实性、准确性和完整性。
            2.2 用户应当妥善保管账户信息，对账户下的所有活动负责。

            3. 使用规范
            3.1 用户应当遵守相关法律法规，不得利用本应用从事违法活动。
            3.2 用户不得发布虚假、违法、有害的信息内容。

            4. 知识产权
            4.1 本应用的所有内容均受知识产权法保护。
            4.2 用户不得侵犯本应用及第三方的知识产权。

            5. 免责声明
            5.1 用户使用本应用进行健身活动时应当量力而行。
            5.2 因用户不当使用导致的任何后果，本应用不承担责任。

            本协议的解释权归FitFocus所有。
          </text>
          
          <text class="agreement-content-text" wx:if="{{agreementType === 'privacy'}}">
            《FitFocus隐私政策》

            1. 信息收集
            1.1 我们会收集您主动提供的个人信息，如注册信息、健身数据等。
            1.2 我们会收集您使用应用时的行为数据，用于改善服务质量。

            2. 信息使用
            2.1 我们使用收集的信息为您提供个性化的健身服务。
            2.2 我们可能使用信息进行数据分析，以优化产品功能。

            3. 信息保护
            3.1 我们采用行业标准的安全措施保护您的个人信息。
            3.2 我们不会向第三方出售、交易或转让您的个人信息。

            4. 信息共享
            4.1 在获得您同意的情况下，我们可能与合作伙伴共享必要信息。
            4.2 在法律要求的情况下，我们可能披露您的信息。

            5. 用户权利
            5.1 您有权查询、更正、删除您的个人信息。
            5.2 您可以随时关闭某些功能的数据收集。

            6. 联系我们
            如您对隐私政策有任何疑问，请联系：<EMAIL>

            本政策的解释权归FitFocus所有。
          </text>
        </view>
      </scroll-view>
      
      <view class="modal-actions">
        <button class="modal-btn primary" bindtap="onCloseAgreementModal">我知道了</button>
      </view>
    </view>
  </view>

  <!-- 加载提示 -->
  <view class="loading-overlay {{isLoading ? 'show' : ''}}" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view> 