<!-- 身体数据页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="back-btn" bindtap="onBack">
      <text class="back-icon">〈</text>
    </view>
    <text class="page-title">身体数据</text>
    <view class="action-btns">
      <view class="action-btn" bindtap="onShowHistory">
        <text class="action-icon">📊</text>
      </view>
      <view class="action-btn" bindtap="onAddData">
        <text class="action-icon">➕</text>
      </view>
    </view>
  </view>

  <!-- 数据概览 -->
  <view class="data-overview">
    <view class="overview-header">
      <text class="overview-title">最新记录</text>
      <text class="overview-date">{{latestRecord.date}}</text>
    </view>
    
    <view class="data-cards">
      <view class="data-card weight">
        <view class="card-icon">⚖️</view>
        <view class="card-content">
          <text class="card-value">{{latestRecord.weight}}</text>
          <text class="card-unit">kg</text>
          <text class="card-change {{latestRecord.weightChange >= 0 ? 'positive' : 'negative'}}">
            {{latestRecord.weightChange >= 0 ? '+' : ''}}{{latestRecord.weightChange}}kg
          </text>
          <text class="card-label">体重</text>
        </view>
      </view>
      
      <view class="data-card bmi">
        <view class="card-icon">📏</view>
        <view class="card-content">
          <text class="card-value">{{latestRecord.bmi}}</text>
          <text class="card-unit"></text>
          <text class="card-status {{latestRecord.bmiStatus}}">{{latestRecord.bmiStatusText}}</text>
          <text class="card-label">BMI</text>
        </view>
      </view>
      
      <view class="data-card body-fat">
        <view class="card-icon">🔥</view>
        <view class="card-content">
          <text class="card-value">{{latestRecord.bodyFat}}</text>
          <text class="card-unit">%</text>
          <text class="card-change {{latestRecord.bodyFatChange >= 0 ? 'positive' : 'negative'}}">
            {{latestRecord.bodyFatChange >= 0 ? '+' : ''}}{{latestRecord.bodyFatChange}}%
          </text>
          <text class="card-label">体脂率</text>
        </view>
      </view>
      
      <view class="data-card muscle">
        <view class="card-icon">💪</view>
        <view class="card-content">
          <text class="card-value">{{latestRecord.muscle}}</text>
          <text class="card-unit">%</text>
          <text class="card-change {{latestRecord.muscleChange >= 0 ? 'positive' : 'negative'}}">
            {{latestRecord.muscleChange >= 0 ? '+' : ''}}{{latestRecord.muscleChange}}%
          </text>
          <text class="card-label">肌肉量</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 趋势图表 -->
  <view class="chart-section">
    <view class="chart-header">
      <text class="chart-title">数据趋势</text>
      <view class="chart-filter">
        <view class="filter-tabs">
          <view 
            class="filter-tab {{chartTimeRange === 'week' ? 'active' : ''}}"
            bindtap="onChartFilterChange"
            data-range="week"
          >
            <text class="tab-text">7天</text>
          </view>
          <view 
            class="filter-tab {{chartTimeRange === 'month' ? 'active' : ''}}"
            bindtap="onChartFilterChange"
            data-range="month"
          >
            <text class="tab-text">30天</text>
          </view>
          <view 
            class="filter-tab {{chartTimeRange === 'quarter' ? 'active' : ''}}"
            bindtap="onChartFilterChange"
            data-range="quarter"
          >
            <text class="tab-text">3个月</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="chart-types">
      <view 
        class="chart-type {{selectedChartType === 'weight' ? 'active' : ''}}"
        bindtap="onChartTypeChange"
        data-type="weight"
      >
        <text class="type-text">体重</text>
      </view>
      <view 
        class="chart-type {{selectedChartType === 'bodyFat' ? 'active' : ''}}"
        bindtap="onChartTypeChange"
        data-type="bodyFat"
      >
        <text class="type-text">体脂率</text>
      </view>
      <view 
        class="chart-type {{selectedChartType === 'muscle' ? 'active' : ''}}"
        bindtap="onChartTypeChange"
        data-type="muscle"
      >
        <text class="type-text">肌肉量</text>
      </view>
    </view>
    
    <view class="chart-container">
      <canvas 
        class="trend-chart" 
        canvas-id="trendChart"
        bindtouchstart="onChartTouch"
        bindtouchmove="onChartTouch"
        bindtouchend="onChartTouchEnd"
      ></canvas>
      
      <view class="chart-tooltip {{showTooltip ? 'show' : ''}}" style="left: {{tooltipX}}px; top: {{tooltipY}}px;">
        <text class="tooltip-date">{{tooltipData.date}}</text>
        <text class="tooltip-value">{{tooltipData.value}}</text>
      </view>
    </view>
    
    <view class="chart-summary">
      <view class="summary-item">
        <text class="summary-label">最高值</text>
        <text class="summary-value">{{chartSummary.max}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">最低值</text>
        <text class="summary-value">{{chartSummary.min}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">平均值</text>
        <text class="summary-value">{{chartSummary.avg}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">变化</text>
        <text class="summary-value {{chartSummary.change >= 0 ? 'positive' : 'negative'}}">
          {{chartSummary.change >= 0 ? '+' : ''}}{{chartSummary.change}}
        </text>
      </view>
    </view>
  </view>

  <!-- 目标设置 -->
  <view class="goals-section">
    <view class="section-header">
      <text class="section-title">健身目标</text>
      <view class="edit-goals-btn" bindtap="onEditGoals">
        <text class="edit-text">编辑</text>
      </view>
    </view>
    
    <view class="goals-list">
      <view class="goal-item">
        <view class="goal-info">
          <text class="goal-label">目标体重</text>
          <text class="goal-value">{{userGoals.targetWeight}}kg</text>
        </view>
        <view class="goal-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{weightProgress}}%"></view>
          </view>
          <text class="progress-text">{{weightProgress}}%</text>
        </view>
      </view>
      
      <view class="goal-item">
        <view class="goal-info">
          <text class="goal-label">目标体脂率</text>
          <text class="goal-value">{{userGoals.targetBodyFat}}%</text>
        </view>
        <view class="goal-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{bodyFatProgress}}%"></view>
          </view>
          <text class="progress-text">{{bodyFatProgress}}%</text>
        </view>
      </view>
      
      <view class="goal-item">
        <view class="goal-info">
          <text class="goal-label">目标肌肉量</text>
          <text class="goal-value">{{userGoals.targetMuscle}}%</text>
        </view>
        <view class="goal-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{muscleProgress}}%"></view>
          </view>
          <text class="progress-text">{{muscleProgress}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="history-section">
    <view class="section-header">
      <text class="section-title">历史记录</text>
      <view class="view-all-btn" bindtap="onViewAllHistory">
        <text class="view-all-text">查看全部</text>
        <text class="view-all-arrow">〉</text>
      </view>
    </view>
    
    <view class="history-list">
      <view 
        class="history-item"
        wx:for="{{recentHistory}}"
        wx:key="id"
        data-record="{{item}}"
        bindtap="onHistoryItemTap"
      >
        <view class="history-date">
          <text class="date-day">{{item.day}}</text>
          <text class="date-month">{{item.month}}</text>
        </view>
        <view class="history-data">
          <view class="data-row">
            <text class="data-label">体重</text>
            <text class="data-value">{{item.weight}}kg</text>
            <text class="data-change {{item.weightChange >= 0 ? 'positive' : 'negative'}}">
              {{item.weightChange >= 0 ? '+' : ''}}{{item.weightChange}}
            </text>
          </view>
          <view class="data-row">
            <text class="data-label">体脂率</text>
            <text class="data-value">{{item.bodyFat}}%</text>
            <text class="data-change {{item.bodyFatChange >= 0 ? 'positive' : 'negative'}}">
              {{item.bodyFatChange >= 0 ? '+' : ''}}{{item.bodyFatChange}}
            </text>
          </view>
        </view>
        <view class="history-arrow">
          <text class="arrow-icon">〉</text>
        </view>
      </view>
      
      <view class="empty-history" wx:if="{{recentHistory.length === 0}}">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无记录数据</text>
        <button class="add-data-btn" bindtap="onAddData">
          <text class="btn-text">添加数据</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 智能分析 -->
  <view class="analysis-section">
    <view class="section-header">
      <text class="section-title">智能分析</text>
      <view class="refresh-analysis-btn" bindtap="onRefreshAnalysis">
        <text class="refresh-icon">🔄</text>
      </view>
    </view>
    
    <view class="analysis-cards">
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">本周变化</text>
          <view class="card-status positive">
            <text class="status-text">良好</text>
          </view>
        </view>
        <text class="card-content">您的体重本周下降了0.5kg，体脂率降低了0.3%，健康状态持续改善。</text>
      </view>
      
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">目标进度</text>
          <view class="card-status warning">
            <text class="status-text">需加油</text>
          </view>
        </view>
        <text class="card-content">距离目标体重还差2.5kg，建议保持当前运动强度，控制饮食摄入。</text>
      </view>
      
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">健康建议</text>
          <view class="card-status info">
            <text class="status-text">提醒</text>
          </view>
        </view>
        <text class="card-content">您的BMI指数正常，建议增加力量训练来提升肌肉量占比。</text>
      </view>
    </view>
  </view>

  <!-- 添加数据弹窗 -->
  <view class="add-data-modal {{showAddModal ? 'show' : ''}}" catchtap="onCloseAddModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">添加身体数据</text>
        <view class="close-btn" bindtap="onCloseAddModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-body">
        <view class="input-section">
          <view class="input-group">
            <text class="input-label">测量日期</text>
            <picker mode="date" value="{{newRecord.date}}" bindchange="onDateChange">
              <view class="date-picker">
                <text class="date-text">{{newRecord.date}}</text>
                <text class="date-arrow">〉</text>
              </view>
            </picker>
          </view>
          
          <view class="input-group">
            <text class="input-label">体重 (kg)</text>
            <input 
              class="data-input"
              type="digit"
              placeholder="请输入体重"
              value="{{newRecord.weight}}"
              bindinput="onWeightInput"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">身高 (cm)</text>
            <input 
              class="data-input"
              type="digit"
              placeholder="请输入身高"
              value="{{newRecord.height}}"
              bindinput="onHeightInput"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">体脂率 (%)</text>
            <input 
              class="data-input"
              type="digit"
              placeholder="请输入体脂率"
              value="{{newRecord.bodyFat}}"
              bindinput="onBodyFatInput"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">肌肉量 (%)</text>
            <input 
              class="data-input"
              type="digit"
              placeholder="请输入肌肉量"
              value="{{newRecord.muscle}}"
              bindinput="onMuscleInput"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">内脏脂肪等级</text>
            <input 
              class="data-input"
              type="digit"
              placeholder="请输入内脏脂肪等级"
              value="{{newRecord.visceralFat}}"
              bindinput="onVisceralFatInput"
            />
          </view>
          
          <view class="input-group">
            <text class="input-label">备注 (可选)</text>
            <textarea 
              class="note-input"
              placeholder="记录今天的感受或特殊情况"
              value="{{newRecord.note}}"
              bindinput="onNoteInput"
              maxlength="200"
            ></textarea>
          </view>
        </view>
        
        <view class="calculated-info" wx:if="{{newRecord.weight && newRecord.height}}">
          <text class="info-title">自动计算</text>
          <view class="info-item">
            <text class="info-label">BMI指数</text>
            <text class="info-value">{{calculatedBMI}}</text>
            <text class="info-status {{bmiStatusClass}}">{{bmiStatusText}}</text>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onCloseAddModal">取消</button>
        <button class="modal-btn primary" bindtap="onSaveRecord" disabled="{{!canSave}}">保存</button>
      </view>
    </view>
  </view>

  <!-- 目标设置弹窗 -->
  <view class="goals-modal {{showGoalsModal ? 'show' : ''}}" catchtap="onCloseGoalsModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">设置健身目标</text>
        <view class="close-btn" bindtap="onCloseGoalsModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-body">
        <view class="goals-inputs">
          <view class="goal-input-group">
            <text class="goal-label">目标体重 (kg)</text>
            <input 
              class="goal-input"
              type="digit"
              placeholder="设置目标体重"
              value="{{tempGoals.targetWeight}}"
              bindinput="onTargetWeightInput"
            />
          </view>
          
          <view class="goal-input-group">
            <text class="goal-label">目标体脂率 (%)</text>
            <input 
              class="goal-input"
              type="digit"
              placeholder="设置目标体脂率"
              value="{{tempGoals.targetBodyFat}}"
              bindinput="onTargetBodyFatInput"
            />
          </view>
          
          <view class="goal-input-group">
            <text class="goal-label">目标肌肉量 (%)</text>
            <input 
              class="goal-input"
              type="digit"
              placeholder="设置目标肌肉量"
              value="{{tempGoals.targetMuscle}}"
              bindinput="onTargetMuscleInput"
            />
          </view>
          
          <view class="goal-input-group">
            <text class="goal-label">目标达成时间</text>
            <picker mode="date" value="{{tempGoals.targetDate}}" bindchange="onTargetDateChange">
              <view class="date-picker">
                <text class="date-text">{{tempGoals.targetDate}}</text>
                <text class="date-arrow">〉</text>
              </view>
            </picker>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onCloseGoalsModal">取消</button>
        <button class="modal-btn primary" bindtap="onSaveGoals">保存目标</button>
      </view>
    </view>
  </view>
</view> 