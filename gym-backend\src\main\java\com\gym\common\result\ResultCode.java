package com.gym.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    
    // 用户相关状态码 (1000-1999)
    USER_NOT_EXIST(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    USER_LOGIN_ERROR(1003, "登录失败"),
    USER_TOKEN_EXPIRED(1004, "登录已过期"),
    USER_TOKEN_INVALID(1005, "登录令牌无效"),
    USER_PERMISSION_DENIED(1006, "权限不足"),
    USER_ALREADY_EXIST(1007, "用户已存在"),
    USER_PASSWORD_ERROR(1008, "密码错误"),
    
    // 课程相关状态码 (2000-2999)
    COURSE_NOT_EXIST(2001, "课程不存在"),
    COURSE_FULL(2002, "课程已满员"),
    COURSE_BOOKING_EXIST(2003, "已预约该课程"),
    COURSE_BOOKING_DEADLINE(2004, "超过预约时间"),
    COURSE_CANCEL_DEADLINE(2005, "超过取消时间"),
    COURSE_NOT_STARTED(2006, "课程尚未开始"),
    COURSE_ALREADY_FINISHED(2007, "课程已结束"),
    
    // 设备相关状态码 (3000-3999)
    EQUIPMENT_NOT_EXIST(3001, "设备不存在"),
    EQUIPMENT_IN_USE(3002, "设备使用中"),
    EQUIPMENT_MAINTENANCE(3003, "设备维护中"),
    EQUIPMENT_MALFUNCTION(3004, "设备故障"),
    EQUIPMENT_QR_INVALID(3005, "设备二维码无效"),
    
    // 支付相关状态码 (4000-4999)
    PAYMENT_FAILED(4001, "支付失败"),
    PAYMENT_AMOUNT_ERROR(4002, "支付金额错误"),
    PAYMENT_ORDER_NOT_EXIST(4003, "支付订单不存在"),
    PAYMENT_ORDER_EXPIRED(4004, "支付订单已过期"),
    REFUND_FAILED(4005, "退款失败"),
    
    // 文件相关状态码 (5000-5999)
    FILE_UPLOAD_ERROR(5001, "文件上传失败"),
    FILE_SIZE_EXCEED(5002, "文件大小超限"),
    FILE_TYPE_NOT_SUPPORT(5003, "文件类型不支持"),
    FILE_NOT_EXIST(5004, "文件不存在"),
    
    // 微信相关状态码 (6000-6999)
    WECHAT_CODE_INVALID(6001, "微信授权码无效"),
    WECHAT_API_ERROR(6002, "微信接口调用失败"),
    WECHAT_DECRYPT_ERROR(6003, "微信数据解密失败"),
    
    // 业务逻辑状态码 (7000-7999)
    MEMBER_LEVEL_INSUFFICIENT(7001, "会员等级不足"),
    MEMBER_EXPIRED(7002, "会员已过期"),
    POINTS_INSUFFICIENT(7003, "积分不足"),
    COUPON_NOT_AVAILABLE(7004, "优惠券不可用"),
    COUPON_EXPIRED(7005, "优惠券已过期"),
    COUPON_USED(7006, "优惠券已使用"),
    
    // 系统相关状态码 (8000-8999)
    SYSTEM_BUSY(8001, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(8002, "系统维护中"),
    RATE_LIMIT_EXCEEDED(8003, "请求频率超限"),
    DATA_SYNC_ERROR(8004, "数据同步失败"),
    
    // 验证相关状态码 (9000-9999)
    VALIDATION_ERROR(9001, "数据验证失败"),
    CAPTCHA_ERROR(9002, "验证码错误"),
    SMS_SEND_ERROR(9003, "短信发送失败"),
    EMAIL_SEND_ERROR(9004, "邮件发送失败");
    
    private final Integer code;
    private final String message;
} 