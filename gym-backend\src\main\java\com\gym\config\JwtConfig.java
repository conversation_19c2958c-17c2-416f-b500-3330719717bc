package com.gym.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT 配置类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Component
@ConfigurationProperties(prefix = "jwt")
public class JwtConfig {

    /**
     * JWT 密钥
     */
    private String secret;

    /**
     * JWT 过期时间（秒）
     */
    private Long expiration;

    /**
     * JWT 请求头
     */
    private String header;

    /**
     * JWT 前缀
     */
    private String prefix;
} 