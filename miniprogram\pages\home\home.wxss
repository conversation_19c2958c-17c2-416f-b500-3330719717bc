/* 主页样式 - 专注健身 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 30rpx;
  box-sizing: border-box;
}

/* 顶部区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0 40rpx;
  margin-top: var(--status-bar-height, 44px);
}

.greeting .title {
  display: block;
  color: #ffffff;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.greeting .subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.avatar-container {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.avatar {
  width: 100%;
  height: 100%;
}

/* 番茄钟区域 */
.pomodoro-section {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}

.pomodoro-circle {
  width: 500rpx;
  height: 500rpx;
  border-radius: 50%;
  background: conic-gradient(#ff6b6b 0deg 90deg, rgba(248, 249, 250, 0.3) 90deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.pomodoro-circle.running {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.pomodoro-inner {
  width: 440rpx;
  height: 440rpx;
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.timer-display {
  font-size: 72rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.timer-label {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 40rpx;
}

.timer-controls {
  position: absolute;
  bottom: 60rpx;
}

.control-btn {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ef4444 0%, #ec4899 100%);
  border: none;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(239, 68, 68, 0.3);
  transition: all 0.2s ease;
}

.control-btn.play {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 16rpx 48rpx rgba(16, 185, 129, 0.3);
}

.control-btn:active {
  transform: scale(0.95);
}

/* 今日目标区域 */
.goals-section {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
}

.progress-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.goal-item {
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.goal-item.completed {
  opacity: 0.6;
}

.goal-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #10b981;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.goal-item.completed .goal-indicator {
  background: #22c55e;
}

.goal-text {
  color: #ffffff;
  font-size: 28rpx;
  flex: 1;
}

.goal-item.completed .goal-text {
  text-decoration: line-through;
}

.goal-check {
  color: #22c55e;
  font-size: 32rpx;
  font-weight: bold;
}

/* 快捷操作 */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.action-btn {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: translateY(4rpx);
  background: rgba(255, 255, 255, 0.25);
}

.btn-icon {
  font-size: 48rpx;
}

.btn-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}

/* 推荐课程 */
.recommended-section {
  margin-bottom: 48rpx;
}

.more-btn {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  text-decoration: underline;
}

.course-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  display: flex;
  gap: 24rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.course-card:active {
  transform: translateY(4rpx);
}

.course-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.course-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.course-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.course-instructor {
  font-size: 24rpx;
  color: #6b7280;
}

.course-meta {
  display: flex;
  gap: 16rpx;
  margin-top: 8rpx;
}

.course-time, .course-spots {
  font-size: 24rpx;
  color: #9ca3af;
}

.course-action {
  display: flex;
  align-items: center;
}

.book-btn {
  background: #667eea;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 设备状态 */
.equipment-overview {
  margin-bottom: 48rpx;
}

.equipment-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-top: 24rpx;
}

.equipment-item {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  position: relative;
  transition: all 0.2s ease;
}

.equipment-item:active {
  transform: translateY(2rpx);
}

.equipment-icon {
  font-size: 40rpx;
}

.equipment-name {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}

.equipment-count {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.status-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.status-indicator.available {
  background: #22c55e;
}

.status-indicator.busy {
  background: #f59e0b;
}

.status-indicator.full {
  background: #ef4444;
}

/* 成就提示 */
.achievement-toast {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%) translateY(-100rpx);
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1000;
}

.achievement-toast.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.achievement-icon {
  font-size: 48rpx;
}

.achievement-text {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.achievement-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.achievement-desc {
  font-size: 24rpx;
  color: #6b7280;
}

/* 完成弹窗 */
.pomodoro-complete-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.pomodoro-complete-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32rpx;
  padding: 48rpx;
  width: 80%;
  max-width: 600rpx;
}

.modal-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #1f2937;
}

.modal-body {
  text-align: center;
  margin-bottom: 48rpx;
}

.modal-text {
  display: block;
  font-size: 32rpx;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.modal-subtext {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
}

.modal-actions {
  display: flex;
  gap: 24rpx;
}

.modal-btn {
  flex: 1;
  padding: 32rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}

.modal-btn.primary {
  background: #667eea;
  color: #ffffff;
}

.modal-btn.secondary {
  background: #f3f4f6;
  color: #4b5563;
} 