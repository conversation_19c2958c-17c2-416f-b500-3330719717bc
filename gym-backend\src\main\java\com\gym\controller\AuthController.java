package com.gym.controller;

import com.gym.common.result.Result;
import com.gym.entity.UserInfo;
import com.gym.service.UserService;
import com.gym.util.JwtUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 微信小程序登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> wechatLogin(@Valid @RequestBody LoginRequest request) {
        log.info("微信登录请求: {}", request.getCode());
        
        try {
            // 调用微信登录服务
            UserInfo user = userService.wechatLogin(request.getCode(), request.getNickname(), request.getAvatar());
            
            // 生成JWT令牌
            String token = jwtUtil.generateToken(user.getOpenid(), user.getId());
            
            // 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("userInfo", buildUserInfo(user));
            
            log.info("用户登录成功: userId={}", user.getId());
            return Result.success("登录成功", data);
            
        } catch (Exception e) {
            log.error("微信登录失败: {}", e.getMessage(), e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refreshToken(@RequestHeader("Authorization") String authHeader) {
        try {
            String token = jwtUtil.extractTokenFromHeader(authHeader);
            
            if (token == null || !jwtUtil.isTokenValid(token)) {
                return Result.error("令牌无效");
            }
            
            String openid = jwtUtil.getOpenidFromToken(token);
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            // 生成新的令牌
            String newToken = jwtUtil.generateToken(openid, userId);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", newToken);
            
            return Result.success("令牌刷新成功", data);
            
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage(), e);
            return Result.error("令牌刷新失败");
        }
    }

    /**
     * 登出
     */
    @PostMapping("/logout")
    public Result<Void> logout(@RequestHeader("Authorization") String authHeader) {
        try {
            // 这里可以将令牌加入黑名单，简化处理直接返回成功
            log.info("用户登出");
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage(), e);
            return Result.error("登出失败");
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public Result<Map<String, Object>> getCurrentUser(@RequestHeader("Authorization") String authHeader) {
        try {
            String token = jwtUtil.extractTokenFromHeader(authHeader);
            
            if (token == null || !jwtUtil.isTokenValid(token)) {
                return Result.error("令牌无效");
            }
            
            Long userId = jwtUtil.getUserIdFromToken(token);
            UserInfo user = userService.getUserById(userId);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            return Result.success("获取成功", buildUserInfo(user));
            
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage(), e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 构建用户信息响应（过滤敏感信息）
     */
    private Map<String, Object> buildUserInfo(UserInfo user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("nickname", user.getNickname());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("phone", user.getPhone());
        userInfo.put("gender", user.getGender());
        userInfo.put("birthday", user.getBirthday());
        userInfo.put("memberLevel", user.getMemberLevel());
        userInfo.put("memberLevelName", user.getMemberLevelName());
        userInfo.put("memberExpireDate", user.getMemberExpireDate());
        userInfo.put("totalPoints", user.getTotalPoints());
        userInfo.put("availablePoints", user.getAvailablePoints());
        userInfo.put("isValidMember", user.isValidMember());
        userInfo.put("isVipMember", user.isVipMember());
        return userInfo;
    }

    /**
     * 登录请求对象
     */
    @Data
    public static class LoginRequest {
        @NotBlank(message = "微信授权码不能为空")
        private String code;
        
        private String nickname;
        private String avatar;
    }
} 