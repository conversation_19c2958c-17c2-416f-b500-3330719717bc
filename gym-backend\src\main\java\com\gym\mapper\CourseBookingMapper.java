package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.CourseBooking;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程预约 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface CourseBookingMapper extends BaseMapper<CourseBooking> {

    /**
     * 根据用户ID查询预约记录
     */
    List<CourseBooking> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据排期ID查询预约记录
     */
    List<CourseBooking> selectByScheduleId(@Param("scheduleId") Long scheduleId);

    /**
     * 查询用户的有效预约
     */
    List<CourseBooking> selectValidBookingsByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否已预约某课程
     */
    CourseBooking selectByUserAndSchedule(@Param("userId") Long userId, 
                                         @Param("scheduleId") Long scheduleId);

    /**
     * 查询需要提醒的预约
     */
    List<CourseBooking> selectBookingsForReminder(@Param("reminderTime") LocalDateTime reminderTime);

    /**
     * 统计用户预约次数
     */
    int countBookingsByUserId(@Param("userId") Long userId, 
                             @Param("startTime") LocalDateTime startTime, 
                             @Param("endTime") LocalDateTime endTime);
} 