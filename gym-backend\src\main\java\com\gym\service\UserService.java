package com.gym.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gym.entity.UserInfo;

/**
 * 用户信息 Service 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface UserService extends IService<UserInfo> {

    /**
     * 微信登录
     */
    UserInfo wechatLogin(String code, String nickname, String avatar);

    /**
     * 根据openid获取用户信息
     */
    UserInfo getUserByOpenid(String openid);

    /**
     * 根据用户ID获取用户信息
     */
    UserInfo getUserById(Long userId);

    /**
     * 更新用户信息
     */
    boolean updateUserInfo(UserInfo userInfo);

    /**
     * 绑定手机号
     */
    boolean bindPhone(Long userId, String phone);

    /**
     * 更新用户最后登录时间
     */
    boolean updateLastLoginTime(Long userId);

    /**
     * 添加用户积分
     */
    boolean addUserPoints(Long userId, Integer points, String reason);

    /**
     * 扣除用户积分
     */
    boolean deductUserPoints(Long userId, Integer points, String reason);

    /**
     * 检查用户是否为有效会员
     */
    boolean isValidMember(Long userId);

    /**
     * 检查用户是否为VIP会员
     */
    boolean isVipMember(Long userId);

    /**
     * 升级用户会员等级
     */
    boolean upgradeMemberLevel(Long userId, Integer newLevel);
} 