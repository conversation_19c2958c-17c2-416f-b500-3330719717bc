package com.gym.controller;

import com.gym.common.result.Result;
import com.gym.entity.UserInfo;
import com.gym.service.UserService;
import com.gym.util.JwtUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 更新用户基本信息
     */
    @PutMapping("/profile")
    public Result<Void> updateProfile(@RequestHeader("Authorization") String authHeader,
                                     @Valid @RequestBody UpdateProfileRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            UserInfo userInfo = new UserInfo();
            userInfo.setId(userId);
            userInfo.setNickname(request.getNickname());
            userInfo.setGender(request.getGender());
            userInfo.setBirthday(request.getBirthday());
            
            boolean success = userService.updateUserInfo(userInfo);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败: {}", e.getMessage(), e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 绑定手机号
     */
    @PostMapping("/bind-phone")
    public Result<Void> bindPhone(@RequestHeader("Authorization") String authHeader,
                                 @Valid @RequestBody BindPhoneRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            boolean success = userService.bindPhone(userId, request.getPhone());
            if (success) {
                return Result.success("绑定成功");
            } else {
                return Result.error("绑定失败");
            }
        } catch (Exception e) {
            log.error("绑定手机号失败: {}", e.getMessage(), e);
            return Result.error("绑定失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户积分信息
     */
    @GetMapping("/points")
    public Result<Map<String, Object>> getUserPoints(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            UserInfo user = userService.getUserById(userId);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("totalPoints", user.getTotalPoints());
            data.put("availablePoints", user.getAvailablePoints());
            
            return Result.success("获取成功", data);
        } catch (Exception e) {
            log.error("获取用户积分失败: {}", e.getMessage(), e);
            return Result.error("获取失败");
        }
    }

    /**
     * 获取用户会员信息
     */
    @GetMapping("/membership")
    public Result<Map<String, Object>> getMembership(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            UserInfo user = userService.getUserById(userId);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("memberLevel", user.getMemberLevel());
            data.put("memberLevelName", user.getMemberLevelName());
            data.put("memberExpireDate", user.getMemberExpireDate());
            data.put("isValidMember", user.isValidMember());
            data.put("isVipMember", user.isVipMember());
            
            return Result.success("获取成功", data);
        } catch (Exception e) {
            log.error("获取用户会员信息失败: {}", e.getMessage(), e);
            return Result.error("获取失败");
        }
    }

    /**
     * 升级会员等级
     */
    @PostMapping("/upgrade-membership")
    public Result<Void> upgradeMembership(@RequestHeader("Authorization") String authHeader,
                                         @Valid @RequestBody UpgradeMembershipRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            boolean success = userService.upgradeMemberLevel(userId, request.getNewLevel());
            if (success) {
                return Result.success("升级成功");
            } else {
                return Result.error("升级失败");
            }
        } catch (Exception e) {
            log.error("升级会员失败: {}", e.getMessage(), e);
            return Result.error("升级失败: " + e.getMessage());
        }
    }

    /**
     * 添加用户积分（管理员接口）
     */
    @PostMapping("/add-points")
    public Result<Void> addPoints(@RequestHeader("Authorization") String authHeader,
                                 @Valid @RequestBody AddPointsRequest request) {
        try {
            // 这里应该添加管理员权限验证
            
            boolean success = userService.addUserPoints(request.getUserId(), request.getPoints(), request.getReason());
            if (success) {
                return Result.success("添加成功");
            } else {
                return Result.error("添加失败");
            }
        } catch (Exception e) {
            log.error("添加积分失败: {}", e.getMessage(), e);
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    /**
     * 从令牌中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        String token = jwtUtil.extractTokenFromHeader(authHeader);
        if (token == null || !jwtUtil.isTokenValid(token)) {
            throw new RuntimeException("令牌无效");
        }
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 更新用户信息请求对象
     */
    @Data
    public static class UpdateProfileRequest {
        private String nickname;
        private Integer gender;
        private LocalDate birthday;
    }

    /**
     * 绑定手机号请求对象
     */
    @Data
    public static class BindPhoneRequest {
        @NotBlank(message = "手机号不能为空")
        private String phone;
    }

    /**
     * 升级会员请求对象
     */
    @Data
    public static class UpgradeMembershipRequest {
        @NotNull(message = "新会员等级不能为空")
        private Integer newLevel;
    }

    /**
     * 添加积分请求对象
     */
    @Data
    public static class AddPointsRequest {
        @NotNull(message = "用户ID不能为空")
        private Long userId;
        
        @NotNull(message = "积分数量不能为空")
        private Integer points;
        
        @NotBlank(message = "原因不能为空")
        private String reason;
    }
} 