package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备使用记录实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("equipment_usage_log")
public class EquipmentUsageLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("equipment_id")
    private Long equipmentId;

    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("duration")
    private Integer duration;

    @TableField("calories_burned")
    private BigDecimal caloriesBurned;

    @TableField("distance")
    private BigDecimal distance;

    @TableField("speed_avg")
    private BigDecimal speedAvg;

    @TableField("heart_rate_avg")
    private Integer heartRateAvg;

    @TableField("workout_data")
    private String workoutData;

    @TableField("status")
    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "正常结束";
        }
        switch (status) {
            case 1: return "正常结束";
            case 2: return "异常结束";
            default: return "正常结束";
        }
    }

    /**
     * 判断是否正在使用
     */
    public boolean isInProgress() {
        return startTime != null && endTime == null;
    }

    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return startTime != null && endTime != null;
    }

    /**
     * 计算使用时长（分钟）
     */
    public Integer calculateDuration() {
        if (startTime != null && endTime != null) {
            return (int) java.time.Duration.between(startTime, endTime).toMinutes();
        }
        return null;
    }

    /**
     * 获取运动强度等级
     */
    public String getIntensityLevel() {
        if (heartRateAvg == null) {
            return "未知";
        }
        if (heartRateAvg < 100) {
            return "轻度";
        } else if (heartRateAvg < 130) {
            return "中等";
        } else if (heartRateAvg < 160) {
            return "高强度";
        } else {
            return "极限";
        }
    }

    /**
     * 获取平均配速（分钟/公里）
     */
    public String getAveragePace() {
        if (speedAvg == null || speedAvg.compareTo(BigDecimal.ZERO) <= 0) {
            return "无数据";
        }
        // 配速 = 60 / 速度
        BigDecimal pace = BigDecimal.valueOf(60).divide(speedAvg, 2, BigDecimal.ROUND_HALF_UP);
        return pace + " 分钟/公里";
    }
} 