const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 摄像头状态
    showCamera: false,
    cameraEnabled: false,
    flashOn: false,
    cameraStatus: '正在初始化摄像头...',
    
    // 扫码历史
    scanHistory: [
      {
        id: 1,
        name: '跑步机 A-001',
        description: '二楼有氧区',
        icon: '🏃‍♂️',
        time: Date.now() - 2 * 60 * 60 * 1000,
        timeAgo: '2小时前',
        status: 'success',
        statusText: '已连接'
      },
      {
        id: 2,
        name: '储物柜 L-056',
        description: '一楼更衣区',
        icon: '🔒',
        time: Date.now() - 6 * 60 * 60 * 1000,
        timeAgo: '6小时前',
        status: 'success',
        statusText: '已使用'
      },
      {
        id: 3,
        name: '淋浴间 S-08',
        description: '二楼淋浴区',
        icon: '🚿',
        time: Date.now() - 24 * 60 * 60 * 1000,
        timeAgo: '1天前',
        status: 'error',
        statusText: '连接失败'
      }
    ],
    
    // 手动输入
    showManualInput: false,
    manualDeviceCode: '',
    recentCodes: ['GYM001', 'GYM002', 'L-056', 'S-08'],
    
    // 设备信息
    showDeviceModal: false,
    currentDevice: null,
    
    // 帮助
    showHelp: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Scanner page loaded');
    this.initCamera();
    this.loadScanHistory();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 重新启动摄像头
    if (this.data.cameraEnabled) {
      this.setData({ showCamera: true });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 暂停摄像头以节省资源
    this.setData({ showCamera: false });
  },

  /**
   * 初始化摄像头
   */
  initCamera() {
    // 检查摄像头权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          this.enableCamera();
        } else {
          this.setData({
            cameraEnabled: false,
            cameraStatus: '需要摄像头权限才能扫码'
          });
        }
      },
      fail: () => {
        this.setData({
          cameraEnabled: false,
          cameraStatus: '获取权限信息失败'
        });
      }
    });
  },

  /**
   * 启用摄像头
   */
  enableCamera() {
    this.setData({
      cameraEnabled: true,
      showCamera: true,
      cameraStatus: '摄像头已启用'
    });
  },

  /**
   * 用户手动启用摄像头
   */
  onEnableCamera() {
    wx.authorize({
      scope: 'scope.camera',
      success: () => {
        this.enableCamera();
        wx.showToast({
          title: '摄像头已启用',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showModal({
          title: '需要摄像头权限',
          content: '扫码功能需要使用摄像头，请在设置中开启权限',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      }
    });
  },

  /**
   * 二维码扫描成功
   */
  onScanCode(e) {
    const { result } = e.detail;
    console.log('Scanned code:', result);
    
    // 震动反馈
    wx.vibrateShort();
    
    // 解析扫码结果
    this.processScanResult(result);
  },

  /**
   * 处理扫码结果
   */
  processScanResult(code) {
    wx.showLoading({ title: '解析中...' });
    
    // 模拟解析设备信息
    setTimeout(() => {
      wx.hideLoading();
      
      const deviceInfo = this.parseDeviceCode(code);
      
      if (deviceInfo) {
        this.showDeviceInfo(deviceInfo);
        this.addToScanHistory(deviceInfo);
      } else {
        wx.showToast({
          title: '无法识别的设备码',
          icon: 'error'
        });
      }
    }, 1500);
  },

  /**
   * 解析设备代码
   */
  parseDeviceCode(code) {
    // 模拟设备信息解析
    const deviceTypes = {
      'GYM001': {
        name: '跑步机 Pro-X1',
        type: 'treadmill',
        icon: '🏃‍♂️',
        location: '二楼有氧区 A区',
        code: 'GYM001',
        status: 'available',
        statusText: '可用',
        features: [
          { name: '最高速度', value: '20 km/h' },
          { name: '坡度范围', value: '0-15%' },
          { name: '心率监测', value: '支持' },
          { name: '娱乐屏幕', value: '21寸' }
        ],
        usage: {
          today: 8,
          avgDuration: 35
        }
      },
      'GYM002': {
        name: '椭圆机 Elite-E2',
        type: 'elliptical',
        icon: '🚴‍♂️',
        location: '二楼有氧区 B区',
        code: 'GYM002',
        status: 'busy',
        statusText: '使用中',
        features: [
          { name: '阻力等级', value: '1-25级' },
          { name: '步幅', value: '51cm' },
          { name: '程序', value: '12种' },
          { name: '用户存储', value: '4个' }
        ],
        usage: {
          today: 12,
          avgDuration: 28
        }
      },
      'L-056': {
        name: '储物柜 056号',
        type: 'locker',
        icon: '🔒',
        location: '一楼更衣区',
        code: 'L-056',
        status: 'available',
        statusText: '可用',
        features: [
          { name: '大小', value: '大柜' },
          { name: '锁类型', value: '密码锁' },
          { name: '电源', value: '有充电口' },
          { name: '租用费', value: '免费' }
        ]
      },
      'S-08': {
        name: '淋浴间 08号',
        type: 'shower',
        icon: '🚿',
        location: '二楼淋浴区',
        code: 'S-08',
        status: 'available',
        statusText: '可用',
        features: [
          { name: '类型', value: '独立淋浴间' },
          { name: '热水', value: '24小时' },
          { name: '设施', value: '洗发水,沐浴露' },
          { name: '预约时长', value: '30分钟' }
        ]
      }
    };
    
    return deviceTypes[code] || null;
  },

  /**
   * 显示设备信息
   */
  showDeviceInfo(deviceInfo) {
    this.setData({
      currentDevice: deviceInfo,
      showDeviceModal: true
    });
  },

  /**
   * 添加到扫码历史
   */
  addToScanHistory(deviceInfo) {
    const newHistoryItem = {
      id: Date.now(),
      name: deviceInfo.name,
      description: deviceInfo.location,
      icon: deviceInfo.icon,
      time: Date.now(),
      timeAgo: '刚刚',
      status: 'success',
      statusText: '已连接'
    };
    
    const updatedHistory = [newHistoryItem, ...this.data.scanHistory.slice(0, 9)]; // 保留最近10条
    
    this.setData({ scanHistory: updatedHistory });
    this.saveScanHistory();
  },

  /**
   * 摄像头控制
   */
  onToggleFlash() {
    const flashOn = !this.data.flashOn;
    this.setData({ flashOn });
    
    // 注意：微信小程序的camera组件flash属性需要重新设置
    // 这里只是状态切换，实际控制需要重新渲染camera组件
  },

  onManualScan() {
    // 手动拍照扫描
    wx.showToast({
      title: '请对准二维码',
      icon: 'none'
    });
  },

  onCameraStop() {
    console.log('Camera stopped');
  },

  onCameraError(e) {
    console.error('Camera error:', e.detail);
    this.setData({
      showCamera: false,
      cameraStatus: '摄像头启动失败'
    });
  },

  /**
   * 快速功能
   */
  onQuickFunction(e) {
    const { type } = e.currentTarget.dataset;
    
    switch (type) {
      case 'equipment':
        wx.showToast({
          title: '请扫描器材二维码',
          icon: 'none'
        });
        break;
      case 'locker':
        wx.showToast({
          title: '请扫描储物柜二维码',
          icon: 'none'
        });
        break;
      case 'shower':
        wx.showToast({
          title: '请扫描淋浴间二维码',
          icon: 'none'
        });
        break;
      case 'course':
        wx.showToast({
          title: '请扫描课程签到码',
          icon: 'none'
        });
        break;
    }
  },

  /**
   * 扫码历史
   */
  onHistoryItemTap(e) {
    const { item } = e.currentTarget.dataset;
    
    // 根据历史记录重新连接设备
    const deviceInfo = this.parseDeviceCode(item.name.split(' ')[1]); // 提取设备代码
    if (deviceInfo) {
      this.showDeviceInfo(deviceInfo);
    }
  },

  onClearHistory() {
    wx.showModal({
      title: '清空历史记录',
      content: '确定要清空所有扫码历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ scanHistory: [] });
          this.saveScanHistory();
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 手动输入
   */
  onShowManualInput() {
    this.setData({ showManualInput: true });
  },

  onCloseManualInput() {
    this.setData({
      showManualInput: false,
      manualDeviceCode: ''
    });
  },

  onDeviceCodeInput(e) {
    this.setData({ manualDeviceCode: e.detail.value.toUpperCase() });
  },

  onSelectRecentCode(e) {
    const { code } = e.currentTarget.dataset;
    this.setData({ manualDeviceCode: code });
  },

  onManualConnect() {
    const { manualDeviceCode } = this.data;
    
    if (!manualDeviceCode) {
      wx.showToast({
        title: '请输入设备编号',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ showManualInput: false });
    this.processScanResult(manualDeviceCode);
  },

  /**
   * 设备连接
   */
  onConnectDevice() {
    const { currentDevice } = this.data;
    
    if (!currentDevice) return;
    
    if (currentDevice.status === 'busy') {
      wx.showToast({
        title: '设备正在使用中',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '连接中...' });
    
    // 模拟连接过程
    setTimeout(() => {
      wx.hideLoading();
      this.setData({ showDeviceModal: false });
      
      // 根据设备类型执行不同操作
      this.handleDeviceConnection(currentDevice);
    }, 2000);
  },

  handleDeviceConnection(device) {
    switch (device.type) {
      case 'treadmill':
      case 'elliptical':
        wx.showModal({
          title: '设备已启动',
          content: `${device.name} 已成功启动，请开始您的训练。训练结束后请记得关闭设备。`,
          showCancel: false,
          confirmText: '开始训练'
        });
        break;
      case 'locker':
        wx.showModal({
          title: '储物柜已开启',
          content: '请放入您的物品并设置密码。使用完毕后请记得清空储物柜。',
          showCancel: false,
          confirmText: '知道了'
        });
        break;
      case 'shower':
        wx.showModal({
          title: '淋浴间已预约',
          content: '已为您预约30分钟使用时间，请在10分钟内前往使用。',
          showCancel: false,
          confirmText: '前往使用'
        });
        break;
    }
    
    // 更新设备状态
    store.updateEquipmentStatus(device.code, 'busy');
  },

  onCloseDeviceModal() {
    this.setData({ showDeviceModal: false });
  },

  /**
   * 帮助
   */
  onShowHelp() {
    this.setData({ showHelp: true });
  },

  onCloseHelp() {
    this.setData({ showHelp: false });
  },

  /**
   * 返回
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 数据持久化
   */
  loadScanHistory() {
    try {
      const savedHistory = wx.getStorageSync('scanHistory');
      if (savedHistory && savedHistory.length > 0) {
        // 更新时间显示
        const updatedHistory = savedHistory.map(item => ({
          ...item,
          timeAgo: this.formatTimeAgo(item.time)
        }));
        this.setData({ scanHistory: updatedHistory });
      }
    } catch (error) {
      console.error('Failed to load scan history:', error);
    }
  },

  saveScanHistory() {
    try {
      wx.setStorageSync('scanHistory', this.data.scanHistory);
    } catch (error) {
      console.error('Failed to save scan history:', error);
    }
  },

  formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else {
      return `${days}天前`;
    }
  },

  /**
   * 页面卸载时清理
   */
  onUnload() {
    this.saveScanHistory();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'FitFocus智能扫码 - 轻松连接健身设备',
      path: '/pages/scanner/scanner',
      imageUrl: '/assets/images/scanner-share.jpg'
    };
  }
}); 