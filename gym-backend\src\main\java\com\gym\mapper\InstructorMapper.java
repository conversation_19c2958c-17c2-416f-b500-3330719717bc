package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.InstructorInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教练信息 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface InstructorMapper extends BaseMapper<InstructorInfo> {

    /**
     * 查询可用教练列表
     */
    List<InstructorInfo> selectAvailableInstructors();

    /**
     * 根据专业特长查询教练
     */
    List<InstructorInfo> selectBySpecialties(@Param("specialty") String specialty);

    /**
     * 查询评分最高的教练
     */
    List<InstructorInfo> selectTopRatedInstructors(@Param("limit") Integer limit);

    /**
     * 更新教练评分
     */
    int updateRating(@Param("instructorId") Long instructorId, 
                    @Param("rating") Double rating);

    /**
     * 更新教练学员数
     */
    int updateTotalStudents(@Param("instructorId") Long instructorId, 
                           @Param("increment") Integer increment);
} 