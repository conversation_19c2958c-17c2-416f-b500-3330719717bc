<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 扫码健身</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .scanner-frame {
            width: 250px;
            height: 250px;
            border: 2px solid #fff;
            border-radius: 20px;
            position: relative;
            margin: 0 auto;
        }
        .scanner-corners {
            position: absolute;
            width: 30px;
            height: 30px;
        }
        .corner-tl {
            top: -2px;
            left: -2px;
            border-top: 4px solid #667eea;
            border-left: 4px solid #667eea;
            border-radius: 20px 0 0 0;
        }
        .corner-tr {
            top: -2px;
            right: -2px;
            border-top: 4px solid #667eea;
            border-right: 4px solid #667eea;
            border-radius: 0 20px 0 0;
        }
        .corner-bl {
            bottom: -2px;
            left: -2px;
            border-bottom: 4px solid #667eea;
            border-left: 4px solid #667eea;
            border-radius: 0 0 0 20px;
        }
        .corner-br {
            bottom: -2px;
            right: -2px;
            border-bottom: 4px solid #667eea;
            border-right: 4px solid #667eea;
            border-radius: 0 0 20px 0;
        }
        .scan-line {
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            animation: scan 2s linear infinite;
        }
        @keyframes scan {
            0% { top: 0; opacity: 1; }
            50% { opacity: 1; }
            100% { top: 100%; opacity: 0; }
        }
        .equipment-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-black h-screen overflow-hidden">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 px-6 py-6 text-white" style="height: calc(100vh - 44px);">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <button class="w-10 h-10 bg-white/20 backdrop-blur-lg rounded-full flex items-center justify-center">
                <i class="fas fa-arrow-left text-white"></i>
            </button>
            <h1 class="text-xl font-semibold">扫码健身</h1>
            <button class="w-10 h-10 bg-white/20 backdrop-blur-lg rounded-full flex items-center justify-center">
                <i class="fas fa-flashlight text-white"></i>
            </button>
        </div>

        <!-- Scanner Area -->
        <div class="text-center mb-8">
            <div class="scanner-frame mx-auto mb-6 relative">
                <div class="scanner-corners corner-tl"></div>
                <div class="scanner-corners corner-tr"></div>
                <div class="scanner-corners corner-bl"></div>
                <div class="scanner-corners corner-br"></div>
                <div class="scan-line"></div>
                
                <!-- Camera View Simulation -->
                <div class="w-full h-full bg-gray-800 rounded-[18px] flex items-center justify-center">
                    <div class="text-6xl text-gray-600">
                        <i class="fas fa-camera"></i>
                    </div>
                </div>
            </div>
            
            <p class="text-white/80 text-sm mb-2">将设备二维码放入框内进行扫描</p>
            <p class="text-white/60 text-xs">请确保二维码清晰可见</p>
        </div>

        <!-- Quick Access -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold mb-4">快速启动</h3>
            <div class="grid grid-cols-2 gap-4">
                <button class="equipment-card rounded-xl p-4 text-center">
                    <i class="fas fa-running text-blue-400 text-2xl mb-2"></i>
                    <div class="text-white font-medium text-sm">跑步机</div>
                    <div class="text-white/60 text-xs mt-1">5台可用</div>
                </button>
                <button class="equipment-card rounded-xl p-4 text-center">
                    <i class="fas fa-dumbbell text-purple-400 text-2xl mb-2"></i>
                    <div class="text-white font-medium text-sm">力量区</div>
                    <div class="text-white/60 text-xs mt-1">2台可用</div>
                </button>
                <button class="equipment-card rounded-xl p-4 text-center">
                    <i class="fas fa-bicycle text-green-400 text-2xl mb-2"></i>
                    <div class="text-white font-medium text-sm">单车房</div>
                    <div class="text-white/60 text-xs mt-1">12台可用</div>
                </button>
                <button class="equipment-card rounded-xl p-4 text-center">
                    <i class="fas fa-swimmer text-cyan-400 text-2xl mb-2"></i>
                    <div class="text-white font-medium text-sm">游泳池</div>
                    <div class="text-white/60 text-xs mt-1">开放中</div>
                </button>
            </div>
        </div>

        <!-- Recent Usage -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-4">最近使用</h3>
            <div class="space-y-3">
                <div class="equipment-card rounded-xl p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-running text-blue-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-white text-sm">跑步机 TM-005</div>
                            <div class="text-white/60 text-xs">今天 14:30 · 使用30分钟</div>
                        </div>
                        <button class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                            再次使用
                        </button>
                    </div>
                </div>
                
                <div class="equipment-card rounded-xl p-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-dumbbell text-purple-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-white text-sm">史密斯机 SM-002</div>
                            <div class="text-white/60 text-xs">昨天 16:00 · 使用45分钟</div>
                        </div>
                        <button class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                            再次使用
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Action -->
        <div class="mt-auto">
            <button class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white py-4 rounded-xl font-semibold text-lg">
                手动输入设备编号
            </button>
        </div>
    </div>

    <!-- Success Modal (Hidden by default) -->
    <div id="successModal" class="fixed inset-0 bg-black/80 flex items-center justify-center px-6 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-sm w-full text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">设备启动成功</h3>
            <p class="text-gray-600 text-sm mb-6">跑步机 TM-005 已为您启动<br>请开始您的训练</p>
            <div class="space-y-3">
                <button class="w-full bg-indigo-600 text-white py-3 rounded-xl font-medium">
                    开始番茄钟训练
                </button>
                <button class="w-full bg-gray-100 text-gray-600 py-3 rounded-xl font-medium">
                    稍后开始
                </button>
            </div>
        </div>
    </div>

    <script>
        // Simulate QR code scan
        setTimeout(() => {
            document.getElementById('successModal').classList.remove('hidden');
        }, 3000);
    </script>
</body>
</html> 