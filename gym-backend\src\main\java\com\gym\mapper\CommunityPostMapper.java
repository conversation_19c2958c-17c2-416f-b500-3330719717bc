package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gym.entity.CommunityPost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 社区动态 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface CommunityPostMapper extends BaseMapper<CommunityPost> {

    /**
     * 分页查询动态（带搜索条件）
     */
    IPage<CommunityPost> selectPageWithCondition(Page<CommunityPost> page,
                                                 @Param("postType") Integer postType,
                                                 @Param("keyword") String keyword,
                                                 @Param("topicId") Long topicId);

    /**
     * 根据用户ID查询动态
     */
    List<CommunityPost> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询热门动态
     */
    List<CommunityPost> selectHotPosts(@Param("limit") Integer limit);

    /**
     * 查询置顶动态
     */
    List<CommunityPost> selectTopPosts();

    /**
     * 根据标签查询动态
     */
    List<CommunityPost> selectByTag(@Param("tag") String tag);

    /**
     * 更新点赞数
     */
    int updateLikeCount(@Param("postId") Long postId, @Param("increment") Integer increment);

    /**
     * 更新评论数
     */
    int updateCommentCount(@Param("postId") Long postId, @Param("increment") Integer increment);

    /**
     * 更新分享数
     */
    int updateShareCount(@Param("postId") Long postId, @Param("increment") Integer increment);

    /**
     * 更新浏览数
     */
    int updateViewCount(@Param("postId") Long postId, @Param("increment") Integer increment);
} 