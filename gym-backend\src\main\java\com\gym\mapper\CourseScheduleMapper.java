package com.gym.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gym.entity.CourseSchedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程排期 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface CourseScheduleMapper extends BaseMapper<CourseSchedule> {

    /**
     * 根据日期范围查询课程排期
     */
    List<CourseSchedule> selectByDateRange(@Param("startDate") LocalDate startDate, 
                                          @Param("endDate") LocalDate endDate);

    /**
     * 根据课程ID查询排期
     */
    List<CourseSchedule> selectByCourseId(@Param("courseId") Long courseId);

    /**
     * 根据教练ID查询排期
     */
    List<CourseSchedule> selectByInstructorId(@Param("instructorId") Long instructorId, 
                                             @Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查询可预约的课程排期
     */
    List<CourseSchedule> selectAvailableSchedules(@Param("startDate") LocalDate startDate, 
                                                 @Param("endDate") LocalDate endDate);

    /**
     * 更新预约人数
     */
    int updateCurrentBookings(@Param("scheduleId") Long scheduleId, 
                             @Param("increment") Integer increment);

    /**
     * 检查教练时间冲突
     */
    int checkInstructorConflict(@Param("instructorId") Long instructorId,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime,
                               @Param("excludeScheduleId") Long excludeScheduleId);
} 