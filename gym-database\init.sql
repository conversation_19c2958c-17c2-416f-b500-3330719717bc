-- 健身房微信小程序数据库初始化脚本
-- 创建时间: 2024-01-15
-- 数据库版本: MySQL 8.0+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `gym_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `gym_db`;

-- ========================================
-- 用户相关表
-- ========================================

-- 用户基础信息表
CREATE TABLE `user_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(50) UNIQUE NOT NULL COMMENT '微信openid',
  `union_id` varchar(50) COMMENT '微信unionid',
  `nickname` varchar(50) COMMENT '昵称',
  `avatar` varchar(200) COMMENT '头像URL',
  `phone` varchar(20) COMMENT '手机号',
  `gender` tinyint COMMENT '性别 1男 2女',
  `birthday` date COMMENT '生日',
  `member_level` tinyint DEFAULT 1 COMMENT '会员等级 1铜卡 2银卡 3VIP',
  `member_expire_date` date COMMENT '会员到期日期',
  `total_points` int DEFAULT 0 COMMENT '总积分',
  `available_points` int DEFAULT 0 COMMENT '可用积分',
  `status` tinyint DEFAULT 1 COMMENT '状态 1正常 2禁用 3冻结',
  `last_login_time` timestamp NULL COMMENT '最后登录时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT 0 COMMENT '逻辑删除 0正常 1删除',
  INDEX `idx_openid` (`openid`),
  INDEX `idx_phone` (`phone`),
  INDEX `idx_member_level` (`member_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 用户体征数据表
CREATE TABLE `user_physical_data` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `height` decimal(5,2) COMMENT '身高(cm)',
  `weight` decimal(5,2) COMMENT '体重(kg)',
  `body_fat_rate` decimal(4,2) COMMENT '体脂率(%)',
  `muscle_mass` decimal(5,2) COMMENT '肌肉量(kg)',
  `bmi` decimal(4,2) COMMENT 'BMI指数',
  `visceral_fat` tinyint COMMENT '内脂等级',
  `body_water` decimal(4,2) COMMENT '体水分(%)',
  `bone_mass` decimal(4,2) COMMENT '骨量(kg)',
  `measurement_date` date COMMENT '测量日期',
  `data_source` tinyint DEFAULT 1 COMMENT '数据来源 1手动录入 2智能设备',
  `device_type` varchar(50) COMMENT '设备类型',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_user_date` (`user_id`, `measurement_date`),
  INDEX `idx_measurement_date` (`measurement_date`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户体征数据表';

-- 用户健身目标表
CREATE TABLE `user_fitness_goal` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '目标ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `goal_type` tinyint NOT NULL COMMENT '目标类型 1减重 2增肌 3塑形 4体能提升',
  `target_weight` decimal(5,2) COMMENT '目标体重(kg)',
  `target_body_fat` decimal(4,2) COMMENT '目标体脂率(%)',
  `weekly_frequency` tinyint COMMENT '每周训练频次',
  `start_date` date COMMENT '开始日期',
  `target_date` date COMMENT '目标完成日期',
  `status` tinyint DEFAULT 1 COMMENT '状态 1进行中 2已完成 3已放弃',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户健身目标表';

-- ========================================
-- 教练相关表
-- ========================================

-- 教练信息表
CREATE TABLE `instructor_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '教练ID',
  `name` varchar(50) NOT NULL COMMENT '教练姓名',
  `gender` tinyint COMMENT '性别 1男 2女',
  `phone` varchar(20) COMMENT '手机号',
  `avatar` varchar(200) COMMENT '头像URL',
  `specialties` varchar(500) COMMENT '专业特长',
  `experience_years` tinyint COMMENT '从业年限',
  `certifications` text COMMENT '资质证书',
  `introduction` text COMMENT '个人介绍',
  `hourly_rate` decimal(8,2) COMMENT '课时费(元/小时)',
  `rating` decimal(3,2) DEFAULT 5.0 COMMENT '评分(1-5)',
  `total_students` int DEFAULT 0 COMMENT '累计学员数',
  `status` tinyint DEFAULT 1 COMMENT '状态 1在职 2休假 3离职',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_status` (`status`),
  INDEX `idx_rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教练信息表';

-- ========================================
-- 课程相关表
-- ========================================

-- 课程信息表
CREATE TABLE `course_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '课程ID',
  `course_name` varchar(100) NOT NULL COMMENT '课程名称',
  `course_type` tinyint NOT NULL COMMENT '课程类型 1瑜伽 2搏击 3动感单车 4私教 5其他',
  `course_category` varchar(50) COMMENT '课程分类',
  `instructor_id` bigint COMMENT '默认教练ID',
  `duration` int COMMENT '课程时长(分钟)',
  `max_capacity` int COMMENT '最大容量',
  `difficulty_level` tinyint COMMENT '难度等级 1初级 2中级 3高级',
  `course_fee` decimal(10,2) COMMENT '课程费用',
  `member_discount` decimal(3,2) DEFAULT 1.0 COMMENT '会员折扣',
  `description` text COMMENT '课程描述',
  `course_image` varchar(200) COMMENT '课程封面图',
  `equipment_needed` varchar(500) COMMENT '所需器材',
  `calories_burn` int COMMENT '预估消耗卡路里',
  `status` tinyint DEFAULT 1 COMMENT '状态 1启用 2禁用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_course_type` (`course_type`),
  INDEX `idx_instructor_id` (`instructor_id`),
  FOREIGN KEY (`instructor_id`) REFERENCES `instructor_info`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程信息表';

-- 课程排期表
CREATE TABLE `course_schedule` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '排期ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `instructor_id` bigint NOT NULL COMMENT '教练ID',
  `room_id` bigint NOT NULL COMMENT '教室ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `current_bookings` int DEFAULT 0 COMMENT '当前预约人数',
  `max_capacity` int NOT NULL COMMENT '最大容量',
  `actual_fee` decimal(10,2) COMMENT '实际收费',
  `booking_deadline` datetime COMMENT '预约截止时间',
  `cancel_deadline` datetime COMMENT '取消截止时间',
  `status` tinyint DEFAULT 1 COMMENT '状态 1正常 2已满 3取消 4已结束',
  `notes` varchar(500) COMMENT '备注信息',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_course_time` (`course_id`, `start_time`),
  INDEX `idx_instructor_time` (`instructor_id`, `start_time`),
  INDEX `idx_room_time` (`room_id`, `start_time`),
  INDEX `idx_start_time` (`start_time`),
  FOREIGN KEY (`course_id`) REFERENCES `course_info`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`instructor_id`) REFERENCES `instructor_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程排期表';

-- 课程预约表
CREATE TABLE `course_booking` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '预约ID',
  `schedule_id` bigint NOT NULL COMMENT '排期ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `booking_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '预约时间',
  `booking_status` tinyint DEFAULT 1 COMMENT '预约状态 1已预约 2已签到 3已完成 4已取消 5缺席',
  `cancel_time` timestamp NULL COMMENT '取消时间',
  `cancel_reason` varchar(200) COMMENT '取消原因',
  `check_in_time` timestamp NULL COMMENT '签到时间',
  `actual_fee` decimal(10,2) COMMENT '实际费用',
  `payment_status` tinyint DEFAULT 0 COMMENT '支付状态 0未支付 1已支付 2已退款',
  `payment_time` timestamp NULL COMMENT '支付时间',
  `refund_amount` decimal(10,2) COMMENT '退款金额',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_schedule_user` (`schedule_id`, `user_id`),
  INDEX `idx_user_booking` (`user_id`, `booking_time`),
  INDEX `idx_booking_status` (`booking_status`),
  UNIQUE KEY `uk_schedule_user` (`schedule_id`, `user_id`),
  FOREIGN KEY (`schedule_id`) REFERENCES `course_schedule`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程预约表';

-- 课程评价表
CREATE TABLE `course_evaluation` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID',
  `booking_id` bigint NOT NULL COMMENT '预约ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `instructor_id` bigint NOT NULL COMMENT '教练ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `rating` tinyint NOT NULL COMMENT '评分 1-5',
  `instructor_rating` tinyint COMMENT '教练评分 1-5',
  `course_rating` tinyint COMMENT '课程评分 1-5',
  `environment_rating` tinyint COMMENT '环境评分 1-5',
  `content` text COMMENT '评价内容',
  `tags` varchar(200) COMMENT '评价标签',
  `is_anonymous` tinyint DEFAULT 0 COMMENT '是否匿名 0否 1是',
  `instructor_reply` text COMMENT '教练回复',
  `reply_time` timestamp NULL COMMENT '回复时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_booking_id` (`booking_id`),
  INDEX `idx_instructor_id` (`instructor_id`),
  INDEX `idx_course_id` (`course_id`),
  FOREIGN KEY (`booking_id`) REFERENCES `course_booking`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`instructor_id`) REFERENCES `instructor_info`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`course_id`) REFERENCES `course_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程评价表';

-- ========================================
-- 场地设备相关表
-- ========================================

-- 教室信息表
CREATE TABLE `room_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '教室ID',
  `room_name` varchar(100) NOT NULL COMMENT '教室名称',
  `room_type` tinyint COMMENT '教室类型 1瑜伽室 2搏击室 3动感单车房 4力量训练区',
  `capacity` int COMMENT '容纳人数',
  `area` decimal(8,2) COMMENT '面积(平方米)',
  `equipment_list` text COMMENT '设备清单',
  `location` varchar(200) COMMENT '位置描述',
  `status` tinyint DEFAULT 1 COMMENT '状态 1可用 2维护中 3停用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教室信息表';

-- 设备信息表
CREATE TABLE `equipment_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '设备ID',
  `equipment_code` varchar(50) UNIQUE NOT NULL COMMENT '设备编号',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_type` tinyint NOT NULL COMMENT '设备类型 1跑步机 2椭圆机 3动感单车 4力量器械 5淋浴间 6其他',
  `brand` varchar(50) COMMENT '品牌',
  `model` varchar(50) COMMENT '型号',
  `location` varchar(100) COMMENT '设备位置',
  `room_id` bigint COMMENT '所属教室ID',
  `qr_code` varchar(200) COMMENT '二维码内容',
  `maintenance_cycle` int COMMENT '维护周期(天)',
  `last_maintenance` date COMMENT '上次维护日期',
  `next_maintenance` date COMMENT '下次维护日期',
  `purchase_date` date COMMENT '购买日期',
  `warranty_expire` date COMMENT '保修到期日期',
  `status` tinyint DEFAULT 1 COMMENT '设备状态 1空闲 2使用中 3维护中 4故障 5停用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_equipment_code` (`equipment_code`),
  INDEX `idx_equipment_type` (`equipment_type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_room_id` (`room_id`),
  FOREIGN KEY (`room_id`) REFERENCES `room_info`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';

-- 设备使用记录表
CREATE TABLE `equipment_usage_log` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '使用记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `equipment_id` bigint NOT NULL COMMENT '设备ID',
  `start_time` datetime NOT NULL COMMENT '开始使用时间',
  `end_time` datetime COMMENT '结束使用时间',
  `duration` int COMMENT '使用时长(分钟)',
  `calories_burned` decimal(8,2) COMMENT '消耗卡路里',
  `distance` decimal(8,2) COMMENT '距离(km)',
  `speed_avg` decimal(6,2) COMMENT '平均速度(km/h)',
  `heart_rate_avg` tinyint COMMENT '平均心率',
  `workout_data` json COMMENT '训练数据JSON',
  `status` tinyint DEFAULT 1 COMMENT '记录状态 1正常结束 2异常结束',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_user_time` (`user_id`, `start_time`),
  INDEX `idx_equipment_time` (`equipment_id`, `start_time`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`equipment_id`) REFERENCES `equipment_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备使用记录表';

-- ========================================
-- 社区功能相关表
-- ========================================

-- 健身打卡表
CREATE TABLE `fitness_checkin` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '打卡ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `checkin_date` date NOT NULL COMMENT '打卡日期',
  `checkin_type` tinyint COMMENT '打卡类型 1到店打卡 2运动打卡 3饮食打卡',
  `workout_type` varchar(50) COMMENT '运动类型',
  `duration` int COMMENT '运动时长(分钟)',
  `calories` int COMMENT '消耗卡路里',
  `mood` tinyint COMMENT '心情 1-5',
  `content` varchar(500) COMMENT '打卡内容',
  `images` json COMMENT '图片列表',
  `location` varchar(100) COMMENT '打卡地点',
  `is_public` tinyint DEFAULT 1 COMMENT '是否公开 0私密 1公开',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `comment_count` int DEFAULT 0 COMMENT '评论数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_user_date` (`user_id`, `checkin_date`),
  INDEX `idx_checkin_date` (`checkin_date`),
  INDEX `idx_is_public` (`is_public`),
  UNIQUE KEY `uk_user_date_type` (`user_id`, `checkin_date`, `checkin_type`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='健身打卡表';

-- 社区动态表
CREATE TABLE `community_post` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '动态ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_type` tinyint DEFAULT 1 COMMENT '动态类型 1普通动态 2健身分享 3饮食分享 4问答',
  `title` varchar(200) COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `images` json COMMENT '图片列表',
  `tags` varchar(200) COMMENT '标签',
  `topic_id` bigint COMMENT '话题ID',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `comment_count` int DEFAULT 0 COMMENT '评论数',
  `share_count` int DEFAULT 0 COMMENT '分享数',
  `view_count` int DEFAULT 0 COMMENT '浏览数',
  `is_top` tinyint DEFAULT 0 COMMENT '是否置顶 0否 1是',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热门 0否 1是',
  `status` tinyint DEFAULT 1 COMMENT '状态 1正常 2隐藏 3删除',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_post_type` (`post_type`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_like_count` (`like_count`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社区动态表';

-- 点赞记录表
CREATE TABLE `like_record` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '点赞ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `target_type` tinyint NOT NULL COMMENT '目标类型 1动态 2打卡 3评论',
  `target_id` bigint NOT NULL COMMENT '目标ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_user_target` (`user_id`, `target_type`, `target_id`),
  INDEX `idx_target` (`target_type`, `target_id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点赞记录表';

-- 评论表
CREATE TABLE `comment` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `target_type` tinyint NOT NULL COMMENT '目标类型 1动态 2打卡',
  `target_id` bigint NOT NULL COMMENT '目标ID',
  `parent_id` bigint COMMENT '父评论ID',
  `reply_to_user_id` bigint COMMENT '回复用户ID',
  `content` varchar(500) NOT NULL COMMENT '评论内容',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `status` tinyint DEFAULT 1 COMMENT '状态 1正常 2删除',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_target` (`target_type`, `target_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_parent_id` (`parent_id`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`reply_to_user_id`) REFERENCES `user_info`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- ========================================
-- 运营管理相关表
-- ========================================

-- 广告位表
CREATE TABLE `advertisement` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '广告ID',
  `title` varchar(200) NOT NULL COMMENT '广告标题',
  `ad_type` tinyint NOT NULL COMMENT '广告类型 1轮播图 2横幅 3信息流 4弹窗',
  `position` varchar(50) COMMENT '展示位置',
  `image_url` varchar(500) COMMENT '图片URL',
  `link_url` varchar(500) COMMENT '跳转链接',
  `link_type` tinyint COMMENT '链接类型 1外部链接 2小程序页面 3无跳转',
  `target_audience` varchar(200) COMMENT '目标用户群体',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `priority` int DEFAULT 0 COMMENT '优先级',
  `click_count` int DEFAULT 0 COMMENT '点击次数',
  `view_count` int DEFAULT 0 COMMENT '展示次数',
  `status` tinyint DEFAULT 1 COMMENT '状态 1启用 2禁用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_ad_type` (`ad_type`),
  INDEX `idx_position` (`position`),
  INDEX `idx_status_time` (`status`, `start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告位表';

-- 优惠券表
CREATE TABLE `coupon` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '优惠券ID',
  `coupon_name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `coupon_type` tinyint NOT NULL COMMENT '优惠券类型 1满减券 2折扣券 3体验券',
  `discount_value` decimal(10,2) COMMENT '优惠金额/折扣值',
  `min_amount` decimal(10,2) COMMENT '使用门槛金额',
  `applicable_courses` varchar(500) COMMENT '适用课程类型',
  `total_quantity` int COMMENT '发放总数量',
  `used_quantity` int DEFAULT 0 COMMENT '已使用数量',
  `per_user_limit` int DEFAULT 1 COMMENT '每人限领数量',
  `valid_days` int COMMENT '有效天数',
  `start_time` datetime COMMENT '生效时间',
  `end_time` datetime COMMENT '失效时间',
  `description` varchar(500) COMMENT '使用说明',
  `status` tinyint DEFAULT 1 COMMENT '状态 1正常 2停用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_coupon_type` (`coupon_type`),
  INDEX `idx_status_time` (`status`, `start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- 用户优惠券表
CREATE TABLE `user_coupon` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '用户优惠券ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `coupon_id` bigint NOT NULL COMMENT '优惠券ID',
  `coupon_code` varchar(50) UNIQUE NOT NULL COMMENT '优惠券码',
  `receive_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  `use_time` timestamp NULL COMMENT '使用时间',
  `expire_time` datetime COMMENT '过期时间',
  `order_id` varchar(50) COMMENT '使用订单号',
  `status` tinyint DEFAULT 1 COMMENT '状态 1未使用 2已使用 3已过期',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_coupon_code` (`coupon_code`),
  INDEX `idx_status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `user_info`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`coupon_id`) REFERENCES `coupon`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- ========================================
-- 系统管理相关表
-- ========================================

-- 系统配置表
CREATE TABLE `system_config` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) UNIQUE NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(50) COMMENT '配置类型',
  `description` varchar(200) COMMENT '配置描述',
  `is_system` tinyint DEFAULT 0 COMMENT '是否系统配置 0否 1是',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE `operation_log` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint COMMENT '操作用户ID',
  `user_type` tinyint COMMENT '用户类型 1普通用户 2管理员',
  `operation_type` varchar(50) COMMENT '操作类型',
  `operation_desc` varchar(500) COMMENT '操作描述',
  `request_method` varchar(10) COMMENT '请求方法',
  `request_url` varchar(500) COMMENT '请求URL',
  `request_params` text COMMENT '请求参数',
  `response_result` text COMMENT '响应结果',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `execution_time` int COMMENT '执行时间(ms)',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_operation_type` (`operation_type`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- ========================================
-- 初始化数据
-- ========================================

-- 插入系统配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('gym.name', '智能健身房', 'string', '健身房名称', 1),
('gym.address', '北京市朝阳区XXX街道XXX号', 'string', '健身房地址', 1),
('gym.phone', '************', 'string', '联系电话', 1),
('gym.business_hours', '06:00-23:00', 'string', '营业时间', 1),
('member.bronze.benefits', '基础训练器材使用', 'string', '铜卡会员权益', 1),
('member.silver.benefits', '基础训练器材+团体课程', 'string', '银卡会员权益', 1),
('member.vip.benefits', '全部器材+课程+私教优惠', 'string', 'VIP会员权益', 1),
('booking.advance_days', '7', 'number', '可提前预约天数', 1),
('booking.cancel_hours', '2', 'number', '取消预约提前时间(小时)', 1),
('points.checkin_daily', '10', 'number', '每日打卡获得积分', 1),
('points.course_complete', '20', 'number', '完成课程获得积分', 1);

-- 插入示例教室数据
INSERT INTO `room_info` (`room_name`, `room_type`, `capacity`, `area`, `equipment_list`, `location`) VALUES
('瑜伽室A', 1, 20, 80.00, '瑜伽垫,瑜伽砖,拉力带', '2楼东侧'),
('瑜伽室B', 1, 15, 60.00, '瑜伽垫,瑜伽球,拉力带', '2楼西侧'),
('搏击训练室', 2, 25, 100.00, '沙袋,拳击手套,护具', '3楼南侧'),
('动感单车房', 3, 30, 120.00, '动感单车30台,音响设备', '3楼北侧'),
('力量训练区A', 4, 40, 200.00, '杠铃,哑铃,综合训练器', '1楼东区'),
('力量训练区B', 4, 35, 180.00, '史密斯机,自由重量区', '1楼西区');

-- 插入示例设备数据
INSERT INTO `equipment_info` (`equipment_code`, `equipment_name`, `equipment_type`, `brand`, `location`, `qr_code`, `room_id`) VALUES
('TM001', '跑步机01', 1, 'Life Fitness', '有氧区东侧', 'TM001', NULL),
('TM002', '跑步机02', 1, 'Life Fitness', '有氧区东侧', 'TM002', NULL),
('TM003', '跑步机03', 1, 'Technogym', '有氧区东侧', 'TM003', NULL),
('EL001', '椭圆机01', 2, 'Precor', '有氧区中央', 'EL001', NULL),
('EL002', '椭圆机02', 2, 'Precor', '有氧区中央', 'EL002', NULL),
('BC001', '动感单车01', 3, 'Spinning', '动感单车房', 'BC001', 4),
('BC002', '动感单车02', 3, 'Spinning', '动感单车房', 'BC002', 4),
('SH001', '淋浴间01', 5, '', '更衣室', 'SH001', NULL),
('SH002', '淋浴间02', 5, '', '更衣室', 'SH002', NULL),
('SH003', '淋浴间03', 5, '', '更衣室', 'SH003', NULL);

-- 插入示例教练数据
INSERT INTO `instructor_info` (`name`, `gender`, `phone`, `specialties`, `experience_years`, `hourly_rate`, `introduction`) VALUES
('张教练', 2, '13800138001', '瑜伽,普拉提', 5, 200.00, '资深瑜伽教练，擅长哈他瑜伽和流瑜伽'),
('李教练', 1, '13800138002', '搏击,体能训练', 8, 250.00, '前职业拳击手，专业搏击训练指导'),
('王教练', 2, '13800138003', '动感单车,有氧训练', 3, 180.00, '活力满满的单车教练，课堂氛围活跃'),
('赵教练', 1, '13800138004', '力量训练,增肌', 10, 300.00, '健美冠军出身，专业力量训练指导'),
('刘教练', 2, '13800138005', '普拉提,康复训练', 6, 220.00, '运动康复专家，擅长体态纠正');

-- 插入示例课程数据
INSERT INTO `course_info` (`course_name`, `course_type`, `instructor_id`, `duration`, `max_capacity`, `difficulty_level`, `course_fee`, `description`) VALUES
('基础哈他瑜伽', 1, 1, 60, 20, 1, 68.00, '适合初学者的瑜伽课程，注重基础体式练习'),
('流瑜伽', 1, 1, 75, 15, 2, 88.00, '动态连贯的瑜伽练习，提升柔韧性和力量'),
('搏击基础班', 2, 2, 45, 25, 1, 78.00, '搏击入门课程，学习基本拳法和步法'),
('高强度搏击', 2, 2, 60, 20, 3, 98.00, '高强度间歇搏击训练，快速燃脂'),
('动感单车', 3, 3, 45, 30, 2, 58.00, '音乐律动的单车课程，燃烧卡路里'),
('力量塑形', 4, 4, 60, 12, 2, 168.00, '个人力量训练指导，塑造完美身材'),
('普拉提塑形', 1, 5, 50, 18, 2, 78.00, '核心力量训练，改善体态');

-- 插入示例课程排期数据
INSERT INTO `course_schedule` (`course_id`, `instructor_id`, `room_id`, `start_time`, `end_time`, `max_capacity`) VALUES
(1, 1, 1, '2024-01-20 09:00:00', '2024-01-20 10:00:00', 20),
(1, 1, 1, '2024-01-20 10:15:00', '2024-01-20 11:15:00', 20),
(2, 1, 1, '2024-01-20 11:30:00', '2024-01-20 12:30:00', 15),
(3, 2, 2, '2024-01-20 14:00:00', '2024-01-20 15:00:00', 25),
(4, 2, 2, '2024-01-20 15:15:00', '2024-01-20 16:15:00', 20),
(5, 3, 3, '2024-01-20 16:30:00', '2024-01-20 17:30:00', 30),
(6, 4, 4, '2024-01-20 17:45:00', '2024-01-20 18:45:00', 12),
(7, 5, 1, '2024-01-20 19:00:00', '2024-01-20 20:00:00', 18);

-- 插入示例课程预约数据
INSERT INTO `course_booking` (`schedule_id`, `user_id`, `booking_status`) VALUES
(1, 1, 1),
(2, 2, 1),
(3, 3, 1),
(4, 4, 1),
(5, 5, 1),
(6, 6, 1),
(7, 7, 1);

-- 插入示例课程评价数据
INSERT INTO `course_evaluation` (`booking_id`, `user_id`, `instructor_id`, `course_id`, `rating`, `content`) VALUES
(1, 1, 1, 1, 5, '课程非常棒，老师讲解清晰，动作标准'),
(2, 2, 1, 1, 4, '课程难度适中，适合初学者'),
(3, 3, 2, 2, 5, '搏击训练很有趣，教练指导到位'),
(4, 4, 2, 2, 3, '课程节奏有点快，需要更多时间练习'),
(5, 5, 3, 3, 5, '动感单车课程很有动力，音乐很棒'),
(6, 6, 4, 4, 4, '力量训练指导很专业，效果明显'),
(7, 7, 5, 1, 5, '普拉提课程让我感觉身体更加灵活');

-- 插入示例用户体征数据
INSERT INTO `user_physical_data` (`user_id`, `height`, `weight`, `body_fat_rate`, `muscle_mass`, `bmi`, `visceral_fat`, `body_water`, `bone_mass`, `measurement_date`) VALUES
(1, 170.00, 65.00, 18.50, 45.00, 22.40, 1, 60.00, 2.50, '2023-12-30'),
(2, 165.00, 58.00, 22.00, 40.00, 21.50, 2, 65.00, 2.00, '2023-12-31'),
(3, 175.00, 72.00, 15.00, 50.00, 23.50, 1, 62.00, 3.00, '2024-01-01'),
(4, 168.00, 60.00, 20.00, 42.00, 21.00, 1, 63.00, 2.80, '2024-01-02'),
(5, 172.00, 68.00, 17.00, 48.00, 22.80, 1, 61.00, 2.60, '2024-01-03'),
(6, 170.00, 62.00, 21.00, 44.00, 21.20, 2, 64.00, 2.20, '2024-01-04'),
(7, 178.00, 75.00, 14.00, 55.00, 23.80, 1, 60.00, 3.20, '2024-01-05');

-- 插入示例用户健身目标数据
INSERT INTO `user_fitness_goal` (`user_id`, `goal_type`, `target_weight`, `target_body_fat`, `weekly_frequency`, `start_date`, `target_date`) VALUES
(1, 1, 60.00, 15.00, 3, '2023-12-01', '2024-03-01'),
(2, 2, 70.00, 20.00, 4, '2023-12-10', '2024-04-10'),
(3, 3, 65.00, 18.00, 5, '2023-12-15', '2024-05-15'),
(4, 1, 62.00, 16.00, 4, '2023-12-20', '2024-03-20'),
(5, 2, 75.00, 22.00, 6, '2023-12-25', '2024-06-25'),
(6, 3, 68.00, 19.00, 5, '2023-12-30', '2024-05-30'),
(7, 1, 61.00, 17.00, 4, '2023-12-31', '2024-03-31');

-- 插入示例设备使用记录数据
INSERT INTO `equipment_usage_log` (`user_id`, `equipment_id`, `start_time`, `end_time`, `duration`, `calories_burned`, `distance`, `speed_avg`, `heart_rate_avg`, `workout_data`) VALUES
(1, 1, '2024-01-18 09:00:00', '2024-01-18 09:30:00', 30, 150.00, 3.00, 10.00, 120, '{"distance": 3.0, "duration": 30, "calories": 150, "speed": 10.0}'),
(2, 2, '2024-01-18 10:00:00', '2024-01-18 10:30:00', 30, 180.00, 3.50, 11.00, 130, '{"distance": 3.5, "duration": 30, "calories": 180, "speed": 11.0}'),
(3, 3, '2024-01-18 11:00:00', '2024-01-18 11:30:00', 30, 200.00, 4.00, 12.00, 140, '{"distance": 4.0, "duration": 30, "calories": 200, "speed": 12.0}'),
(4, 4, '2024-01-18 14:00:00', '2024-01-18 14:30:00', 30, 100.00, 2.00, 8.00, 110, '{"distance": 2.0, "duration": 30, "calories": 100, "speed": 8.0}'),
(5, 5, '2024-01-18 15:00:00', '2024-01-18 15:30:00', 30, 120.00, 2.50, 9.00, 120, '{"distance": 2.5, "duration": 30, "calories": 120, "speed": 9.0}'),
(6, 6, '2024-01-18 16:00:00', '2024-01-18 16:30:00', 30, 150.00, 3.00, 10.00, 130, '{"distance": 3.0, "duration": 30, "calories": 150, "speed": 10.0}'),
(7, 7, '2024-01-18 17:00:00', '2024-01-18 17:30:00', 30, 180.00, 3.50, 11.00, 140, '{"distance": 3.5, "duration": 30, "calories": 180, "speed": 11.0}');

-- 插入示例健身打卡数据
INSERT INTO `fitness_checkin` (`user_id`, `checkin_date`, `checkin_type`, `workout_type`, `duration`, `calories`, `mood`, `content`, `images`, `location`, `is_public`) VALUES
(1, '2024-01-18', 1, '到店打卡', 0, 0, 3, '今天准时到店打卡', NULL, '健身房门口', 1),
(2, '2024-01-18', 1, '到店打卡', 0, 0, 4, '今天到店打卡，准备开始训练', NULL, '健身房门口', 1),
(3, '2024-01-18', 1, '到店打卡', 0, 0, 5, '今天到店打卡，准备开始训练', NULL, '健身房门口', 1),
(4, '2024-01-18', 1, '到店打卡', 0, 0, 3, '今天到店打卡，准备开始训练', NULL, '健身房门口', 1),
(5, '2024-01-18', 1, '到店打卡', 0, 0, 4, '今天到店打卡，准备开始训练', NULL, '健身房门口', 1),
(6, '2024-01-18', 1, '到店打卡', 0, 0, 5, '今天到店打卡，准备开始训练', NULL, '健身房门口', 1),
(7, '2024-01-18', 1, '到店打卡', 0, 0, 3, '今天到店打卡，准备开始训练', NULL, '健身房门口', 1);

-- 插入示例社区动态数据
INSERT INTO `community_post` (`user_id`, `post_type`, `title`, `content`, `images`, `tags`, `topic_id`, `is_top`, `is_hot`, `status`) VALUES
(1, 1, '今天开始我的健身之旅', '大家好，我是新来的会员。今天开始我的健身之旅，感觉非常棒！', NULL, '健身,打卡', NULL, 0, 0, 1),
(2, 1, '今天完成了我的第一个搏击课程', '今天完成了我的第一个搏击课程，感觉非常棒！教练指导很专业。', NULL, '搏击,课程', NULL, 0, 0, 1),
(3, 1, '今天尝试了动感单车，很棒！', '今天尝试了动感单车，很棒！音乐和氛围都很好。', NULL, '动感单车,课程', NULL, 0, 0, 1),
(4, 1, '今天完成了我的第一次力量训练', '今天完成了我的第一次力量训练，感觉肌肉在燃烧！', NULL, '力量训练,增肌', NULL, 0, 0, 1),
(5, 1, '今天尝试了普拉提，很有效果！', '今天尝试了普拉提，很有效果！教练指导很细致。', NULL, '普拉提,康复', NULL, 0, 0, 1),
(6, 1, '今天完成了我的第一次瑜伽课程', '今天完成了我的第一次瑜伽课程，感觉身体更加灵活了。', NULL, '瑜伽,课程', NULL, 0, 0, 1),
(7, 1, '今天完成了我的第一次有氧训练', '今天完成了我的第一次有氧训练，感觉身体更加轻盈了。', NULL, '有氧训练,燃脂', NULL, 0, 0, 1);

-- 插入示例点赞记录数据
INSERT INTO `like_record` (`user_id`, `target_type`, `target_id`) VALUES
(1, 1, 1),
(2, 1, 2),
(3, 1, 3),
(4, 1, 4),
(5, 1, 5),
(6, 1, 6),
(7, 1, 7);

-- 插入示例评论数据
INSERT INTO `comment` (`user_id`, `target_type`, `target_id`, `content`) VALUES
(1, 1, 1, '欢迎新会员！'),
(2, 1, 2, '课程体验如何？'),
(3, 1, 3, '动感单车很棒！'),
(4, 1, 4, '力量训练很累但有效！'),
(5, 1, 5, '普拉提很舒服，推荐！'),
(6, 1, 6, '瑜伽课程很放松。'),
(7, 1, 7, '有氧训练很燃脂！');

-- 插入示例广告数据
INSERT INTO `advertisement` (`title`, `ad_type`, `position`, `image_url`, `link_url`, `link_type`, `target_audience`, `start_time`, `end_time`, `priority`) VALUES
('欢迎新会员！', 1, '首页轮播', 'https://via.placeholder.com/1200x400', 'https://example.com/new-member', 1, '所有用户', '2024-01-01 00:00:00', '2024-06-30 23:59:59', 1),
('搏击课程优惠！', 2, '顶部横幅', 'https://via.placeholder.com/1200x100', 'https://example.com/搏击优惠', 1, '所有用户', '2024-01-10 00:00:00', '2024-02-28 23:59:59', 2),
('动感单车体验课', 3, '信息流广告', 'https://via.placeholder.com/300x250', 'https://example.com/动感单车体验', 1, '所有用户', '2024-01-15 00:00:00', '2024-01-31 23:59:59', 3),
('VIP会员特惠', 4, '弹窗广告', 'https://via.placeholder.com/300x250', 'https://example.com/VIP优惠', 1, '所有用户', '2024-01-20 00:00:00', '2024-03-31 23:59:59', 4);

-- 插入示例优惠券数据
INSERT INTO `coupon` (`coupon_name`, `coupon_type`, `discount_value`, `min_amount`, `applicable_courses`, `total_quantity`, `used_quantity`, `per_user_limit`, `valid_days`, `start_time`, `end_time`, `description`, `status`) VALUES
('减重优惠券', 1, 50.00, 500.00, '所有课程', 100, 0, 1, 30, '2024-01-10 00:00:00', '2024-04-10 23:59:59', '减重课程满500减50', 1),
('搏击折扣券', 2, 0.80, 0.00, '搏击课程', 50, 0, 1, 15, '2024-01-15 00:00:00', '2024-02-15 23:59:59', '搏击课程8折', 1),
('动感单车体验券', 3, 0.00, 0.00, '动感单车', 20, 0, 1, 7, '2024-01-20 00:00:00', '2024-01-27 23:59:59', '免费体验动感单车一次', 1),
('VIP会员体验券', 3, 0.00, 0.00, '私教课程', 10, 0, 1, 30, '2024-01-25 00:00:00', '2024-02-25 23:59:59', '免费体验VIP私教一次', 1);

-- 插入示例用户优惠券数据
INSERT INTO `user_coupon` (`user_id`, `coupon_id`, `coupon_code`, `receive_time`, `status`) VALUES
(1, 1, 'COUPON001', '2024-01-10 10:00:00', 1),
(2, 2, 'COUPON002', '2024-01-15 11:00:00', 1),
(3, 3, 'COUPON003', '2024-01-20 12:00:00', 1),
(4, 4, 'COUPON004', '2024-01-25 13:00:00', 1),
(5, 1, 'COUPON005', '2024-01-10 14:00:00', 1),
(6, 2, 'COUPON006', '2024-01-15 15:00:00', 1),
(7, 3, 'COUPON007', '2024-01-20 16:00:00', 1);

-- 插入示例操作日志数据
INSERT INTO `operation_log` (`user_id`, `user_type`, `operation_type`, `operation_desc`, `request_method`, `request_url`, `request_params`, `response_result`, `ip_address`, `user_agent`, `execution_time`) VALUES
(NULL, 2, '系统初始化', '数据库初始化脚本执行', 'GET', '/init', NULL, '{"status": "success", "message": "数据库初始化成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 100),
(NULL, 2, '数据导入', '插入系统配置数据', 'POST', '/api/import/config', '{"config_key": "gym.name", "config_value": "智能健身房", "config_type": "string", "description": "健身房名称", "is_system": 1}', '{"status": "success", "message": "系统配置数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 50),
(NULL, 2, '数据导入', '插入示例教室数据', 'POST', '/api/import/room', '{"room_name": "瑜伽室A", "room_type": 1, "capacity": 20, "area": 80.00, "equipment_list": "瑜伽垫,瑜伽砖,拉力带", "location": "2楼东侧"}', '{"status": "success", "message": "教室数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 60),
(NULL, 2, '数据导入', '插入示例设备数据', 'POST', '/api/import/equipment', '{"equipment_code": "TM001", "equipment_name": "跑步机01", "equipment_type": 1, "brand": "Life Fitness", "location": "有氧区东侧", "qr_code": "TM001", "room_id": null}', '{"status": "success", "message": "设备数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 70),
(NULL, 2, '数据导入', '插入示例教练数据', 'POST', '/api/import/instructor', '{"name": "张教练", "gender": 2, "phone": "13800138001", "specialties": "瑜伽,普拉提", "experience_years": 5, "hourly_rate": 200.00, "introduction": "资深瑜伽教练，擅长哈他瑜伽和流瑜伽"}', '{"status": "success", "message": "教练数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 80),
(NULL, 2, '数据导入', '插入示例课程数据', 'POST', '/api/import/course', '{"course_name": "基础哈他瑜伽", "course_type": 1, "instructor_id": 1, "duration": 60, "max_capacity": 20, "difficulty_level": 1, "course_fee": 68.00, "description": "适合初学者的瑜伽课程，注重基础体式练习"}', '{"status": "success", "message": "课程数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 90),
(NULL, 2, '数据导入', '插入示例课程排期数据', 'POST', '/api/import/course-schedule', '{"course_id": 1, "instructor_id": 1, "room_id": 1, "start_time": "2024-01-20 09:00:00", "end_time": "2024-01-20 10:00:00", "max_capacity": 20}', '{"status": "success", "message": "课程排期数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 100),
(NULL, 2, '数据导入', '插入示例课程预约数据', 'POST', '/api/import/course-booking', '{"schedule_id": 1, "user_id": 1, "booking_status": 1}', '{"status": "success", "message": "课程预约数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 110),
(NULL, 2, '数据导入', '插入示例课程评价数据', 'POST', '/api/import/course-evaluation', '{"booking_id": 1, "user_id": 1, "instructor_id": 1, "course_id": 1, "rating": 5, "content": "课程非常棒，老师讲解清晰，动作标准"}', '{"status": "success", "message": "课程评价数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 120),
(NULL, 2, '数据导入', '插入示例用户体征数据', 'POST', '/api/import/user-physical-data', '{"user_id": 1, "height": 170.00, "weight": 65.00, "body_fat_rate": 18.50, "muscle_mass": 45.00, "bmi": 22.40, "visceral_fat": 1, "body_water": 60.00, "bone_mass": 2.50, "measurement_date": "2023-12-30"}', '{"status": "success", "message": "用户体征数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 130),
(NULL, 2, '数据导入', '插入示例用户健身目标数据', 'POST', '/api/import/user-fitness-goal', '{"user_id": 1, "goal_type": 1, "target_weight": 60.00, "target_body_fat": 15.00, "weekly_frequency": 3, "start_date": "2023-12-01", "target_date": "2024-03-01"}', '{"status": "success", "message": "用户健身目标数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 140),
(NULL, 2, '数据导入', '插入示例设备使用记录数据', 'POST', '/api/import/equipment-usage-log', '{"user_id": 1, "equipment_id": 1, "start_time": "2024-01-18 09:00:00", "end_time": "2024-01-18 09:30:00", "duration": 30, "calories_burned": 150.00, "distance": 3.00, "speed_avg": 10.00, "heart_rate_avg": 120, "workout_data": {"distance": 3.0, "duration": 30, "calories": 150, "speed": 10.0}}', '{"status": "success", "message": "设备使用记录数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 150),
(NULL, 2, '数据导入', '插入示例健身打卡数据', 'POST', '/api/import/fitness-checkin', '{"user_id": 1, "checkin_date": "2024-01-18", "checkin_type": 1, "workout_type": "到店打卡", "duration": 0, "calories": 0, "mood": 3, "content": "今天准时到店打卡", "images": null, "location": "健身房门口", "is_public": 1}', '{"status": "success", "message": "健身打卡数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 160),
(NULL, 2, '数据导入', '插入示例社区动态数据', 'POST', '/api/import/community-post', '{"user_id": 1, "post_type": 1, "title": "今天开始我的健身之旅", "content": "大家好，我是新来的会员。今天开始我的健身之旅，感觉非常棒！", "images": null, "tags": "健身,打卡", "topic_id": null, "is_top": 0, "is_hot": 0, "status": 1}', '{"status": "success", "message": "社区动态数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 170),
(NULL, 2, '数据导入', '插入示例点赞记录数据', 'POST', '/api/import/like-record', '{"user_id": 1, "target_type": 1, "target_id": 1}', '{"status": "success", "message": "点赞记录数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 180),
(NULL, 2, '数据导入', '插入示例评论数据', 'POST', '/api/import/comment', '{"user_id": 1, "target_type": 1, "target_id": 1, "content": "欢迎新会员！"}', '{"status": "success", "message": "评论数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 190),
(NULL, 2, '数据导入', '插入示例广告数据', 'POST', '/api/import/advertisement', '{"title": "欢迎新会员！", "ad_type": 1, "position": "首页轮播", "image_url": "https://via.placeholder.com/1200x400", "link_url": "https://example.com/new-member", "link_type": 1, "target_audience": "所有用户", "start_time": "2024-01-01 00:00:00", "end_time": "2024-06-30 23:59:59", "priority": 1}', '{"status": "success", "message": "广告数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 200),
(NULL, 2, '数据导入', '插入示例优惠券数据', 'POST', '/api/import/coupon', '{"coupon_name": "减重优惠券", "coupon_type": 1, "discount_value": 50.00, "min_amount": 500.00, "applicable_courses": "所有课程", "total_quantity": 100, "used_quantity": 0, "per_user_limit": 1, "valid_days": 30, "start_time": "2024-01-10 00:00:00", "end_time": "2024-04-10 23:59:59", "description": "减重课程满500减50", "status": 1}', '{"status": "success", "message": "优惠券数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 210),
(NULL, 2, '数据导入', '插入示例用户优惠券数据', 'POST', '/api/import/user-coupon', '{"user_id": 1, "coupon_id": 1, "coupon_code": "COUPON001", "receive_time": "2024-01-10 10:00:00", "status": 1}', '{"status": "success", "message": "用户优惠券数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 220),
(NULL, 2, '数据导入', '插入示例操作日志数据', 'POST', '/api/import/operation-log', '{"user_id": null, "user_type": 2, "operation_type": "系统初始化", "operation_desc": "数据库初始化脚本执行", "request_method": "GET", "request_url": "/init", "request_params": null, "response_result": "{\"status\": \"success\", \"message\": \"数据库初始化成功\"}", "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "execution_time": 100}', '{"status": "success", "message": "操作日志数据导入成功"}', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 230); 