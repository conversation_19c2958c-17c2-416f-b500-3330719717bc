package com.gym.util;

import lombok.extern.slf4j.Slf4j;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 二维码工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
public class QRCodeUtil {

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成设备二维码内容
     */
    public static String generateEquipmentQRCode(String equipmentCode) {
        return String.format("EQUIPMENT:%s:%s", equipmentCode, System.currentTimeMillis());
    }

    /**
     * 生成课程二维码内容
     */
    public static String generateCourseQRCode(Long scheduleId) {
        return String.format("COURSE:%d:%s", scheduleId, System.currentTimeMillis());
    }

    /**
     * 生成签到二维码内容
     */
    public static String generateCheckinQRCode(String location) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format("CHECKIN:%s:%s", location, timestamp);
    }

    /**
     * 生成随机二维码ID
     */
    public static String generateRandomQRCodeId() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    /**
     * 生成短码（用于简化二维码内容）
     */
    public static String generateShortCode(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }

    /**
     * 解析设备二维码内容
     */
    public static String parseEquipmentCode(String qrContent) {
        if (qrContent != null && qrContent.startsWith("EQUIPMENT:")) {
            String[] parts = qrContent.split(":");
            if (parts.length >= 2) {
                return parts[1];
            }
        }
        return null;
    }

    /**
     * 解析课程二维码内容
     */
    public static Long parseCourseScheduleId(String qrContent) {
        if (qrContent != null && qrContent.startsWith("COURSE:")) {
            String[] parts = qrContent.split(":");
            if (parts.length >= 2) {
                try {
                    return Long.parseLong(parts[1]);
                } catch (NumberFormatException e) {
                    log.error("解析课程二维码失败: {}", qrContent);
                }
            }
        }
        return null;
    }

    /**
     * 解析签到二维码内容
     */
    public static String parseCheckinLocation(String qrContent) {
        if (qrContent != null && qrContent.startsWith("CHECKIN:")) {
            String[] parts = qrContent.split(":");
            if (parts.length >= 2) {
                return parts[1];
            }
        }
        return null;
    }

    /**
     * 验证二维码是否有效（基于时间戳）
     */
    public static boolean isQRCodeValid(String qrContent, long validDurationMillis) {
        if (qrContent == null) return false;
        
        String[] parts = qrContent.split(":");
        if (parts.length >= 3) {
            try {
                long timestamp = Long.parseLong(parts[2]);
                long currentTime = System.currentTimeMillis();
                return (currentTime - timestamp) <= validDurationMillis;
            } catch (NumberFormatException e) {
                log.error("解析二维码时间戳失败: {}", qrContent);
            }
        }
        return false;
    }

    /**
     * 获取二维码类型
     */
    public static String getQRCodeType(String qrContent) {
        if (qrContent == null) return "UNKNOWN";
        
        if (qrContent.startsWith("EQUIPMENT:")) return "EQUIPMENT";
        if (qrContent.startsWith("COURSE:")) return "COURSE";
        if (qrContent.startsWith("CHECKIN:")) return "CHECKIN";
        
        return "UNKNOWN";
    }

    /**
     * 生成带过期时间的临时二维码
     */
    public static String generateTempQRCode(String type, String content, int validMinutes) {
        long expireTime = System.currentTimeMillis() + (validMinutes * 60 * 1000L);
        return String.format("TEMP:%s:%s:%d", type, content, expireTime);
    }

    /**
     * 验证临时二维码是否过期
     */
    public static boolean isTempQRCodeExpired(String qrContent) {
        if (qrContent != null && qrContent.startsWith("TEMP:")) {
            String[] parts = qrContent.split(":");
            if (parts.length >= 4) {
                try {
                    long expireTime = Long.parseLong(parts[3]);
                    return System.currentTimeMillis() > expireTime;
                } catch (NumberFormatException e) {
                    log.error("解析临时二维码过期时间失败: {}", qrContent);
                    return true;
                }
            }
        }
        return true;
    }
} 