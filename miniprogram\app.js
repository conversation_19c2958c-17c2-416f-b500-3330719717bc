// 小程序主入口文件
import { createStore } from './utils/store'
import { request } from './utils/request'

App({
  globalData: {
    userInfo: null,
    token: null,
    systemInfo: null,
    baseUrl: 'https://api.fitfocus.com', // 后端API地址
    version: '1.0.0'
  },

  // 小程序初始化
  onLaunch(options) {
    console.log('FitFocus小程序启动', options)
    
    // 获取系统信息
    this.getSystemInfo()
    
    // 检查更新
    this.checkUpdate()
    
    // 初始化用户信息
    this.initUserInfo()
    
    // 初始化全局Store
    this.store = createStore()
  },

  // 小程序显示
  onShow(options) {
    console.log('小程序显示', options)
    
    // 检查登录状态
    this.checkLoginStatus()
  },

  // 小程序隐藏
  onHide() {
    console.log('小程序隐藏')
  },

  // 获取系统信息
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()
    this.globalData.systemInfo = systemInfo
    
    // 设置顶部安全区域
    const menuButton = wx.getMenuButtonBoundingClientRect()
    const statusBarHeight = systemInfo.statusBarHeight || 0
    const navBarHeight = menuButton.top + menuButton.height + (menuButton.top - statusBarHeight)
    
    this.globalData.navBarHeight = navBarHeight
    this.globalData.statusBarHeight = statusBarHeight
    
    console.log('系统信息:', {
      systemInfo,
      navBarHeight,
      statusBarHeight
    })
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        wx.showToast({
          title: '更新失败，请重新启动小程序',
          icon: 'none'
        })
      })
    }
  },

  // 初始化用户信息
  initUserInfo() {
    try {
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('userInfo')
      
      if (token && userInfo) {
        this.globalData.token = token
        this.globalData.userInfo = userInfo
        console.log('读取本地用户信息成功', userInfo)
      }
    } catch (error) {
      console.error('读取本地用户信息失败', error)
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = this.globalData.token
    if (!token) {
      console.log('未登录，跳转到登录页')
      return
    }
    
    // 验证token有效性
    request({
      url: '/api/user/verify-token',
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`
      }
    }).then(res => {
      if (res.code !== 200) {
        // token失效，清除本地数据
        this.logout()
      }
    }).catch(error => {
      console.error('验证token失败', error)
      this.logout()
    })
  },

  // 用户登录
  login(userInfo) {
    this.globalData.userInfo = userInfo.userInfo
    this.globalData.token = userInfo.token
    
    // 保存到本地存储
    try {
      wx.setStorageSync('userInfo', userInfo.userInfo)
      wx.setStorageSync('token', userInfo.token)
      console.log('用户登录成功', userInfo)
    } catch (error) {
      console.error('保存用户信息失败', error)
    }
  },

  // 用户退出登录
  logout() {
    this.globalData.userInfo = null
    this.globalData.token = null
    
    // 清除本地存储
    try {
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('token')
      console.log('用户退出登录')
    } catch (error) {
      console.error('清除用户信息失败', error)
    }
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 全局错误处理
  onError(error) {
    console.error('小程序全局错误:', error)
    
    // 上报错误到服务器
    if (this.globalData.token) {
      request({
        url: '/api/system/error-report',
        method: 'POST',
        data: {
          error: error,
          userAgent: this.globalData.systemInfo?.model || '',
          version: this.globalData.version,
          timestamp: Date.now()
        }
      }).catch(err => {
        console.error('错误上报失败', err)
      })
    }
  },

  // 页面不存在
  onPageNotFound(res) {
    console.log('页面不存在:', res)
    wx.navigateTo({
      url: '/pages/home/<USER>'
    })
  }
}) 