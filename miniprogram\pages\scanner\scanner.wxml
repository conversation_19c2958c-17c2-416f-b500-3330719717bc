<!-- 扫码页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="back-btn" bindtap="onBack">
      <text class="back-icon">〈</text>
    </view>
    <text class="page-title">设备扫码</text>
    <view class="help-btn" bindtap="onShowHelp">
      <text class="help-icon">❓</text>
    </view>
  </view>

  <!-- 扫码区域 -->
  <view class="scanner-section">
    <view class="scanner-container">
      <!-- 相机视图 -->
      <camera 
        class="camera-view" 
        device-position="back" 
        flash="off" 
        bindscancode="onScanCode"
        bindstop="onCameraStop"
        binderror="onCameraError"
        wx:if="{{showCamera}}"
      >
        <!-- 扫描框 -->
        <cover-view class="scan-frame">
          <cover-view class="frame-corner corner-tl"></cover-view>
          <cover-view class="frame-corner corner-tr"></cover-view>
          <cover-view class="frame-corner corner-bl"></cover-view>
          <cover-view class="frame-corner corner-br"></cover-view>
          <cover-view class="scan-line"></cover-view>
        </cover-view>
        
        <!-- 扫描提示 -->
        <cover-view class="scan-tip">
          <cover-view class="tip-text">将二维码放入框内，即可自动扫描</cover-view>
        </cover-view>
        
        <!-- 控制按钮 -->
        <cover-view class="camera-controls">
          <cover-view class="control-btn" bindtap="onToggleFlash">
            <cover-view class="control-icon">{{flashOn ? '🔦' : '💡'}}</cover-view>
            <cover-view class="control-text">{{flashOn ? '关闭' : '闪光灯'}}</cover-view>
          </cover-view>
          
          <cover-view class="control-btn scan-btn" bindtap="onManualScan">
            <cover-view class="control-icon">📷</cover-view>
            <cover-view class="control-text">拍照扫描</cover-view>
          </cover-view>
          
          <cover-view class="control-btn" bindtap="onShowManualInput">
            <cover-view class="control-icon">⌨️</cover-view>
            <cover-view class="control-text">手动输入</cover-view>
          </cover-view>
        </cover-view>
      </camera>
      
      <!-- 摄像头未开启时的占位 -->
      <view class="camera-placeholder" wx:if="{{!showCamera}}">
        <view class="placeholder-icon">📷</view>
        <text class="placeholder-text">{{cameraStatus}}</text>
        <button class="enable-camera-btn" bindtap="onEnableCamera" wx:if="{{!cameraEnabled}}">
          开启摄像头
        </button>
      </view>
    </view>
  </view>

  <!-- 功能区域 -->
  <view class="function-section">
    <!-- 快速功能 -->
    <view class="quick-functions">
      <view class="function-item" bindtap="onQuickFunction" data-type="equipment">
        <view class="function-icon">🏋️‍♂️</view>
        <text class="function-text">健身器材</text>
      </view>
      
      <view class="function-item" bindtap="onQuickFunction" data-type="locker">
        <view class="function-icon">🔒</view>
        <text class="function-text">储物柜</text>
      </view>
      
      <view class="function-item" bindtap="onQuickFunction" data-type="shower">
        <view class="function-icon">🚿</view>
        <text class="function-text">淋浴间</text>
      </view>
      
      <view class="function-item" bindtap="onQuickFunction" data-type="course">
        <view class="function-icon">📚</view>
        <text class="function-text">课程签到</text>
      </view>
    </view>
    
    <!-- 扫码历史 -->
    <view class="scan-history">
      <view class="history-header">
        <text class="history-title">最近扫码</text>
        <view class="clear-history" bindtap="onClearHistory">
          <text class="clear-text">清空</text>
        </view>
      </view>
      
      <view class="history-list" wx:if="{{scanHistory.length > 0}}">
        <view 
          class="history-item" 
          wx:for="{{scanHistory}}" 
          wx:key="id"
          data-item="{{item}}"
          bindtap="onHistoryItemTap"
        >
          <view class="history-icon">{{item.icon}}</view>
          <view class="history-info">
            <text class="history-name">{{item.name}}</text>
            <text class="history-desc">{{item.description}}</text>
            <text class="history-time">{{item.timeAgo}}</text>
          </view>
          <view class="history-status {{item.status}}">
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>
      </view>
      
      <view class="empty-history" wx:else>
        <text class="empty-icon">📱</text>
        <text class="empty-text">暂无扫码记录</text>
      </view>
    </view>
  </view>

  <!-- 手动输入弹窗 -->
  <view class="manual-input-modal {{showManualInput ? 'show' : ''}}" catchtap="onCloseManualInput">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">手动输入设备编号</text>
        <view class="close-btn" bindtap="onCloseManualInput">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-body">
        <view class="input-section">
          <text class="input-label">设备编号</text>
          <input 
            class="device-input"
            placeholder="请输入设备编号（如：GYM001）"
            value="{{manualDeviceCode}}"
            bindinput="onDeviceCodeInput"
            maxlength="20"
          />
        </view>
        
        <view class="recent-codes" wx:if="{{recentCodes.length > 0}}">
          <text class="recent-title">最近使用</text>
          <view class="codes-list">
            <view 
              class="code-item"
              wx:for="{{recentCodes}}"
              wx:key="*this"
              data-code="{{item}}"
              bindtap="onSelectRecentCode"
            >
              <text class="code-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onCloseManualInput">取消</button>
        <button class="modal-btn primary" bindtap="onManualConnect" disabled="{{!manualDeviceCode}}">连接</button>
      </view>
    </view>
  </view>

  <!-- 设备信息弹窗 -->
  <view class="device-modal {{showDeviceModal ? 'show' : ''}}" catchtap="onCloseDeviceModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">设备信息</text>
        <view class="close-btn" bindtap="onCloseDeviceModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="device-info" wx:if="{{currentDevice}}">
        <view class="device-main-info">
          <view class="device-icon">{{currentDevice.icon}}</view>
          <view class="device-details">
            <text class="device-name">{{currentDevice.name}}</text>
            <text class="device-location">{{currentDevice.location}}</text>
            <text class="device-code">设备编号：{{currentDevice.code}}</text>
          </view>
          <view class="device-status {{currentDevice.status}}">
            <text class="status-dot"></text>
            <text class="status-text">{{currentDevice.statusText}}</text>
          </view>
        </view>
        
        <view class="device-features">
          <view class="feature-item" wx:for="{{currentDevice.features}}" wx:key="name">
            <text class="feature-name">{{item.name}}</text>
            <text class="feature-value">{{item.value}}</text>
          </view>
        </view>
        
        <view class="device-usage" wx:if="{{currentDevice.usage}}">
          <text class="usage-title">使用统计</text>
          <view class="usage-stats">
            <view class="usage-item">
              <text class="usage-label">今日使用</text>
              <text class="usage-value">{{currentDevice.usage.today}}次</text>
            </view>
            <view class="usage-item">
              <text class="usage-label">平均时长</text>
              <text class="usage-value">{{currentDevice.usage.avgDuration}}分钟</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onCloseDeviceModal">取消</button>
        <button 
          class="modal-btn primary" 
          bindtap="onConnectDevice"
          disabled="{{currentDevice && currentDevice.status === 'busy'}}"
        >
          {{currentDevice && currentDevice.status === 'busy' ? '设备使用中' : '开始使用'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 帮助弹窗 -->
  <view class="help-modal {{showHelp ? 'show' : ''}}" catchtap="onCloseHelp">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">扫码帮助</text>
        <view class="close-btn" bindtap="onCloseHelp">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="help-content">
        <view class="help-section">
          <text class="help-section-title">如何扫码？</text>
          <text class="help-text">1. 将手机对准设备上的二维码</text>
          <text class="help-text">2. 保持适当距离，让二维码完全显示在扫描框内</text>
          <text class="help-text">3. 等待自动识别，或点击拍照扫描</text>
        </view>
        
        <view class="help-section">
          <text class="help-section-title">扫码后可以做什么？</text>
          <text class="help-text">• 启动健身器材</text>
          <text class="help-text">• 预约和使用淋浴间</text>
          <text class="help-text">• 开启储物柜</text>
          <text class="help-text">• 课程签到</text>
        </view>
        
        <view class="help-section">
          <text class="help-section-title">扫码失败怎么办？</text>
          <text class="help-text">• 检查网络连接</text>
          <text class="help-text">• 清洁摄像头镜头</text>
          <text class="help-text">• 调整光线亮度</text>
          <text class="help-text">• 尝试手动输入设备编号</text>
        </view>
      </view>
    </view>
  </view>
</view> 