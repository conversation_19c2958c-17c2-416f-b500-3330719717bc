/* 社区页面样式 */
.container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 0 32rpx 120rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 0 32rpx;
  margin-top: var(--status-bar-height, 44px);
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: #5a67d8;
}

.btn-icon {
  color: #ffffff;
  font-size: 32rpx;
}

/* 功能导航 */
.feature-nav {
  display: flex;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 16rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.nav-item.active {
  background: #667eea;
}

.nav-icon {
  font-size: 32rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.nav-item.active .nav-text {
  color: #ffffff;
}

/* 快速打卡 */
.quick-checkin {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.checkin-streak {
  font-size: 24rpx;
  color: #10b981;
  font-weight: 500;
}

.checkin-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.checkin-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  transition: all 0.2s ease;
  position: relative;
}

.checkin-btn:active {
  transform: scale(0.95);
}

.checkin-btn.completed {
  background: #f0f9ff;
  border-color: #10b981;
}

.checkin-icon {
  font-size: 32rpx;
}

.checkin-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.checkin-btn.completed .checkin-text {
  color: #10b981;
}

.checkin-mark {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background: #10b981;
  border-radius: 50%;
  color: #ffffff;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 社区动态 */
.community-posts {
  margin-bottom: 32rpx;
}

.posts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.posts-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.filter-dropdown {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #f3f4f6;
  border-radius: 16rpx;
  padding: 12rpx 20rpx;
}

.filter-text {
  font-size: 24rpx;
  color: #667eea;
}

.filter-arrow {
  font-size: 20rpx;
  color: #667eea;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 动态卡片 */
.post-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.post-item:active {
  transform: translateY(2rpx);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.post-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.post-type-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.post-type-badge.workout {
  background: #fef3c7;
  color: #d97706;
}

.post-type-badge.food {
  background: #fce7f3;
  color: #be185d;
}

.post-type-badge.moment {
  background: #e0f2fe;
  color: #0369a1;
}

.post-type-badge.checkin {
  background: #f0fdf4;
  color: #16a34a;
}

.badge-text {
  font-weight: 500;
}

/* 动态内容 */
.post-content {
  margin-bottom: 24rpx;
}

.post-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.post-images {
  display: grid;
  gap: 8rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.post-images:has(.single) {
  grid-template-columns: 1fr;
}

.post-images:not(:has(.single)) {
  grid-template-columns: repeat(3, 1fr);
}

.post-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
}

.post-image.single {
  height: 400rpx;
}

.workout-data {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.data-item {
  text-align: center;
}

.data-label {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.data-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.food-data {
  background: #fdf2f8;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.food-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.food-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #be185d;
}

.food-calories {
  font-size: 24rpx;
  color: #9d174d;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 16rpx;
}

.location-icon {
  font-size: 24rpx;
}

.location-text {
  font-size: 24rpx;
  color: #6b7280;
}

/* 互动区域 */
.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24rpx;
  border-top: 2rpx solid #f3f4f6;
}

.post-actions .action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background: #f8fafc;
  border: none;
  transition: all 0.2s ease;
  width: auto;
  height: auto;
}

.post-actions .action-btn:active {
  transform: scale(0.95);
  background: #f3f4f6;
}

.post-actions .action-btn.like.active {
  background: #fef2f2;
}

.action-icon {
  font-size: 28rpx;
}

.action-count,
.action-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.post-actions .action-btn.like.active .action-count {
  color: #ef4444;
}

/* 加载更多和空状态 */
.load-more {
  text-align: center;
  padding: 40rpx;
  margin-top: 24rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #667eea;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #9ca3af;
}

/* 筛选菜单 */
.filter-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.filter-menu.show {
  opacity: 1;
  pointer-events: all;
}

.filter-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.filter-menu.show .filter-content {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.filter-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.filter-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.filter-option.active {
  background: #667eea;
}

.option-text {
  font-size: 28rpx;
  color: #374151;
}

.filter-option.active .option-text {
  color: #ffffff;
}

.option-check {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 发布动态弹窗 */
.publish-modal,
.comments-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.publish-modal.show,
.comments-modal.show {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.modal-body {
  padding: 40rpx;
}

/* 动态类型选择 */
.post-type-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  transition: all 0.2s ease;
}

.type-option.active {
  background: #667eea;
  border-color: #667eea;
}

.type-icon {
  font-size: 32rpx;
}

.type-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.type-option.active .type-text {
  color: #ffffff;
}

/* 内容输入 */
.content-input {
  margin-bottom: 32rpx;
  position: relative;
}

.content-textarea {
  width: 100%;
  min-height: 200rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  bottom: 16rpx;
  right: 24rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

/* 图片上传 */
.image-upload {
  margin-bottom: 32rpx;
}

.uploaded-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.remove-image {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.add-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: #f3f4f6;
  border: 2rpx dashed #d1d5db;
  border-radius: 12rpx;
  aspect-ratio: 1;
}

.add-icon {
  font-size: 32rpx;
  color: #9ca3af;
}

.add-text {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 运动数据输入 */
.workout-input {
  margin-bottom: 32rpx;
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.input-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.number-input {
  flex: 1;
  height: 80rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1f2937;
}

.picker-display {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 20rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #1f2937;
}

.picker-arrow {
  font-size: 24rpx;
  color: #6b7280;
}

/* 位置选择 */
.location-selector {
  margin-bottom: 32rpx;
}

.location-option {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 12rpx;
}

.location-icon {
  font-size: 24rpx;
}

.location-text {
  font-size: 28rpx;
  color: #6b7280;
}

/* 弹窗动作 */
.modal-actions {
  display: flex;
  gap: 24rpx;
  padding: 0 40rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #667eea;
  color: #ffffff;
}

.modal-btn.secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.modal-btn:active {
  transform: scale(0.98);
}

/* 评论相关 */
.comments-list {
  max-height: 500rpx;
  overflow-y: auto;
  margin-bottom: 32rpx;
}

.comment-item {
  display: flex;
  gap: 16rpx;
  padding: 24rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.comment-user {
  font-size: 24rpx;
  font-weight: 600;
  color: #1f2937;
}

.comment-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
}

.comment-time {
  font-size: 20rpx;
  color: #9ca3af;
}

.no-comments {
  text-align: center;
  padding: 60rpx 40rpx;
}

.no-comments-text {
  font-size: 28rpx;
  color: #9ca3af;
}

.comment-input-area {
  display: flex;
  gap: 16rpx;
  padding: 24rpx;
  border-top: 2rpx solid #f3f4f6;
}

.comment-input {
  flex: 1;
  height: 80rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1f2937;
}

.send-comment-btn {
  height: 80rpx;
  padding: 0 32rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
} 