# 健身房微信小程序项目

## 项目概述

这是一个基于微信小程序的智能健身房管理系统，采用前后端分离架构，为健身房提供完整的数字化解决方案。

### 技术架构

- **前端**: 微信小程序 + Vue3 风格开发
- **后端**: Spring Boot 2.7+ + MyBatis Plus + MySQL + Redis
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+

## 项目结构

```
gym-weixinxiaochengxu/
├── gym-backend/                 # Spring Boot后端项目
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/gym/
│   │   │   │   ├── common/      # 公共组件
│   │   │   │   ├── entity/      # 实体类
│   │   │   │   ├── mapper/      # MyBatis映射
│   │   │   │   ├── service/     # 业务服务
│   │   │   │   ├── controller/  # 控制器
│   │   │   │   └── config/      # 配置类
│   │   │   └── resources/
│   │   └── test/
│   └── pom.xml                  # Maven配置
├── gym-frontend/                # 微信小程序前端
│   ├── pages/                   # 页面目录
│   ├── components/              # 组件目录
│   ├── utils/                   # 工具类
│   ├── static/                  # 静态资源
│   ├── app.js                   # 应用逻辑
│   ├── app.json                 # 应用配置
│   └── project.config.json      # 项目配置
├── gym-database/                # 数据库脚本
│   └── init.sql                 # 初始化脚本
├── docs/                        # 项目文档
└── 健身房小程序开发文档.md      # 详细开发文档
```

## 核心功能模块

### 1. 用户系统
- ✅ 微信登录授权
- ✅ 会员等级体系（铜卡/银卡/VIP）
- ✅ 用户体征数据管理
- 🔄 智能穿戴设备数据同步

### 2. 课程管理
- ✅ 团体课程预约（瑜伽/搏击/动感单车）
- ✅ 私教课程管理
- ✅ 双向评价系统
- 🔄 课程提醒推送

### 3. 设备物联
- ✅ 设备二维码扫码启动
- ✅ 实时设备状态监控
- ✅ 淋浴间空位查询
- 🔄 使用数据统计

### 4. 数据可视化
- ✅ 训练数据图表展示
- ✅ 体测报告生成
- 🔄 运动成果分享模板

### 5. 社区功能
- ✅ 健身打卡系统
- ✅ 饮食分享功能
- ✅ 社区互动（点赞/评论）

### 6. 运营管理
- ✅ 广告位管理
- ✅ 优惠券系统
- 🔄 会员权益管理

## 开发进度

### 已完成 ✅
1. **项目架构搭建**
   - Spring Boot后端框架初始化
   - Maven依赖配置完成
   - 微信小程序前端项目结构
   - 数据库表结构设计

2. **核心组件开发**
   - 统一响应结果封装（Result类）
   - 响应状态码定义（ResultCode枚举）
   - 用户实体类设计
   - 小程序首页界面

3. **数据库设计**
   - 完整的数据表结构（15+张表）
   - 用户、课程、设备、社区等核心业务表
   - 索引和外键约束设计
   - 示例数据初始化

### 进行中 🔄
1. **后端API开发**
   - 用户认证与授权
   - 课程管理接口
   - 设备管理接口

2. **前端页面开发**
   - 页面组件实现
   - 状态管理配置
   - UI交互优化

### 待开发 📋
1. **集成功能**
   - 微信支付集成
   - 推送消息服务
   - 第三方设备SDK对接

2. **高级功能**
   - 数据分析与报表
   - AI智能推荐
   - 性能优化

## 快速开始

### 后端启动

1. **环境要求**
   - JDK 8+
   - Maven 3.6+
   - MySQL 8.0+
   - Redis 6.0+

2. **数据库配置**
   ```bash
   # 1. 创建数据库
   mysql -u root -p
   source gym-database/init.sql

   # 2. 修改配置文件
   # 编辑 gym-backend/src/main/resources/application.yml
   # 修改数据库连接信息
   ```

3. **启动应用**
   ```bash
   cd gym-backend
   mvn clean install
   mvn spring-boot:run
   ```

4. **验证启动**
   - 访问: http://localhost:8080/api
   - Druid监控: http://localhost:8080/api/druid/

### 前端启动

1. **环境要求**
   - 微信开发者工具
   - 已注册的小程序AppID

2. **项目导入**
   ```bash
   # 1. 打开微信开发者工具
   # 2. 导入项目：选择 gym-frontend 目录
   # 3. 填入AppID（测试可使用测试号）
   ```

3. **配置修改**
   ```javascript
   // 修改 gym-frontend/utils/config.js
   const config = {
     baseUrl: 'http://localhost:8080/api',  // 后端地址
     appId: 'your-miniapp-appid'            // 小程序AppID
   }
   ```

## API接口文档

### 基础接口

#### 用户登录
```http
POST /api/user/login
Content-Type: application/json

{
  "code": "微信登录code",
  "nickname": "用户昵称",
  "avatar": "头像URL"
}
```

#### 获取课程列表
```http
GET /api/courses?type=1&date=2024-01-15&page=1&size=10
```

#### 设备扫码启动
```http
POST /api/equipment/start
Content-Type: application/json

{
  "equipmentCode": "TM001",
  "userId": 123
}
```

详细API文档请参考：[健身房小程序开发文档.md](./健身房小程序开发文档.md)

## 开发规范

### 代码规范
- **后端**: 遵循阿里巴巴Java开发规范
- **前端**: 遵循微信小程序开发规范
- **数据库**: 统一使用下划线命名
- **接口**: RESTful API设计风格

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构代码
test: 测试相关
chore: 构建过程或辅助工具变动
```

## 部署说明

### 生产环境部署

1. **后端部署**
   ```bash
   # 1. 打包应用
   mvn clean package -Dmaven.test.skip=true
   
   # 2. 部署到服务器
   java -jar target/gym-backend-1.0.0.jar
   ```

2. **前端发布**
   - 在微信开发者工具中点击"上传"
   - 登录微信公众平台进行版本发布
   - 配置服务器域名白名单

### 环境配置

- **开发环境**: 本地MySQL + Redis
- **测试环境**: 云服务器部署
- **生产环境**: 集群部署 + 负载均衡

## 联系信息

- **开发团队**: gym-dev-team
- **项目负责人**: [项目经理姓名]
- **技术支持**: [技术负责人联系方式]

## License

Copyright © 2024 智能健身房小程序. All rights reserved.

---

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 完成项目基础架构搭建
- ✅ 实现数据库设计和初始化
- ✅ 完成核心实体类和工具类
- ✅ 创建小程序基础页面框架 