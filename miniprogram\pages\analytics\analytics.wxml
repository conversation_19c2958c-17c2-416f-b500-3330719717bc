<!-- 数据分析页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">数据分析</text>
    <view class="header-actions">
      <view class="action-btn" bindtap="onShareData">
        <text class="btn-icon">📊</text>
      </view>
    </view>
  </view>

  <!-- 时间筛选 -->
  <view class="time-filter">
    <view class="filter-tabs">
      <view 
        class="tab-item {{timeFilter === item.value ? 'active' : ''}}"
        wx:for="{{timeTabs}}"
        wx:key="value"
        data-filter="{{item.value}}"
        bindtap="onTimeFilterChange"
      >
        <text class="tab-text">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 核心数据统计 -->
  <view class="stats-overview">
    <view class="stats-grid">
      <view class="stat-card">
        <view class="stat-icon">🏋️‍♂️</view>
        <view class="stat-content">
          <text class="stat-value">{{overviewStats.totalWorkouts}}</text>
          <text class="stat-label">总训练次数</text>
          <text class="stat-change" wx:if="{{overviewStats.workoutChange > 0}}">+{{overviewStats.workoutChange}}%</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon">⏱️</view>
        <view class="stat-content">
          <text class="stat-value">{{overviewStats.totalHours}}</text>
          <text class="stat-label">训练时长</text>
          <text class="stat-change" wx:if="{{overviewStats.timeChange > 0}}">+{{overviewStats.timeChange}}%</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon">🔥</view>
        <view class="stat-content">
          <text class="stat-value">{{overviewStats.totalCalories}}</text>
          <text class="stat-label">消耗卡路里</text>
          <text class="stat-change" wx:if="{{overviewStats.calorieChange > 0}}">+{{overviewStats.calorieChange}}%</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon">📈</view>
        <view class="stat-content">
          <text class="stat-value">{{overviewStats.consistency}}</text>
          <text class="stat-label">训练一致性</text>
          <text class="stat-change" wx:if="{{overviewStats.consistencyChange > 0}}">+{{overviewStats.consistencyChange}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 训练频率图表 -->
  <view class="chart-section">
    <view class="section-header">
      <text class="section-title">训练频率</text>
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-color workout"></view>
          <text class="legend-text">训练次数</text>
        </view>
      </view>
    </view>
    
    <view class="chart-container">
      <canvas 
        class="chart-canvas" 
        canvas-id="workoutChart" 
        disable-scroll="true"
        bindtouchstart="onChartTouch"
        bindtouchmove="onChartTouch"
        bindtouchend="onChartTouch"
      ></canvas>
      
      <!-- 图表数据提示 -->
      <view class="chart-tooltip {{showTooltip ? 'show' : ''}}" style="left: {{tooltipX}}rpx; top: {{tooltipY}}rpx;">
        <text class="tooltip-date">{{tooltipData.date}}</text>
        <text class="tooltip-value">{{tooltipData.value}} 次训练</text>
      </view>
    </view>
  </view>

  <!-- 体征数据趋势 -->
  <view class="body-data-section">
    <view class="section-header">
      <text class="section-title">体征数据</text>
      <view class="data-selector">
        <picker 
          mode="selector" 
          range="{{bodyDataTypes}}" 
          range-key="label"
          value="{{selectedBodyDataIndex}}"
          bindchange="onBodyDataTypeChange"
        >
          <view class="picker-display">
            <text class="picker-text">{{bodyDataTypes[selectedBodyDataIndex].label}}</text>
            <text class="picker-arrow">〉</text>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="body-data-chart">
      <canvas 
        class="chart-canvas" 
        canvas-id="bodyDataChart" 
        disable-scroll="true"
      ></canvas>
    </view>
    
    <!-- 最新数据 -->
    <view class="latest-data">
      <view class="data-item">
        <text class="data-label">当前</text>
        <text class="data-value">{{currentBodyData.current}} {{currentBodyData.unit}}</text>
      </view>
      <view class="data-item">
        <text class="data-label">变化</text>
        <text class="data-value {{currentBodyData.change > 0 ? 'positive' : 'negative'}}">
          {{currentBodyData.change > 0 ? '+' : ''}}{{currentBodyData.change}} {{currentBodyData.unit}}
        </text>
      </view>
      <view class="data-item">
        <text class="data-label">目标</text>
        <text class="data-value">{{currentBodyData.target}} {{currentBodyData.unit}}</text>
      </view>
    </view>
  </view>

  <!-- 训练类型分布 -->
  <view class="workout-distribution">
    <view class="section-header">
      <text class="section-title">训练类型分布</text>
    </view>
    
    <view class="distribution-chart">
      <canvas 
        class="pie-chart" 
        canvas-id="distributionChart" 
        disable-scroll="true"
      ></canvas>
      
      <view class="distribution-legend">
        <view 
          class="legend-item"
          wx:for="{{workoutDistribution}}"
          wx:key="type"
        >
          <view class="legend-color" style="background-color: {{item.color}};"></view>
          <text class="legend-text">{{item.name}}</text>
          <text class="legend-percent">{{item.percentage}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 成就与里程碑 -->
  <view class="achievements-section">
    <view class="section-header">
      <text class="section-title">成就与里程碑</text>
      <view class="view-all" bindtap="onViewAllAchievements">
        <text class="view-all-text">查看全部</text>
        <text class="arrow">〉</text>
      </view>
    </view>
    
    <scroll-view class="achievements-scroll" scroll-x="true">
      <view class="achievements-list">
        <view 
          class="achievement-card {{item.unlocked ? 'unlocked' : 'locked'}}"
          wx:for="{{recentAchievements}}"
          wx:key="id"
          data-achievement="{{item}}"
          bindtap="onAchievementDetail"
        >
          <view class="achievement-icon">{{item.icon}}</view>
          <text class="achievement-name">{{item.name}}</text>
          <text class="achievement-desc">{{item.description}}</text>
          <view class="achievement-progress" wx:if="{{!item.unlocked}}">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.progress}}%;"></view>
            </view>
            <text class="progress-text">{{item.current}}/{{item.target}}</text>
          </view>
          <view class="achievement-date" wx:if="{{item.unlocked}}">
            <text class="date-text">{{item.unlockDate}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 个人记录 -->
  <view class="personal-records">
    <view class="section-header">
      <text class="section-title">个人记录</text>
    </view>
    
    <view class="records-grid">
      <view 
        class="record-item"
        wx:for="{{personalRecords}}"
        wx:key="type"
      >
        <view class="record-icon">{{item.icon}}</view>
        <view class="record-content">
          <text class="record-name">{{item.name}}</text>
          <text class="record-value">{{item.value}} {{item.unit}}</text>
          <text class="record-date">{{item.date}}</text>
        </view>
        <view class="record-trend">
          <text class="trend-icon" wx:if="{{item.isImproved}}">📈</text>
          <text class="trend-icon" wx:else>📊</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据导出 -->
  <view class="export-section">
    <view class="section-header">
      <text class="section-title">数据导出</text>
    </view>
    
    <view class="export-options">
      <button class="export-btn" bindtap="onExportData" data-type="csv">
        <text class="btn-icon">📋</text>
        <text class="btn-text">导出CSV</text>
      </button>
      
      <button class="export-btn" bindtap="onShareReport">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享报告</text>
      </button>
    </view>
  </view>
</view> 