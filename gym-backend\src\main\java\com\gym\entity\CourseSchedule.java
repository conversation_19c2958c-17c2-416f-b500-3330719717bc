package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程排期实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("course_schedule")
public class CourseSchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("course_id")
    private Long courseId;

    @TableField("instructor_id")
    private Long instructorId;

    @TableField("room_id")
    private Long roomId;

    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("current_bookings")
    private Integer currentBookings;

    @TableField("max_capacity")
    private Integer maxCapacity;

    @TableField("actual_fee")
    private BigDecimal actualFee;

    @TableField("booking_deadline")
    private LocalDateTime bookingDeadline;

    @TableField("cancel_deadline")
    private LocalDateTime cancelDeadline;

    @TableField("status")
    private Integer status;

    @TableField("notes")
    private String notes;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "正常";
        }
        switch (status) {
            case 1: return "正常";
            case 2: return "已满";
            case 3: return "取消";
            case 4: return "已结束";
            default: return "正常";
        }
    }

    /**
     * 判断是否可以预约
     */
    public boolean canBook() {
        if (status == null || status != 1) {
            return false;
        }
        if (currentBookings != null && maxCapacity != null && currentBookings >= maxCapacity) {
            return false;
        }
        if (bookingDeadline != null && LocalDateTime.now().isAfter(bookingDeadline)) {
            return false;
        }
        return true;
    }

    /**
     * 判断是否可以取消预约
     */
    public boolean canCancel() {
        if (cancelDeadline != null && LocalDateTime.now().isAfter(cancelDeadline)) {
            return false;
        }
        return status != null && (status == 1 || status == 2);
    }

    /**
     * 判断课程是否已开始
     */
    public boolean isStarted() {
        return startTime != null && LocalDateTime.now().isAfter(startTime);
    }

    /**
     * 判断课程是否已结束
     */
    public boolean isFinished() {
        return endTime != null && LocalDateTime.now().isAfter(endTime);
    }
} 