package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 社区动态实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("community_post")
public class CommunityPost implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("post_type")
    private Integer postType;

    @TableField("title")
    private String title;

    @TableField("content")
    private String content;

    @TableField("images")
    private String images;

    @TableField("tags")
    private String tags;

    @TableField("topic_id")
    private Long topicId;

    @TableField("like_count")
    private Integer likeCount;

    @TableField("comment_count")
    private Integer commentCount;

    @TableField("share_count")
    private Integer shareCount;

    @TableField("view_count")
    private Integer viewCount;

    @TableField("is_top")
    private Integer isTop;

    @TableField("is_hot")
    private Integer isHot;

    @TableField("status")
    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取动态类型名称
     */
    public String getPostTypeName() {
        if (postType == null) {
            return "普通动态";
        }
        switch (postType) {
            case 1: return "普通动态";
            case 2: return "健身分享";
            case 3: return "饮食分享";
            case 4: return "问答";
            default: return "普通动态";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "正常";
        }
        switch (status) {
            case 1: return "正常";
            case 2: return "隐藏";
            case 3: return "删除";
            default: return "正常";
        }
    }

    /**
     * 判断是否置顶
     */
    public boolean isTopPost() {
        return isTop != null && isTop == 1;
    }

    /**
     * 判断是否热门
     */
    public boolean isHotPost() {
        return isHot != null && isHot == 1;
    }

    /**
     * 判断是否可见
     */
    public boolean isVisible() {
        return status != null && status == 1;
    }

    /**
     * 判断是否为健身分享
     */
    public boolean isFitnessShare() {
        return postType != null && postType == 2;
    }

    /**
     * 判断是否为饮食分享
     */
    public boolean isDietShare() {
        return postType != null && postType == 3;
    }

    /**
     * 判断是否为问答
     */
    public boolean isQuestion() {
        return postType != null && postType == 4;
    }

    /**
     * 获取热度分数（根据点赞、评论、分享、浏览量计算）
     */
    public Integer getHotScore() {
        int score = 0;
        if (likeCount != null) score += likeCount * 2;
        if (commentCount != null) score += commentCount * 3;
        if (shareCount != null) score += shareCount * 5;
        if (viewCount != null) score += viewCount;
        return score;
    }

    /**
     * 获取互动总数
     */
    public Integer getTotalInteractions() {
        int total = 0;
        if (likeCount != null) total += likeCount;
        if (commentCount != null) total += commentCount;
        if (shareCount != null) total += shareCount;
        return total;
    }
} 