# 健身房微信小程序开发文档

## 项目概述

### 项目名称
健身房智能管理微信小程序

### 项目简介
一款集用户管理、课程预约、设备物联、数据分析、社区互动于一体的综合性健身房管理小程序。

### 技术栈
- **前端**: Vue3 + 微信小程序原生框架
- **后端**: Spring Boot 2.7+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **其他**: 微信支付API、推送服务、第三方设备SDK

---

## 需求分析

### 核心功能模块

#### 1. 用户系统
- **会员等级体系**: 铜卡/银卡/VIP三级会员制度
- **用户注册**: 收集身高、体重、体脂率等体征数据
- **智能穿戴设备**: 对接主流品牌（华为、小米、Apple Watch等）
- **用户档案**: 个人信息管理、健身目标设定

#### 2. 课程管理系统
- **团体课预约**: 
  - 瑜伽区、搏击区、动感单车区分区管理
  - 课程容量控制、排队机制
  - 取消预约规则设定
- **私教课程**:
  - 教练-学员双向评价系统
  - 私教费用结算管理
  - 课程记录和进度跟踪
- **课程提醒**: 提前15分钟微信推送提醒

#### 3. 设备物联网
- **扫码启动**: 跑步机、力量器械等设备二维码启动
- **设备状态监控**: 实时显示设备使用情况
- **淋浴间管理**: 空位查询、使用时长统计

#### 4. 数据可视化
- **训练数据图表**: 折线图展示训练趋势、热力图显示运动强度
- **体测报告**: 自动生成周报、月报、季度报告
- **成果分享**: 训练成果社交分享模板

#### 5. 社区功能
- **健身打卡**: 日常训练打卡、连续打卡奖励
- **饮食分享**: 健康饮食记录、营养分析
- **社区互动**: 点赞、评论、关注机制

#### 6. 运营管理
- **广告位管理**: Banner广告、信息流广告投放
- **会员权益**: 不同等级会员专享权益
- **营销活动**: 优惠券、活动推广

---

## 系统架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────┐
│                    微信小程序前端 (Vue3)                    │
├─────────────────────────────────────────────────────────┤
│                     API网关层                            │
├─────────────────────────────────────────────────────────┤
│  用户服务 │ 课程服务 │ 设备服务 │ 数据服务 │ 社区服务 │ 运营服务 │
├─────────────────────────────────────────────────────────┤
│                   数据访问层 (MyBatis)                    │
├─────────────────────────────────────────────────────────┤
│          MySQL主库    │    MySQL从库    │    Redis缓存    │
└─────────────────────────────────────────────────────────┘
```

### 核心技术组件

#### 前端技术栈
- **框架**: Vue3 + Composition API
- **状态管理**: Pinia
- **UI组件**: Vant Weapp
- **图表库**: ECharts for 微信小程序
- **网络请求**: Axios 适配器

#### 后端技术栈
- **核心框架**: Spring Boot 2.7+
- **安全框架**: Spring Security + JWT
- **数据访问**: MyBatis Plus
- **任务调度**: Spring Task + Quartz
- **消息队列**: RabbitMQ
- **日志监控**: SLF4J + Logback

#### 数据库设计
- **主数据库**: MySQL 8.0 (用户数据、业务数据)
- **缓存数据库**: Redis 6.0 (会话、热点数据)
- **搜索引擎**: Elasticsearch (日志分析、搜索)

---

## 数据库设计

### 核心数据表

#### 用户相关表
```sql
-- 用户基础信息表
CREATE TABLE `user_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `openid` varchar(50) UNIQUE NOT NULL COMMENT '微信openid',
  `nickname` varchar(50) COMMENT '昵称',
  `avatar` varchar(200) COMMENT '头像URL',
  `phone` varchar(20) COMMENT '手机号',
  `gender` tinyint COMMENT '性别 1男 2女',
  `birthday` date COMMENT '生日',
  `member_level` tinyint DEFAULT 1 COMMENT '会员等级 1铜 2银 3VIP',
  `status` tinyint DEFAULT 1 COMMENT '状态 1正常 2禁用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户体征数据表
CREATE TABLE `user_physical_data` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `height` decimal(5,2) COMMENT '身高(cm)',
  `weight` decimal(5,2) COMMENT '体重(kg)',
  `body_fat_rate` decimal(4,2) COMMENT '体脂率(%)',
  `muscle_mass` decimal(5,2) COMMENT '肌肉量(kg)',
  `bmi` decimal(4,2) COMMENT 'BMI指数',
  `measurement_date` date COMMENT '测量日期',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_user_date` (`user_id`, `measurement_date`)
);
```

#### 课程相关表
```sql
-- 课程信息表
CREATE TABLE `course_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `course_name` varchar(100) NOT NULL COMMENT '课程名称',
  `course_type` tinyint NOT NULL COMMENT '课程类型 1瑜伽 2搏击 3动感单车 4私教',
  `instructor_id` bigint COMMENT '教练ID',
  `duration` int COMMENT '课程时长(分钟)',
  `max_capacity` int COMMENT '最大容量',
  `difficulty_level` tinyint COMMENT '难度等级 1初级 2中级 3高级',
  `course_fee` decimal(10,2) COMMENT '课程费用',
  `description` text COMMENT '课程描述',
  `status` tinyint DEFAULT 1 COMMENT '状态 1启用 2禁用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);

-- 课程排期表
CREATE TABLE `course_schedule` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `course_id` bigint NOT NULL,
  `instructor_id` bigint NOT NULL,
  `room_id` bigint NOT NULL COMMENT '教室ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `current_bookings` int DEFAULT 0 COMMENT '当前预约人数',
  `max_capacity` int NOT NULL COMMENT '最大容量',
  `status` tinyint DEFAULT 1 COMMENT '状态 1正常 2取消 3已满',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_time_room` (`start_time`, `room_id`)
);
```

#### 设备相关表
```sql
-- 设备信息表
CREATE TABLE `equipment_info` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `equipment_code` varchar(50) UNIQUE NOT NULL COMMENT '设备编号',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_type` tinyint NOT NULL COMMENT '设备类型 1跑步机 2力量器械 3淋浴间',
  `location` varchar(100) COMMENT '设备位置',
  `qr_code` varchar(200) COMMENT '二维码内容',
  `status` tinyint DEFAULT 1 COMMENT '设备状态 1空闲 2使用中 3维护中 4故障',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);

-- 设备使用记录表
CREATE TABLE `equipment_usage_log` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `equipment_id` bigint NOT NULL,
  `start_time` datetime NOT NULL COMMENT '开始使用时间',
  `end_time` datetime COMMENT '结束使用时间',
  `duration` int COMMENT '使用时长(分钟)',
  `calories_burned` decimal(8,2) COMMENT '消耗卡路里',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_user_time` (`user_id`, `start_time`)
);
```

---

## API接口设计

### 用户相关接口

#### 用户注册/登录
```http
POST /api/user/login
{
  "code": "微信登录code",
  "nickname": "用户昵称",
  "avatar": "头像URL"
}
```

#### 更新体征数据
```http
POST /api/user/physical-data
{
  "height": 170.5,
  "weight": 65.2,
  "bodyFatRate": 15.8,
  "measurementDate": "2024-01-15"
}
```

### 课程相关接口

#### 获取课程列表
```http
GET /api/courses?type=1&date=2024-01-15&page=1&size=10
```

#### 预约课程
```http
POST /api/course/booking
{
  "scheduleId": 123,
  "userId": 456
}
```

### 设备相关接口

#### 扫码启动设备
```http
POST /api/equipment/start
{
  "equipmentCode": "TM001",
  "userId": 123
}
```

#### 查询淋浴间状态
```http
GET /api/equipment/shower-rooms/status
```

---

## 开发计划与里程碑

### 第一阶段：基础框架搭建 (2周)

#### 后端开发
1. **项目初始化** (2天)
   - Spring Boot项目创建
   - 基础依赖配置 (MyBatis, Redis, Security)
   - 开发环境配置

2. **数据库设计与创建** (3天)
   - 设计核心数据表结构
   - 创建数据库脚本
   - 配置数据源和连接池

3. **用户认证系统** (4天)
   - 微信登录集成
   - JWT令牌管理
   - 用户权限控制

4. **基础工具类开发** (3天)
   - 响应结果封装
   - 异常处理机制
   - 日志配置
   - 工具类库

#### 前端开发
1. **小程序项目初始化** (2天)
   - Vue3 + 微信小程序环境搭建
   - UI组件库集成
   - 路由和状态管理配置

2. **基础页面框架** (3天)
   - 登录页面
   - 首页框架
   - 个人中心页面
   - 通用组件开发

### 第二阶段：核心业务功能 (4周)

#### 用户系统完善 (1周)
1. **用户信息管理**
   - 个人资料编辑
   - 体征数据录入
   - 会员等级管理

2. **会员权益系统**
   - 不同等级权益设定
   - 会员升级逻辑

#### 课程管理系统 (2周)
1. **课程基础功能** (1周)
   - 课程信息展示
   - 课程分类筛选
   - 教练信息展示

2. **预约系统** (1周)
   - 课程预约功能
   - 预约冲突检测
   - 取消预约规则
   - 排队机制

#### 私教系统 (1周)
1. **私教课程管理**
   - 私教预约
   - 课程安排
   - 双向评价系统

### 第三阶段：高级功能开发 (3周)

#### 设备物联网 (1.5周)
1. **设备管理**
   - 设备信息维护
   - 二维码生成
   - 设备状态监控

2. **使用记录**
   - 扫码启动逻辑
   - 使用时长统计
   - 数据记录

#### 数据可视化 (1.5周)
1. **图表展示**
   - 训练数据图表
   - ECharts集成
   - 数据统计分析

2. **报告生成**
   - 定期体测报告
   - 数据导出功能

### 第四阶段：社区功能与运营 (2周)

#### 社区功能 (1周)
1. **打卡系统**
   - 日常打卡
   - 打卡奖励机制

2. **社交功能**
   - 动态发布
   - 点赞评论
   - 饮食分享

#### 运营管理 (1周)
1. **广告系统**
   - Banner管理
   - 信息流广告

2. **营销活动**
   - 优惠券系统
   - 活动推广

### 第五阶段：测试优化与上线 (2周)

#### 测试阶段 (1周)
1. **功能测试**
   - 接口测试
   - 前端功能测试
   - 集成测试

2. **性能优化**
   - 数据库优化
   - 缓存策略
   - 前端性能优化

#### 部署上线 (1周)
1. **生产环境部署**
   - 服务器配置
   - 数据库部署
   - 小程序发布

2. **监控告警**
   - 日志监控
   - 性能监控
   - 异常告警

---

## 开发规范

### 代码规范

#### 后端规范
- **包命名**: com.gym.项目名.模块名
- **类命名**: 大驼峰命名法
- **方法命名**: 小驼峰命名法
- **常量命名**: 全大写下划线分隔
- **注释要求**: 类和公共方法必须有完整注释

#### 前端规范
- **组件命名**: 大驼峰命名法
- **文件命名**: 小写短横线分隔
- **方法命名**: 小驼峰命名法
- **CSS类名**: BEM命名规范

### 接口规范
- **统一响应格式**: `{code, message, data}`
- **状态码规范**: 200成功, 4xx客户端错误, 5xx服务器错误
- **接口版本**: URL路径版本控制 `/api/v1/`

### 数据库规范
- **表命名**: 小写下划线分隔
- **字段命名**: 小写下划线分隔
- **主键**: 统一使用`id`
- **时间字段**: 统一使用`created_at`, `updated_at`

---

## 风险评估与应对

### 技术风险
1. **第三方设备集成风险**
   - 风险: 设备API变更、兼容性问题
   - 应对: 建立适配器层，支持多设备厂商

2. **并发访问风险**
   - 风险: 课程预约高并发冲突
   - 应对: 分布式锁、队列机制

3. **数据同步风险**
   - 风险: 智能穿戴设备数据同步延迟
   - 应对: 异步处理、重试机制

### 业务风险
1. **用户隐私风险**
   - 风险: 体征数据泄露
   - 应对: 数据加密、权限控制

2. **运营风险**
   - 风险: 恶意刷单、黄牛预约
   - 应对: 风控系统、身份验证

---

## 后续优化方向

### 功能扩展
1. **AI智能推荐**
   - 基于用户数据的个性化训练计划
   - 智能课程推荐算法

2. **营养管理**
   - 饮食记录与分析
   - 营养师在线咨询

3. **VR/AR体验**
   - 虚拟教练指导
   - AR健身游戏

### 技术优化
1. **微服务架构**
   - 服务拆分
   - 容器化部署

2. **大数据分析**
   - 用户行为分析
   - 运营数据挖掘

---

## 开发团队分工

### 后端团队 (2-3人)
- **架构师/技术负责人**: 系统设计、核心模块开发
- **业务开发工程师**: 业务逻辑实现、接口开发
- **运维工程师**: 部署运维、监控告警

### 前端团队 (2人)
- **前端负责人**: 架构设计、核心功能开发
- **UI开发工程师**: 页面实现、交互优化

### 测试团队 (1人)
- **测试工程师**: 功能测试、性能测试、自动化测试

### 产品设计 (1人)
- **产品经理**: 需求分析、原型设计、项目管理

---

## 总结

这个健身房微信小程序项目涵盖了用户管理、课程预约、设备物联、数据分析等多个复杂业务场景。通过合理的架构设计和分阶段开发计划，可以确保项目的稳定推进和质量保证。

关键成功要素：
1. **扎实的技术架构设计**
2. **详细的开发计划和里程碑控制**  
3. **完善的测试和质量保证体系**
4. **良好的团队协作和代码规范**

预计总开发周期：**13周 (约3个月)**，包含完整的功能开发、测试和上线部署。 