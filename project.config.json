{"appid": "wxfb9e3cf1c9565b78", "compileType": "miniprogram", "miniprogramRoot": "miniprogram/", "libVersion": "3.9.1", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}