<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 训练</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 83px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 10px;
            font-weight: 500;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-icon {
            font-size: 22px;
            margin-bottom: 4px;
        }
        .course-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            overflow: hidden;
        }
        .equipment-card {
            transition: transform 0.2s ease;
        }
        .equipment-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50 h-screen overflow-hidden">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 overflow-y-auto" style="height: calc(100vh - 127px);">
        <!-- Header -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">训练</h1>
                <button class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-search text-gray-600"></i>
                </button>
            </div>
            
            <!-- Category Tabs -->
            <div class="flex space-x-4 mt-4">
                <button class="bg-indigo-600 text-white px-4 py-2 rounded-full text-sm font-medium">全部</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">瑜伽</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">力量</button>
                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">有氧</button>
            </div>
        </div>

        <div class="px-6 pt-6">
            <!-- Featured Course -->
            <div class="course-card p-6 mb-6 text-white relative overflow-hidden">
                <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"></div>
                <div class="relative z-10">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-xl font-bold mb-1">今日推荐</h3>
                            <p class="text-white/80 text-sm">高强度间歇训练</p>
                        </div>
                        <div class="bg-orange-400 text-white px-2 py-1 rounded-full text-xs font-medium">
                            HOT
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-sm">
                        <span><i class="fas fa-clock mr-1"></i>45分钟</span>
                        <span><i class="fas fa-fire mr-1"></i>380卡路里</span>
                        <span><i class="fas fa-user mr-1"></i>15/20人</span>
                    </div>
                    <button class="mt-4 bg-white text-indigo-600 px-6 py-2 rounded-full font-medium text-sm">
                        立即预约
                    </button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <button class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <i class="fas fa-qrcode text-indigo-600 text-2xl mb-2"></i>
                    <div class="text-gray-900 font-medium text-sm">扫码启动</div>
                    <div class="text-gray-500 text-xs mt-1">设备二维码扫描</div>
                </button>
                <button class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <i class="fas fa-calendar-check text-green-600 text-2xl mb-2"></i>
                    <div class="text-gray-900 font-medium text-sm">我的预约</div>
                    <div class="text-gray-500 text-xs mt-1">查看已预约课程</div>
                </button>
            </div>

            <!-- Today's Classes -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">今日课程</h3>
                    <button class="text-indigo-600 text-sm font-medium">查看全部</button>
                </div>
                
                <div class="space-y-3">
                    <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=60&h=60&fit=crop" 
                                 class="w-15 h-15 rounded-lg object-cover mr-4">
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-semibold text-gray-900 mb-1">瑜伽基础班</h4>
                                        <p class="text-gray-600 text-sm mb-2">张教练 · 瑜伽室A</p>
                                        <div class="flex items-center text-xs text-gray-500">
                                            <i class="fas fa-clock mr-1"></i>
                                            <span>09:00-10:00</span>
                                            <span class="mx-2">•</span>
                                            <span>12/20人</span>
                                        </div>
                                    </div>
                                    <button class="bg-indigo-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                                        预约
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex">
                            <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=60&h=60&fit=crop" 
                                 class="w-15 h-15 rounded-lg object-cover mr-4">
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-semibold text-gray-900 mb-1">搏击训练</h4>
                                        <p class="text-gray-600 text-sm mb-2">李教练 · 搏击训练室</p>
                                        <div class="flex items-center text-xs text-gray-500">
                                            <i class="fas fa-clock mr-1"></i>
                                            <span>14:00-15:00</span>
                                            <span class="mx-2">•</span>
                                            <span>8/15人</span>
                                        </div>
                                    </div>
                                    <button class="bg-indigo-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                                        预约
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Equipment Status -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">设备状态</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="equipment-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-running text-blue-600 text-xl"></i>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">可用</span>
                        </div>
                        <div class="text-gray-900 font-medium text-sm mb-1">跑步机</div>
                        <div class="text-gray-500 text-xs">5/8 台空闲</div>
                    </div>
                    
                    <div class="equipment-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-dumbbell text-purple-600 text-xl"></i>
                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">繁忙</span>
                        </div>
                        <div class="text-gray-900 font-medium text-sm mb-1">力量区</div>
                        <div class="text-gray-500 text-xs">2/6 台空闲</div>
                    </div>
                    
                    <div class="equipment-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-shower text-cyan-600 text-xl"></i>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">可用</span>
                        </div>
                        <div class="text-gray-900 font-medium text-sm mb-1">淋浴间</div>
                        <div class="text-gray-500 text-xs">8/10 间空闲</div>
                    </div>
                    
                    <div class="equipment-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-bicycle text-green-600 text-xl"></i>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">可用</span>
                        </div>
                        <div class="text-gray-900 font-medium text-sm mb-1">动感单车</div>
                        <div class="text-gray-500 text-xs">12/15 台空闲</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span>主页</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-dumbbell tab-icon"></i>
            <span>训练</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span>数据</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-users tab-icon"></i>
            <span>社区</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 