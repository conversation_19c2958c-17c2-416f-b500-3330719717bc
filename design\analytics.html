<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitFocus - 数据分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 83px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 10px;
            font-weight: 500;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-icon {
            font-size: 22px;
            margin-bottom: 4px;
        }
        .chart-container {
            height: 200px;
            width: 100%;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-ring {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(#667eea 0deg 270deg, #e5e7eb 270deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .stat-ring-inner {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50 h-screen overflow-hidden">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <i class="fas fa-battery-three-quarters text-xs"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 overflow-y-auto" style="height: calc(100vh - 127px);">
        <!-- Header -->
        <div class="gradient-bg px-6 py-6 text-white">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold">健身数据</h1>
                    <p class="text-white/80 text-sm mt-1">本周已训练 4 天</p>
                </div>
                <button class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <i class="fas fa-calendar text-white"></i>
                </button>
            </div>

            <!-- Weekly Stats -->
            <div class="grid grid-cols-3 gap-4">
                <div class="bg-white/20 backdrop-blur-lg rounded-xl p-4 text-center">
                    <div class="text-2xl font-bold mb-1">12</div>
                    <div class="text-white/80 text-xs">番茄钟</div>
                </div>
                <div class="bg-white/20 backdrop-blur-lg rounded-xl p-4 text-center">
                    <div class="text-2xl font-bold mb-1">240</div>
                    <div class="text-white/80 text-xs">分钟</div>
                </div>
                <div class="bg-white/20 backdrop-blur-lg rounded-xl p-4 text-center">
                    <div class="text-2xl font-bold mb-1">1,280</div>
                    <div class="text-white/80 text-xs">卡路里</div>
                </div>
            </div>
        </div>

        <div class="px-6 pt-6">
            <!-- Progress Overview -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">今日进度</h3>
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="stat-ring mx-auto mb-2">
                            <div class="stat-ring-inner">
                                <div class="text-sm font-bold text-gray-900">75%</div>
                                <div class="text-xs text-gray-500">目标</div>
                            </div>
                        </div>
                        <div class="text-xs text-gray-600">训练目标</div>
                    </div>
                    <div class="text-center">
                        <div class="stat-ring mx-auto mb-2" style="background: conic-gradient(#10b981 0deg 288deg, #e5e7eb 288deg 360deg);">
                            <div class="stat-ring-inner">
                                <div class="text-sm font-bold text-gray-900">80%</div>
                                <div class="text-xs text-gray-500">卡路里</div>
                            </div>
                        </div>
                        <div class="text-xs text-gray-600">消耗卡路里</div>
                    </div>
                    <div class="text-center">
                        <div class="stat-ring mx-auto mb-2" style="background: conic-gradient(#f59e0b 0deg 216deg, #e5e7eb 216deg 360deg);">
                            <div class="stat-ring-inner">
                                <div class="text-sm font-bold text-gray-900">60%</div>
                                <div class="text-xs text-gray-500">活动</div>
                            </div>
                        </div>
                        <div class="text-xs text-gray-600">活动时间</div>
                    </div>
                </div>
            </div>

            <!-- Workout Chart -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">训练趋势</h3>
                    <div class="flex space-x-2">
                        <button class="bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-xs font-medium">周</button>
                        <button class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs font-medium">月</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="workoutChart"></canvas>
                </div>
            </div>

            <!-- Body Metrics -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">体测数据</h3>
                    <button class="text-indigo-600 text-sm font-medium">查看详情</button>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-blue-50 rounded-xl">
                        <div class="text-2xl font-bold text-blue-600 mb-1">70.2</div>
                        <div class="text-gray-600 text-sm mb-1">体重 (kg)</div>
                        <div class="text-green-600 text-xs flex items-center justify-center">
                            <i class="fas fa-arrow-down mr-1"></i>-0.5kg
                        </div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-xl">
                        <div class="text-2xl font-bold text-green-600 mb-1">15.8%</div>
                        <div class="text-gray-600 text-sm mb-1">体脂率</div>
                        <div class="text-green-600 text-xs flex items-center justify-center">
                            <i class="fas fa-arrow-down mr-1"></i>-1.2%
                        </div>
                    </div>
                    <div class="text-center p-4 bg-purple-50 rounded-xl">
                        <div class="text-2xl font-bold text-purple-600 mb-1">55.4</div>
                        <div class="text-gray-600 text-sm mb-1">肌肉量 (kg)</div>
                        <div class="text-green-600 text-xs flex items-center justify-center">
                            <i class="fas fa-arrow-up mr-1"></i>+0.3kg
                        </div>
                    </div>
                    <div class="text-center p-4 bg-orange-50 rounded-xl">
                        <div class="text-2xl font-bold text-orange-600 mb-1">22.1</div>
                        <div class="text-gray-600 text-sm mb-1">BMI</div>
                        <div class="text-gray-500 text-xs">正常范围</div>
                    </div>
                </div>
            </div>

            <!-- Recent Workouts -->
            <div class="bg-white rounded-xl p-6 mb-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">最近训练</h3>
                    <button class="text-indigo-600 text-sm font-medium">查看全部</button>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-fire text-red-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900 text-sm">高强度训练</div>
                                <div class="text-gray-500 text-xs">今天 14:30</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">45分钟</div>
                            <div class="text-xs text-gray-500">380卡路里</div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-running text-blue-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900 text-sm">有氧运动</div>
                                <div class="text-gray-500 text-xs">昨天 09:15</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">30分钟</div>
                            <div class="text-xs text-gray-500">220卡路里</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span>主页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-dumbbell tab-icon"></i>
            <span>训练</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-chart-line tab-icon"></i>
            <span>数据</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-users tab-icon"></i>
            <span>社区</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span>我的</span>
        </div>
    </div>

    <script>
        // Workout Chart
        const ctx = document.getElementById('workoutChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '训练时长(分钟)',
                    data: [45, 60, 0, 90, 75, 30, 0],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    </script>
</body>
</html> 