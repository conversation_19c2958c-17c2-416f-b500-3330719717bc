<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gym.mapper.UserMapper">

    <!-- 根据openid查询用户 -->
    <select id="selectByOpenid" resultType="com.gym.entity.UserInfo">
        SELECT * FROM user_info 
        WHERE openid = #{openid} AND deleted = 0
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultType="com.gym.entity.UserInfo">
        SELECT * FROM user_info 
        WHERE phone = #{phone} AND deleted = 0
    </select>

    <!-- 更新用户最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE user_info 
        SET last_login_time = NOW(), updated_at = NOW()
        WHERE id = #{userId} AND deleted = 0
    </update>

    <!-- 更新用户积分 -->
    <update id="updateUserPoints">
        UPDATE user_info 
        SET total_points = #{totalPoints}, 
            available_points = #{availablePoints},
            updated_at = NOW()
        WHERE id = #{userId} AND deleted = 0
    </update>

</mapper> 