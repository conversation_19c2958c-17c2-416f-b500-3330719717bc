package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户基础信息实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("user_info")
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("openid")
    private String openid;

    @TableField("union_id")
    private String unionId;

    @TableField("nickname")
    private String nickname;

    @TableField("avatar")
    private String avatar;

    @TableField("phone")
    private String phone;

    @TableField("gender")
    private Integer gender;

    @TableField("birthday")
    private LocalDate birthday;

    @TableField("member_level")
    private Integer memberLevel;

    @TableField("member_expire_date")
    private LocalDate memberExpireDate;

    @TableField("total_points")
    private Integer totalPoints;

    @TableField("available_points")
    private Integer availablePoints;

    @TableField("status")
    private Integer status;

    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 判断是否为有效会员
     */
    public boolean isValidMember() {
        if (memberExpireDate == null) {
            return false;
        }
        return memberExpireDate.isAfter(LocalDate.now());
    }

    /**
     * 判断是否为VIP会员
     */
    public boolean isVipMember() {
        return memberLevel != null && memberLevel == 3 && isValidMember();
    }

    /**
     * 获取会员等级名称
     */
    public String getMemberLevelName() {
        if (memberLevel == null) {
            return "普通用户";
        }
        switch (memberLevel) {
            case 1: return "铜卡会员";
            case 2: return "银卡会员";
            case 3: return "VIP会员";
            default: return "普通用户";
        }
    }
} 