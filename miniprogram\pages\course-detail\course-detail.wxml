<!-- 课程详情页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="back-btn" bindtap="onBack">
      <text class="back-icon">〈</text>
    </view>
    <text class="page-title">课程详情</text>
    <view class="action-btns">
      <view class="action-btn" bindtap="onShareCourse">
        <text class="action-icon">📤</text>
      </view>
      <view class="action-btn {{course.isFavorite ? 'favorited' : ''}}" bindtap="onToggleFavorite">
        <text class="action-icon">{{course.isFavorite ? '❤️' : '🤍'}}</text>
      </view>
    </view>
  </view>

  <!-- 课程封面 -->
  <view class="course-hero">
    <image class="hero-image" src="{{course.coverImage}}" mode="aspectFill"></image>
    <view class="hero-overlay">
      <view class="course-badge">
        <text class="badge-text">{{course.category}}</text>
      </view>
      <view class="course-level {{course.level}}">
        <text class="level-text">{{course.levelText}}</text>
      </view>
    </view>
    <view class="hero-content">
      <text class="course-title">{{course.name}}</text>
      <text class="course-subtitle">{{course.subtitle}}</text>
      <view class="course-stats">
        <view class="stat-item">
          <text class="stat-icon">⏱️</text>
          <text class="stat-text">{{course.duration}}分钟</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">👥</text>
          <text class="stat-text">{{course.currentParticipants}}/{{course.maxParticipants}}人</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">⭐</text>
          <text class="stat-text">{{course.rating}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 课程内容 -->
  <view class="course-content">
    <!-- 教练信息 -->
    <view class="instructor-section">
      <view class="section-header">
        <text class="section-title">授课教练</text>
      </view>
      <view class="instructor-card" bindtap="onViewInstructor">
        <image class="instructor-avatar" src="{{course.instructor.avatar}}" mode="aspectFill"></image>
        <view class="instructor-info">
          <text class="instructor-name">{{course.instructor.name}}</text>
          <text class="instructor-title">{{course.instructor.title}}</text>
          <view class="instructor-stats">
            <view class="instructor-stat">
              <text class="stat-label">教学经验</text>
              <text class="stat-value">{{course.instructor.experience}}年</text>
            </view>
            <view class="instructor-stat">
              <text class="stat-label">学员评分</text>
              <text class="stat-value">{{course.instructor.rating}}</text>
            </view>
          </view>
        </view>
        <view class="instructor-arrow">
          <text class="arrow-icon">〉</text>
        </view>
      </view>
    </view>

    <!-- 课程描述 -->
    <view class="description-section">
      <view class="section-header">
        <text class="section-title">课程介绍</text>
      </view>
      <view class="description-content">
        <text class="description-text">{{course.description}}</text>
        <view class="description-highlights">
          <view class="highlight-item" wx:for="{{course.highlights}}" wx:key="*this">
            <text class="highlight-icon">✓</text>
            <text class="highlight-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 课程安排 -->
    <view class="schedule-section">
      <view class="section-header">
        <text class="section-title">课程安排</text>
        <view class="schedule-filter">
          <view class="filter-btn {{scheduleFilter === 'week' ? 'active' : ''}}" bindtap="onFilterChange" data-filter="week">
            <text class="filter-text">本周</text>
          </view>
          <view class="filter-btn {{scheduleFilter === 'month' ? 'active' : ''}}" bindtap="onFilterChange" data-filter="month">
            <text class="filter-text">本月</text>
          </view>
        </view>
      </view>
      
      <view class="schedule-list">
        <view 
          class="schedule-item {{item.isBooked ? 'booked' : ''}} {{item.isFull ? 'full' : ''}} {{item.isPast ? 'past' : ''}}"
          wx:for="{{filteredSchedule}}"
          wx:key="id"
          data-schedule="{{item}}"
          bindtap="onScheduleItemTap"
        >
          <view class="schedule-date">
            <text class="date-weekday">{{item.weekday}}</text>
            <text class="date-number">{{item.date}}</text>
            <text class="date-month">{{item.month}}</text>
          </view>
          <view class="schedule-info">
            <view class="schedule-time">
              <text class="time-start">{{item.startTime}}</text>
              <text class="time-separator">-</text>
              <text class="time-end">{{item.endTime}}</text>
            </view>
            <text class="schedule-location">{{item.location}}</text>
            <view class="schedule-participants">
              <text class="participants-count">{{item.participants}}/{{item.maxParticipants}}人</text>
              <view class="participants-avatars">
                <image 
                  class="participant-avatar"
                  wx:for="{{item.participantAvatars}}"
                  wx:for-item="avatar"
                  wx:key="*this"
                  src="{{avatar}}"
                  mode="aspectFill"
                ></image>
              </view>
            </view>
          </view>
          <view class="schedule-status">
            <view class="status-badge {{item.status}}" wx:if="{{item.isBooked}}">
              <text class="status-text">已预约</text>
            </view>
            <view class="status-badge full" wx:elif="{{item.isFull}}">
              <text class="status-text">已满</text>
            </view>
            <view class="status-badge past" wx:elif="{{item.isPast}}">
              <text class="status-text">已结束</text>
            </view>
            <view class="book-btn" wx:else bindtap="onBookSchedule" data-schedule="{{item}}" catchtap="">
              <text class="book-text">预约</text>
            </view>
          </view>
        </view>
        
        <view class="empty-schedule" wx:if="{{filteredSchedule.length === 0}}">
          <text class="empty-icon">📅</text>
          <text class="empty-text">暂无课程安排</text>
        </view>
      </view>
    </view>

    <!-- 学员评价 -->
    <view class="reviews-section">
      <view class="section-header">
        <text class="section-title">学员评价</text>
        <view class="reviews-summary">
          <text class="rating-score">{{course.rating}}</text>
          <view class="rating-stars">
            <text class="star {{index < course.ratingStars ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this">⭐</text>
          </view>
          <text class="rating-count">({{course.reviewCount}}条)</text>
        </view>
      </view>
      
      <view class="reviews-list">
        <view class="review-item" wx:for="{{course.reviews}}" wx:key="id">
          <view class="review-header">
            <image class="reviewer-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
            <view class="reviewer-info">
              <text class="reviewer-name">{{item.userName}}</text>
              <view class="review-rating">
                <text class="star {{starIndex < item.rating ? 'filled' : ''}}" wx:for="{{5}}" wx:for-item="star" wx:for-index="starIndex" wx:key="*this">⭐</text>
              </view>
            </view>
            <text class="review-date">{{item.date}}</text>
          </view>
          <text class="review-content">{{item.content}}</text>
          <view class="review-images" wx:if="{{item.images && item.images.length > 0}}">
            <image 
              class="review-image"
              wx:for="{{item.images}}"
              wx:for-item="image"
              wx:key="*this"
              src="{{image}}"
              mode="aspectFill"
              bindtap="onPreviewReviewImage"
              data-images="{{item.images}}"
              data-current="{{image}}"
            ></image>
          </view>
        </view>
        
        <view class="view-all-reviews" bindtap="onViewAllReviews">
          <text class="view-all-text">查看全部评价</text>
          <text class="view-all-arrow">〉</text>
        </view>
      </view>
    </view>

    <!-- 相关推荐 -->
    <view class="related-section">
      <view class="section-header">
        <text class="section-title">相关推荐</text>
      </view>
      <scroll-view class="related-courses" scroll-x="true" show-scrollbar="false">
        <view 
          class="related-course"
          wx:for="{{relatedCourses}}"
          wx:key="id"
          data-course="{{item}}"
          bindtap="onRelatedCourseTap"
        >
          <image class="related-image" src="{{item.coverImage}}" mode="aspectFill"></image>
          <view class="related-info">
            <text class="related-name">{{item.name}}</text>
            <text class="related-instructor">{{item.instructor}}</text>
            <view class="related-meta">
              <text class="related-duration">{{item.duration}}min</text>
              <text class="related-rating">⭐{{item.rating}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="price-info">
      <text class="price-label">课程费用</text>
      <view class="price-value">
        <text class="price-currency">¥</text>
        <text class="price-amount">{{course.price}}</text>
        <text class="price-unit">/节</text>
      </view>
    </view>
    <button 
      class="book-course-btn {{course.canBook ? '' : 'disabled'}}"
      bindtap="onBookCourse"
      disabled="{{!course.canBook}}"
    >
      {{course.canBook ? '立即预约' : course.disabledReason}}
    </button>
  </view>

  <!-- 预约确认弹窗 -->
  <view class="booking-modal {{showBookingModal ? 'show' : ''}}" catchtap="onCloseBookingModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">确认预约</text>
        <view class="close-btn" bindtap="onCloseBookingModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="booking-details" wx:if="{{selectedSchedule}}">
        <view class="course-summary">
          <image class="summary-image" src="{{course.coverImage}}" mode="aspectFill"></image>
          <view class="summary-info">
            <text class="summary-title">{{course.name}}</text>
            <text class="summary-instructor">{{course.instructor.name}}</text>
            <text class="summary-location">{{selectedSchedule.location}}</text>
          </view>
        </view>
        
        <view class="booking-time">
          <view class="time-item">
            <text class="time-label">上课时间</text>
            <text class="time-value">{{selectedSchedule.fullDate}} {{selectedSchedule.startTime}}-{{selectedSchedule.endTime}}</text>
          </view>
          <view class="time-item">
            <text class="time-label">课程时长</text>
            <text class="time-value">{{course.duration}}分钟</text>
          </view>
        </view>
        
        <view class="booking-notes">
          <text class="notes-title">预约须知</text>
          <text class="notes-item">• 请提前10分钟到达教室</text>
          <text class="notes-item">• 取消预约需提前2小时</text>
          <text class="notes-item">• 迟到超过15分钟视为自动取消</text>
          <text class="notes-item">• 首次体验用户享受8折优惠</text>
        </view>
        
        <view class="payment-section">
          <view class="payment-method">
            <text class="payment-label">支付方式</text>
            <view class="payment-options">
              <view class="payment-option {{paymentMethod === 'wechat' ? 'selected' : ''}}" bindtap="onSelectPayment" data-method="wechat">
                <text class="payment-icon">💳</text>
                <text class="payment-text">微信支付</text>
                <text class="payment-check">{{paymentMethod === 'wechat' ? '✓' : ''}}</text>
              </view>
              <view class="payment-option {{paymentMethod === 'points' ? 'selected' : ''}}" bindtap="onSelectPayment" data-method="points">
                <text class="payment-icon">💰</text>
                <text class="payment-text">积分支付 ({{userPoints}}分可用)</text>
                <text class="payment-check">{{paymentMethod === 'points' ? '✓' : ''}}</text>
              </view>
            </view>
          </view>
          
          <view class="price-breakdown">
            <view class="price-item">
              <text class="price-desc">课程费用</text>
              <text class="price-value">¥{{course.price}}</text>
            </view>
            <view class="price-item" wx:if="{{hasDiscount}}">
              <text class="price-desc">体验优惠</text>
              <text class="price-value discount">-¥{{discountAmount}}</text>
            </view>
            <view class="price-total">
              <text class="total-desc">实付金额</text>
              <text class="total-value">¥{{finalPrice}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onCloseBookingModal">取消</button>
        <button class="modal-btn primary" bindtap="onConfirmBooking">确认预约</button>
      </view>
    </view>
  </view>
</view> 