package com.gym.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gym.entity.CourseInfo;
import com.gym.entity.CourseSchedule;
import com.gym.entity.CourseBooking;
import com.gym.entity.InstructorInfo;

import java.time.LocalDate;
import java.util.List;

/**
 * 课程服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface CourseService extends IService<CourseInfo> {

    /**
     * 分页查询课程列表
     */
    IPage<CourseInfo> getCourseList(Page<CourseInfo> page, Integer courseType, 
                                   Integer difficultyLevel, String keyword);

    /**
     * 根据类型获取课程列表
     */
    List<CourseInfo> getCoursesByType(Integer courseType);

    /**
     * 获取热门课程
     */
    List<CourseInfo> getHotCourses(Integer limit);

    /**
     * 获取课程详情
     */
    CourseInfo getCourseDetail(Long courseId);

    /**
     * 获取课程排期列表
     */
    List<CourseSchedule> getCourseSchedules(Long courseId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取可预约的课程排期
     */
    List<CourseSchedule> getAvailableSchedules(LocalDate startDate, LocalDate endDate);

    /**
     * 预约课程
     */
    boolean bookCourse(Long userId, Long scheduleId);

    /**
     * 取消预约
     */
    boolean cancelBooking(Long userId, Long bookingId);

    /**
     * 签到
     */
    boolean checkIn(Long userId, Long bookingId);

    /**
     * 获取用户预约记录
     */
    List<CourseBooking> getUserBookings(Long userId);

    /**
     * 获取用户有效预约
     */
    List<CourseBooking> getUserValidBookings(Long userId);

    /**
     * 获取教练列表
     */
    List<InstructorInfo> getInstructors();

    /**
     * 获取教练详情
     */
    InstructorInfo getInstructorDetail(Long instructorId);

    /**
     * 根据专业特长获取教练
     */
    List<InstructorInfo> getInstructorsBySpecialty(String specialty);

    /**
     * 获取排行榜教练
     */
    List<InstructorInfo> getTopRatedInstructors(Integer limit);
} 