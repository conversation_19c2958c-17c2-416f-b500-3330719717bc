package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 设备信息实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("equipment_info")
public class EquipmentInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("equipment_code")
    private String equipmentCode;

    @TableField("equipment_name")
    private String equipmentName;

    @TableField("equipment_type")
    private Integer equipmentType;

    @TableField("brand")
    private String brand;

    @TableField("model")
    private String model;

    @TableField("location")
    private String location;

    @TableField("room_id")
    private Long roomId;

    @TableField("qr_code")
    private String qrCode;

    @TableField("maintenance_cycle")
    private Integer maintenanceCycle;

    @TableField("last_maintenance")
    private LocalDate lastMaintenance;

    @TableField("next_maintenance")
    private LocalDate nextMaintenance;

    @TableField("purchase_date")
    private LocalDate purchaseDate;

    @TableField("warranty_expire")
    private LocalDate warrantyExpire;

    @TableField("status")
    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取设备类型名称
     */
    public String getEquipmentTypeName() {
        if (equipmentType == null) {
            return "其他";
        }
        switch (equipmentType) {
            case 1: return "跑步机";
            case 2: return "椭圆机";
            case 3: return "动感单车";
            case 4: return "力量器械";
            case 5: return "淋浴间";
            case 6: return "其他";
            default: return "其他";
        }
    }

    /**
     * 获取设备状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "空闲";
        }
        switch (status) {
            case 1: return "空闲";
            case 2: return "使用中";
            case 3: return "维护中";
            case 4: return "故障";
            case 5: return "停用";
            default: return "空闲";
        }
    }

    /**
     * 判断设备是否可用
     */
    public boolean isAvailable() {
        return status != null && status == 1;
    }

    /**
     * 判断设备是否使用中
     */
    public boolean isInUse() {
        return status != null && status == 2;
    }

    /**
     * 判断是否需要维护
     */
    public boolean needsMaintenance() {
        if (nextMaintenance == null) {
            return false;
        }
        return LocalDate.now().isAfter(nextMaintenance) || LocalDate.now().isEqual(nextMaintenance);
    }

    /**
     * 判断保修是否过期
     */
    public boolean isWarrantyExpired() {
        if (warrantyExpire == null) {
            return true;
        }
        return LocalDate.now().isAfter(warrantyExpire);
    }
} 