package com.gym.service;

import com.gym.entity.UserPhysicalData;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 数据分析服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AnalyticsService {

    /**
     * 获取用户运动数据统计
     */
    Map<String, Object> getUserWorkoutStats(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取用户体征数据趋势
     */
    Map<String, Object> getUserPhysicalTrends(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取用户课程参与统计
     */
    Map<String, Object> getUserCourseStats(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取用户设备使用统计
     */
    Map<String, Object> getUserEquipmentStats(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取用户体征数据历史
     */
    List<UserPhysicalData> getUserPhysicalHistory(Long userId);

    /**
     * 添加用户体征数据
     */
    boolean addUserPhysicalData(UserPhysicalData physicalData);

    /**
     * 获取用户最新体征数据
     */
    UserPhysicalData getLatestPhysicalData(Long userId);

    /**
     * 获取用户健身目标达成情况
     */
    Map<String, Object> getFitnessGoalProgress(Long userId);

    /**
     * 获取用户运动强度分析
     */
    Map<String, Object> getWorkoutIntensityAnalysis(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取用户健身报告
     */
    Map<String, Object> getFitnessReport(Long userId, String reportType);
} 