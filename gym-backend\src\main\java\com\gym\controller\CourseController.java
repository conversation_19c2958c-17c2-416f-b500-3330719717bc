package com.gym.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gym.common.result.Result;
import com.gym.entity.CourseInfo;
import com.gym.entity.CourseSchedule;
import com.gym.entity.CourseBooking;
import com.gym.entity.InstructorInfo;
import com.gym.service.CourseService;
import com.gym.util.JwtUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 课程控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/course")
public class CourseController {

    @Autowired
    private CourseService courseService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 分页查询课程列表
     */
    @GetMapping("/list")
    public Result<IPage<CourseInfo>> getCourseList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer courseType,
            @RequestParam(required = false) Integer difficultyLevel,
            @RequestParam(required = false) String keyword) {
        try {
            Page<CourseInfo> pageParam = new Page<>(page, size);
            IPage<CourseInfo> result = courseService.getCourseList(pageParam, courseType, difficultyLevel, keyword);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询课程列表失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 根据类型获取课程
     */
    @GetMapping("/type/{courseType}")
    public Result<List<CourseInfo>> getCoursesByType(@PathVariable Integer courseType) {
        try {
            List<CourseInfo> courses = courseService.getCoursesByType(courseType);
            return Result.success("查询成功", courses);
        } catch (Exception e) {
            log.error("根据类型查询课程失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取热门课程
     */
    @GetMapping("/hot")
    public Result<List<CourseInfo>> getHotCourses(@RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<CourseInfo> courses = courseService.getHotCourses(limit);
            return Result.success("查询成功", courses);
        } catch (Exception e) {
            log.error("查询热门课程失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取课程详情
     */
    @GetMapping("/{courseId}")
    public Result<CourseInfo> getCourseDetail(@PathVariable Long courseId) {
        try {
            CourseInfo course = courseService.getCourseDetail(courseId);
            if (course == null) {
                return Result.error("课程不存在");
            }
            return Result.success("查询成功", course);
        } catch (Exception e) {
            log.error("查询课程详情失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取课程排期
     */
    @GetMapping("/{courseId}/schedules")
    public Result<List<CourseSchedule>> getCourseSchedules(
            @PathVariable Long courseId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            List<CourseSchedule> schedules = courseService.getCourseSchedules(courseId, startDate, endDate);
            return Result.success("查询成功", schedules);
        } catch (Exception e) {
            log.error("查询课程排期失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取可预约的课程排期
     */
    @GetMapping("/available-schedules")
    public Result<List<CourseSchedule>> getAvailableSchedules(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            List<CourseSchedule> schedules = courseService.getAvailableSchedules(startDate, endDate);
            return Result.success("查询成功", schedules);
        } catch (Exception e) {
            log.error("查询可预约排期失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 预约课程
     */
    @PostMapping("/book")
    public Result<Void> bookCourse(@RequestHeader("Authorization") String authHeader,
                                  @Valid @RequestBody BookCourseRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = courseService.bookCourse(userId, request.getScheduleId());
            if (success) {
                return Result.success("预约成功");
            } else {
                return Result.error("预约失败");
            }
        } catch (Exception e) {
            log.error("预约课程失败: {}", e.getMessage(), e);
            return Result.error("预约失败: " + e.getMessage());
        }
    }

    /**
     * 取消预约
     */
    @PostMapping("/cancel")
    public Result<Void> cancelBooking(@RequestHeader("Authorization") String authHeader,
                                     @Valid @RequestBody CancelBookingRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = courseService.cancelBooking(userId, request.getBookingId());
            if (success) {
                return Result.success("取消成功");
            } else {
                return Result.error("取消失败");
            }
        } catch (Exception e) {
            log.error("取消预约失败: {}", e.getMessage(), e);
            return Result.error("取消失败: " + e.getMessage());
        }
    }

    /**
     * 签到
     */
    @PostMapping("/checkin")
    public Result<Void> checkIn(@RequestHeader("Authorization") String authHeader,
                               @Valid @RequestBody CheckInRequest request) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            boolean success = courseService.checkIn(userId, request.getBookingId());
            if (success) {
                return Result.success("签到成功");
            } else {
                return Result.error("签到失败");
            }
        } catch (Exception e) {
            log.error("课程签到失败: {}", e.getMessage(), e);
            return Result.error("签到失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户预约记录
     */
    @GetMapping("/my-bookings")
    public Result<List<CourseBooking>> getMyBookings(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            List<CourseBooking> bookings = courseService.getUserBookings(userId);
            return Result.success("查询成功", bookings);
        } catch (Exception e) {
            log.error("查询用户预约记录失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取用户有效预约
     */
    @GetMapping("/my-valid-bookings")
    public Result<List<CourseBooking>> getMyValidBookings(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            List<CourseBooking> bookings = courseService.getUserValidBookings(userId);
            return Result.success("查询成功", bookings);
        } catch (Exception e) {
            log.error("查询用户有效预约失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取教练列表
     */
    @GetMapping("/instructors")
    public Result<List<InstructorInfo>> getInstructors() {
        try {
            List<InstructorInfo> instructors = courseService.getInstructors();
            return Result.success("查询成功", instructors);
        } catch (Exception e) {
            log.error("查询教练列表失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取教练详情
     */
    @GetMapping("/instructor/{instructorId}")
    public Result<InstructorInfo> getInstructorDetail(@PathVariable Long instructorId) {
        try {
            InstructorInfo instructor = courseService.getInstructorDetail(instructorId);
            if (instructor == null) {
                return Result.error("教练不存在");
            }
            return Result.success("查询成功", instructor);
        } catch (Exception e) {
            log.error("查询教练详情失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 根据专业特长获取教练
     */
    @GetMapping("/instructors/specialty/{specialty}")
    public Result<List<InstructorInfo>> getInstructorsBySpecialty(@PathVariable String specialty) {
        try {
            List<InstructorInfo> instructors = courseService.getInstructorsBySpecialty(specialty);
            return Result.success("查询成功", instructors);
        } catch (Exception e) {
            log.error("根据专业查询教练失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 获取排行榜教练
     */
    @GetMapping("/instructors/top-rated")
    public Result<List<InstructorInfo>> getTopRatedInstructors(@RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<InstructorInfo> instructors = courseService.getTopRatedInstructors(limit);
            return Result.success("查询成功", instructors);
        } catch (Exception e) {
            log.error("查询排行榜教练失败: {}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    /**
     * 从令牌中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        String token = jwtUtil.extractTokenFromHeader(authHeader);
        if (token == null || !jwtUtil.isTokenValid(token)) {
            throw new RuntimeException("令牌无效");
        }
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 预约课程请求对象
     */
    @Data
    public static class BookCourseRequest {
        @NotNull(message = "排期ID不能为空")
        private Long scheduleId;
    }

    /**
     * 取消预约请求对象
     */
    @Data
    public static class CancelBookingRequest {
        @NotNull(message = "预约ID不能为空")
        private Long bookingId;
    }

    /**
     * 签到请求对象
     */
    @Data
    public static class CheckInRequest {
        @NotNull(message = "预约ID不能为空")
        private Long bookingId;
    }
} 