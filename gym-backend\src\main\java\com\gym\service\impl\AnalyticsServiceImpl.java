package com.gym.service.impl;

import com.gym.entity.UserPhysicalData;
import com.gym.mapper.*;
import com.gym.service.AnalyticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据分析服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class AnalyticsServiceImpl implements AnalyticsService {

    @Autowired
    private UserPhysicalDataMapper userPhysicalDataMapper;

    @Autowired
    private EquipmentUsageLogMapper equipmentUsageLogMapper;

    @Autowired
    private CourseBookingMapper courseBookingMapper;

    @Override
    public Map<String, Object> getUserWorkoutStats(Long userId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> stats = new HashMap<>();
        
        // 查询设备使用记录
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        Integer totalDuration = equipmentUsageLogMapper.sumDurationByUserId(userId, startTime, endTime);
        Integer totalSessions = equipmentUsageLogMapper.countUsageByEquipmentId(userId, startTime, endTime);
        
        // 查询课程预约记录
        Integer courseBookings = courseBookingMapper.countBookingsByUserId(userId, startTime, endTime);
        
        stats.put("totalWorkoutDuration", totalDuration != null ? totalDuration : 0);
        stats.put("totalWorkoutSessions", totalSessions != null ? totalSessions : 0);
        stats.put("totalCourseBookings", courseBookings != null ? courseBookings : 0);
        stats.put("averageSessionDuration", totalSessions != null && totalSessions > 0 ? totalDuration / totalSessions : 0);
        stats.put("period", startDate + " 至 " + endDate);
        
        return stats;
    }

    @Override
    public Map<String, Object> getUserPhysicalTrends(Long userId, LocalDate startDate, LocalDate endDate) {
        List<UserPhysicalData> physicalData = userPhysicalDataMapper.selectByDateRange(userId, startDate, endDate);
        
        Map<String, Object> trends = new HashMap<>();
        
        if (physicalData.isEmpty()) {
            trends.put("hasData", false);
            trends.put("message", "暂无体征数据");
            return trends;
        }
        
        // 按日期排序
        physicalData.sort(Comparator.comparing(UserPhysicalData::getMeasurementDate));
        
        // 构建趋势数据
        List<Map<String, Object>> weightTrend = new ArrayList<>();
        List<Map<String, Object>> bmiTrend = new ArrayList<>();
        List<Map<String, Object>> bodyFatTrend = new ArrayList<>();
        
        for (UserPhysicalData data : physicalData) {
            String dateStr = data.getMeasurementDate().toString();
            
            if (data.getWeight() != null) {
                Map<String, Object> weightPoint = new HashMap<>();
                weightPoint.put("date", dateStr);
                weightPoint.put("value", data.getWeight());
                weightTrend.add(weightPoint);
            }
            
            if (data.getBmi() != null) {
                Map<String, Object> bmiPoint = new HashMap<>();
                bmiPoint.put("date", dateStr);
                bmiPoint.put("value", data.getBmi());
                bmiTrend.add(bmiPoint);
            }
            
            if (data.getBodyFatRate() != null) {
                Map<String, Object> fatPoint = new HashMap<>();
                fatPoint.put("date", dateStr);
                fatPoint.put("value", data.getBodyFatRate());
                bodyFatTrend.add(fatPoint);
            }
        }
        
        trends.put("hasData", true);
        trends.put("weightTrend", weightTrend);
        trends.put("bmiTrend", bmiTrend);
        trends.put("bodyFatTrend", bodyFatTrend);
        trends.put("latestData", physicalData.get(physicalData.size() - 1));
        
        return trends;
    }

    @Override
    public Map<String, Object> getUserCourseStats(Long userId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> stats = new HashMap<>();
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        Integer totalBookings = courseBookingMapper.countBookingsByUserId(userId, startTime, endTime);
        
        stats.put("totalBookings", totalBookings != null ? totalBookings : 0);
        stats.put("period", startDate + " 至 " + endDate);
        
        return stats;
    }

    @Override
    public Map<String, Object> getUserEquipmentStats(Long userId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> stats = new HashMap<>();
        
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(23, 59, 59);
        
        Integer totalDuration = equipmentUsageLogMapper.sumDurationByUserId(userId, startTime, endTime);
        Integer totalSessions = equipmentUsageLogMapper.countUsageByEquipmentId(userId, startTime, endTime);
        
        stats.put("totalDuration", totalDuration != null ? totalDuration : 0);
        stats.put("totalSessions", totalSessions != null ? totalSessions : 0);
        stats.put("averageDuration", totalSessions != null && totalSessions > 0 ? totalDuration / totalSessions : 0);
        stats.put("period", startDate + " 至 " + endDate);
        
        return stats;
    }

    @Override
    public List<UserPhysicalData> getUserPhysicalHistory(Long userId) {
        return userPhysicalDataMapper.selectByUserId(userId);
    }

    @Override
    public boolean addUserPhysicalData(UserPhysicalData physicalData) {
        // 计算BMI
        if (physicalData.getHeight() != null && physicalData.getWeight() != null) {
            BigDecimal bmi = physicalData.calculateBMI();
            physicalData.setBmi(bmi);
        }
        
        return userPhysicalDataMapper.insert(physicalData) > 0;
    }

    @Override
    public UserPhysicalData getLatestPhysicalData(Long userId) {
        return userPhysicalDataMapper.selectLatestByUserId(userId);
    }

    @Override
    public Map<String, Object> getFitnessGoalProgress(Long userId) {
        Map<String, Object> progress = new HashMap<>();
        
        // 获取用户最近30天的运动数据
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(30);
        
        Map<String, Object> workoutStats = getUserWorkoutStats(userId, startDate, endDate);
        
        // 设定目标（这里简化处理，实际应该从用户设置中获取）
        int weeklyGoal = 3; // 每周3次运动
        int durationGoal = 150; // 每周150分钟
        
        Integer totalSessions = (Integer) workoutStats.get("totalWorkoutSessions");
        Integer totalDuration = (Integer) workoutStats.get("totalWorkoutDuration");
        
        // 计算周平均
        double weeklyAverageSessions = totalSessions / 4.0; // 30天约4周
        double weeklyAverageDuration = totalDuration / 4.0;
        
        progress.put("weeklySessionsGoal", weeklyGoal);
        progress.put("weeklySessionsActual", Math.round(weeklyAverageSessions * 10.0) / 10.0);
        progress.put("sessionsProgress", Math.min(100, (int) (weeklyAverageSessions / weeklyGoal * 100)));
        
        progress.put("weeklyDurationGoal", durationGoal);
        progress.put("weeklyDurationActual", Math.round(weeklyAverageDuration));
        progress.put("durationProgress", Math.min(100, (int) (weeklyAverageDuration / durationGoal * 100)));
        
        return progress;
    }

    @Override
    public Map<String, Object> getWorkoutIntensityAnalysis(Long userId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 这里简化处理，实际应该根据心率、消耗等数据分析强度
        Map<String, Object> workoutStats = getUserWorkoutStats(userId, startDate, endDate);
        Integer totalSessions = (Integer) workoutStats.get("totalWorkoutSessions");
        Integer averageDuration = (Integer) workoutStats.get("averageSessionDuration");
        
        String intensity;
        if (averageDuration < 30) {
            intensity = "轻度";
        } else if (averageDuration < 60) {
            intensity = "中等";
        } else {
            intensity = "高强度";
        }
        
        analysis.put("overallIntensity", intensity);
        analysis.put("totalSessions", totalSessions);
        analysis.put("averageDuration", averageDuration);
        analysis.put("recommendation", getIntensityRecommendation(intensity, totalSessions));
        
        return analysis;
    }

    @Override
    public Map<String, Object> getFitnessReport(Long userId, String reportType) {
        Map<String, Object> report = new HashMap<>();
        
        LocalDate endDate = LocalDate.now();
        LocalDate startDate;
        
        switch (reportType) {
            case "weekly":
                startDate = endDate.minusDays(7);
                break;
            case "monthly":
                startDate = endDate.minusDays(30);
                break;
            case "yearly":
                startDate = endDate.minusDays(365);
                break;
            default:
                startDate = endDate.minusDays(30);
        }
        
        report.put("reportType", reportType);
        report.put("period", startDate + " 至 " + endDate);
        report.put("workoutStats", getUserWorkoutStats(userId, startDate, endDate));
        report.put("physicalTrends", getUserPhysicalTrends(userId, startDate, endDate));
        report.put("goalProgress", getFitnessGoalProgress(userId));
        report.put("intensityAnalysis", getWorkoutIntensityAnalysis(userId, startDate, endDate));
        report.put("generatedAt", LocalDateTime.now());
        
        return report;
    }

    /**
     * 获取运动强度建议
     */
    private String getIntensityRecommendation(String intensity, Integer sessions) {
        if (sessions < 2) {
            return "建议增加运动频率，每周至少进行3次运动";
        }
        
        switch (intensity) {
            case "轻度":
                return "可以适当增加运动强度和时长，建议每次运动30-60分钟";
            case "中等":
                return "运动强度适中，建议保持当前强度并适当增加频率";
            case "高强度":
                return "运动强度较高，注意适当休息，避免过度训练";
            default:
                return "建议制定合理的运动计划，循序渐进";
        }
    }
}