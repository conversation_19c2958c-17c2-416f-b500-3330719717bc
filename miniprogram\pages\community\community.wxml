<!-- 社区页面 -->
<view class="container">
  <!-- 页面标题和发布按钮 -->
  <view class="page-header">
    <text class="page-title">健身社区</text>
    <view class="header-actions">
      <view class="action-btn" bindtap="onPublishPost">
        <text class="btn-icon">➕</text>
      </view>
    </view>
  </view>

  <!-- 功能导航 -->
  <view class="feature-nav">
    <view 
      class="nav-item {{activeTab === item.key ? 'active' : ''}}"
      wx:for="{{navTabs}}"
      wx:key="key"
      data-tab="{{item.key}}"
      bindtap="onTabChange"
    >
      <view class="nav-icon">{{item.icon}}</view>
      <text class="nav-text">{{item.label}}</text>
    </view>
  </view>

  <!-- 快速打卡区域 -->
  <view class="quick-checkin" wx:if="{{activeTab === 'all' || activeTab === 'checkin'}}">
    <view class="section-header">
      <text class="section-title">今日打卡</text>
      <text class="checkin-streak">连续{{userStats.checkinStreak}}天</text>
    </view>
    
    <view class="checkin-options">
      <view 
        class="checkin-btn {{item.completed ? 'completed' : ''}}"
        wx:for="{{checkinTypes}}"
        wx:key="type"
        data-type="{{item.type}}"
        bindtap="onQuickCheckin"
      >
        <view class="checkin-icon">{{item.icon}}</view>
        <text class="checkin-text">{{item.name}}</text>
        <view class="checkin-mark" wx:if="{{item.completed}}">✓</view>
      </view>
    </view>
  </view>

  <!-- 社区动态列表 -->
  <view class="community-posts">
    <view class="posts-header" wx:if="{{activeTab === 'all'}}">
      <text class="posts-title">社区动态</text>
      <view class="filter-dropdown" bindtap="onShowFilterMenu">
        <text class="filter-text">{{currentFilter.label}}</text>
        <text class="filter-arrow">〉</text>
      </view>
    </view>
    
    <view class="posts-list">
      <view 
        class="post-item"
        wx:for="{{filteredPosts}}"
        wx:key="id"
        data-post="{{item}}"
        bindtap="onPostDetail"
      >
        <!-- 用户信息 -->
        <view class="post-header">
          <view class="user-info">
            <image class="user-avatar" src="{{item.user.avatar}}" mode="aspectFill"></image>
            <view class="user-details">
              <text class="user-name">{{item.user.name}}</text>
              <text class="post-time">{{item.timeAgo}}</text>
            </view>
          </view>
          <view class="post-type-badge {{item.type}}">
            <text class="badge-text">{{item.typeLabel}}</text>
          </view>
        </view>
        
        <!-- 内容区域 -->
        <view class="post-content">
          <text class="post-text" wx:if="{{item.content}}">{{item.content}}</text>
          
          <!-- 图片内容 -->
          <view class="post-images" wx:if="{{item.images && item.images.length > 0}}">
            <image 
              class="post-image {{item.images.length === 1 ? 'single' : 'multiple'}}"
              wx:for="{{item.images}}"
              wx:for-item="image"
              wx:for-index="imageIndex"
              wx:key="imageIndex"
              src="{{image}}"
              mode="aspectFill"
              data-images="{{item.images}}"
              data-current="{{imageIndex}}"
              bindtap="onPreviewImage"
            ></image>
          </view>
          
          <!-- 运动数据 -->
          <view class="workout-data" wx:if="{{item.workoutData}}">
            <view class="data-item">
              <text class="data-label">运动类型</text>
              <text class="data-value">{{item.workoutData.type}}</text>
            </view>
            <view class="data-item">
              <text class="data-label">时长</text>
              <text class="data-value">{{item.workoutData.duration}}</text>
            </view>
            <view class="data-item">
              <text class="data-label">卡路里</text>
              <text class="data-value">{{item.workoutData.calories}}</text>
            </view>
          </view>
          
          <!-- 饮食信息 -->
          <view class="food-data" wx:if="{{item.foodData}}">
            <view class="food-item">
              <text class="food-name">{{item.foodData.name}}</text>
              <text class="food-calories">{{item.foodData.calories}}卡路里</text>
            </view>
          </view>
          
          <!-- 位置信息 -->
          <view class="location-info" wx:if="{{item.location}}">
            <text class="location-icon">📍</text>
            <text class="location-text">{{item.location}}</text>
          </view>
        </view>
        
        <!-- 互动区域 -->
        <view class="post-actions">
          <view 
            class="action-btn like {{item.isLiked ? 'active' : ''}}"
            data-post-id="{{item.id}}"
            bindtap="onToggleLike"
          >
            <text class="action-icon">{{item.isLiked ? '❤️' : '🤍'}}</text>
            <text class="action-count">{{item.likeCount}}</text>
          </view>
          
          <view 
            class="action-btn comment"
            data-post-id="{{item.id}}"
            bindtap="onShowComments"
          >
            <text class="action-icon">💬</text>
            <text class="action-count">{{item.commentCount}}</text>
          </view>
          
          <view 
            class="action-btn share"
            data-post="{{item}}"
            bindtap="onSharePost"
          >
            <text class="action-icon">📤</text>
            <text class="action-text">分享</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMorePosts}}" bindtap="onLoadMorePosts">
      <text class="load-more-text">加载更多</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredPosts.length === 0}}">
      <text class="empty-icon">🏃‍♂️</text>
      <text class="empty-title">暂无动态</text>
      <text class="empty-desc">快来发布你的健身动态吧！</text>
    </view>
  </view>

  <!-- 筛选菜单 -->
  <view class="filter-menu {{showFilterMenu ? 'show' : ''}}" catchtap="onHideFilterMenu">
    <view class="filter-content" catchtap="">
      <view class="filter-header">
        <text class="filter-title">筛选动态</text>
        <view class="close-btn" bindtap="onHideFilterMenu">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="filter-options">
        <view 
          class="filter-option {{currentFilter.value === item.value ? 'active' : ''}}"
          wx:for="{{filterOptions}}"
          wx:key="value"
          data-filter="{{item}}"
          bindtap="onSelectFilter"
        >
          <text class="option-text">{{item.label}}</text>
          <text class="option-check" wx:if="{{currentFilter.value === item.value}}">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 发布动态弹窗 -->
  <view class="publish-modal {{showPublishModal ? 'show' : ''}}" catchtap="onClosePublishModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">发布动态</text>
        <view class="close-btn" bindtap="onClosePublishModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-body">
        <!-- 动态类型选择 -->
        <view class="post-type-selector">
          <view 
            class="type-option {{newPost.type === item.value ? 'active' : ''}}"
            wx:for="{{postTypes}}"
            wx:key="value"
            data-type="{{item.value}}"
            bindtap="onSelectPostType"
          >
            <view class="type-icon">{{item.icon}}</view>
            <text class="type-text">{{item.label}}</text>
          </view>
        </view>
        
        <!-- 文本内容 -->
        <view class="content-input">
          <textarea 
            class="content-textarea"
            placeholder="分享你的健身心得..."
            value="{{newPost.content}}"
            bindinput="onContentInput"
            maxlength="500"
          ></textarea>
          <text class="char-count">{{newPost.content.length}}/500</text>
        </view>
        
        <!-- 图片上传 -->
        <view class="image-upload" wx:if="{{newPost.type === 'moment' || newPost.type === 'food'}}">
          <view class="uploaded-images">
            <view 
              class="image-item"
              wx:for="{{newPost.images}}"
              wx:key="*this"
              wx:for-index="imageIndex"
            >
              <image class="upload-image" src="{{item}}" mode="aspectFill"></image>
              <view class="remove-image" data-index="{{imageIndex}}" bindtap="onRemoveImage">
                <text class="remove-icon">×</text>
              </view>
            </view>
            <view class="add-image" wx:if="{{newPost.images.length < 9}}" bindtap="onChooseImage">
              <text class="add-icon">📷</text>
              <text class="add-text">添加图片</text>
            </view>
          </view>
        </view>
        
        <!-- 运动数据输入 -->
        <view class="workout-input" wx:if="{{newPost.type === 'workout'}}">
          <view class="input-row">
            <text class="input-label">运动类型</text>
            <picker 
              mode="selector" 
              range="{{workoutTypes}}" 
              range-key="label"
              value="{{newPost.workoutTypeIndex}}"
              bindchange="onWorkoutTypeChange"
            >
              <view class="picker-display">
                <text class="picker-text">{{workoutTypes[newPost.workoutTypeIndex].label}}</text>
                <text class="picker-arrow">〉</text>
              </view>
            </picker>
          </view>
          
          <view class="input-row">
            <text class="input-label">时长（分钟）</text>
            <input 
              class="number-input"
              type="number"
              placeholder="30"
              value="{{newPost.workoutData.duration}}"
              bindinput="onWorkoutDurationInput"
            />
          </view>
          
          <view class="input-row">
            <text class="input-label">消耗卡路里</text>
            <input 
              class="number-input"
              type="number"
              placeholder="300"
              value="{{newPost.workoutData.calories}}"
              bindinput="onWorkoutCaloriesInput"
            />
          </view>
        </view>
        
        <!-- 位置选择 -->
        <view class="location-selector">
          <view class="location-option" bindtap="onChooseLocation">
            <text class="location-icon">📍</text>
            <text class="location-text">{{newPost.location || '添加位置'}}</text>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onClosePublishModal">取消</button>
        <button class="modal-btn primary" bindtap="onConfirmPublish">发布</button>
      </view>
    </view>
  </view>

  <!-- 评论弹窗 -->
  <view class="comments-modal {{showCommentsModal ? 'show' : ''}}" catchtap="onCloseCommentsModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">评论</text>
        <view class="close-btn" bindtap="onCloseCommentsModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="comments-list">
        <view 
          class="comment-item"
          wx:for="{{currentPostComments}}"
          wx:key="id"
        >
          <image class="comment-avatar" src="{{item.user.avatar}}" mode="aspectFill"></image>
          <view class="comment-content">
            <text class="comment-user">{{item.user.name}}</text>
            <text class="comment-text">{{item.content}}</text>
            <text class="comment-time">{{item.timeAgo}}</text>
          </view>
        </view>
        
        <view class="no-comments" wx:if="{{currentPostComments.length === 0}}">
          <text class="no-comments-text">暂无评论，快来说点什么吧~</text>
        </view>
      </view>
      
      <view class="comment-input-area">
        <input 
          class="comment-input"
          placeholder="写下你的评论..."
          value="{{newComment}}"
          bindinput="onCommentInput"
        />
        <button class="send-comment-btn" bindtap="onSendComment">发送</button>
      </view>
    </view>
  </view>
</view> 