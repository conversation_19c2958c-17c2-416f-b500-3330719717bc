package com.gym.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程信息实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@TableName("course_info")
public class CourseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("course_name")
    private String courseName;

    @TableField("course_type")
    private Integer courseType;

    @TableField("course_category")
    private String courseCategory;

    @TableField("instructor_id")
    private Long instructorId;

    @TableField("duration")
    private Integer duration;

    @TableField("max_capacity")
    private Integer maxCapacity;

    @TableField("difficulty_level")
    private Integer difficultyLevel;

    @TableField("course_fee")
    private BigDecimal courseFee;

    @TableField("member_discount")
    private BigDecimal memberDiscount;

    @TableField("description")
    private String description;

    @TableField("course_image")
    private String courseImage;

    @TableField("equipment_needed")
    private String equipmentNeeded;

    @TableField("calories_burn")
    private Integer caloriesBurn;

    @TableField("status")
    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取课程类型名称
     */
    public String getCourseTypeName() {
        if (courseType == null) {
            return "其他";
        }
        switch (courseType) {
            case 1: return "瑜伽";
            case 2: return "搏击";
            case 3: return "动感单车";
            case 4: return "私教";
            case 5: return "其他";
            default: return "其他";
        }
    }

    /**
     * 获取难度等级名称
     */
    public String getDifficultyLevelName() {
        if (difficultyLevel == null) {
            return "初级";
        }
        switch (difficultyLevel) {
            case 1: return "初级";
            case 2: return "中级";
            case 3: return "高级";
            default: return "初级";
        }
    }

    /**
     * 判断是否为私教课程
     */
    public boolean isPersonalTraining() {
        return courseType != null && courseType == 4;
    }
} 