<!-- 个人资料页面 -->
<view class="container">
  <!-- 用户头部信息 -->
  <view class="user-header">
    <view class="header-bg"></view>
    <view class="user-info">
      <view class="avatar-section" bindtap="onChangeAvatar">
        <image class="user-avatar" src="{{userInfo.avatar}}" mode="aspectFill"></image>
        <view class="avatar-edit">
          <text class="edit-icon">📷</text>
        </view>
      </view>
      
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname}}</text>
        <text class="user-level">{{userInfo.memberLevel}} 会员</text>
        <text class="user-id">ID: {{userInfo.id}}</text>
      </view>
      
      <view class="settings-btn" bindtap="onSettings">
        <text class="settings-icon">⚙️</text>
      </view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-section">
    <view class="section-header">
      <text class="section-title">我的数据</text>
      <view class="view-detail" bindtap="onViewAllStats">
        <text class="detail-text">查看详细</text>
        <text class="arrow">〉</text>
      </view>
    </view>
    
    <view class="stats-grid">
      <view class="stat-card" wx:for="{{userStats}}" wx:key="key">
        <view class="stat-icon">{{item.icon}}</view>
        <text class="stat-value">{{item.value}}</text>
        <text class="stat-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" wx:for="{{menuItems}}" wx:key="key" data-item="{{item}}" bindtap="onMenuItemTap">
        <view class="menu-icon">{{item.icon}}</view>
        <text class="menu-text">{{item.title}}</text>
        <view class="menu-extra">
          <text class="extra-text" wx:if="{{item.extra}}">{{item.extra}}</text>
          <text class="menu-arrow">〉</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置弹窗 -->
  <view class="settings-modal {{showSettingsModal ? 'show' : ''}}" catchtap="onCloseSettingsModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">设置</text>
        <view class="close-btn" bindtap="onCloseSettingsModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="settings-list">
        <view class="setting-item" wx:for="{{settingsItems}}" wx:key="key">
          <view class="setting-icon">{{item.icon}}</view>
          <text class="setting-text">{{item.title}}</text>
          <view class="setting-control">
            <switch 
              wx:if="{{item.type === 'switch'}}" 
              checked="{{item.value}}" 
              bindchange="onSettingToggle"
              data-key="{{item.key}}"
            />
            <text wx:else class="setting-arrow">〉</text>
          </view>
        </view>
      </view>
      
      <view class="logout-section">
        <button class="logout-btn" bindtap="onLogout">退出登录</button>
      </view>
    </view>
  </view>
</view> 