const { createStore } = require('../../utils/store');
const store = createStore();

Page({
  data: {
    // 最新记录
    latestRecord: {
      date: '2024-01-15',
      weight: 65.2,
      bmi: 22.1,
      bodyFat: 18.5,
      muscle: 38.2,
      weightChange: -0.3,
      bodyFatChange: -0.5,
      muscleChange: +0.8,
      bmiStatus: 'normal',
      bmiStatusText: '正常'
    },
    
    // 图表相关
    chartTimeRange: 'month',
    selectedChartType: 'weight',
    showTooltip: false,
    tooltipX: 0,
    tooltipY: 0,
    tooltipData: { date: '', value: '' },
    chartSummary: {
      max: '66.1kg', min: '64.8kg', avg: '65.4kg', change: -1.2
    },
    
    // 目标设置
    userGoals: {
      targetWeight: 63.0,
      targetBodyFat: 16.0,
      targetMuscle: 40.0,
      targetDate: '2024-06-01'
    },
    weightProgress: 73,
    bodyFatProgress: 65,
    muscleProgress: 42,
    
    // 历史记录
    recentHistory: [
      {
        id: 1, date: '2024-01-15', day: '15', month: '01月',
        weight: 65.2, bodyFat: 18.5, muscle: 38.2,
        weightChange: -0.3, bodyFatChange: -0.5, muscleChange: +0.8
      }
    ],
    
    // 模态框
    showAddModal: false,
    showGoalsModal: false,
    
    // 新增记录
    newRecord: {
      date: '', weight: '', height: '', bodyFat: '', muscle: '', visceralFat: '', note: ''
    },
    calculatedBMI: '',
    bmiStatusClass: '',
    bmiStatusText: '',
    canSave: false,
    
    // 临时目标
    tempGoals: {
      targetWeight: '', targetBodyFat: '', targetMuscle: '', targetDate: ''
    }
  },

  chartCtx: null,

  onLoad(options) {
    console.log('Body data page loaded');
    this.initPage();
  },

  onShow() {
    this.refreshCharts();
  },

  initPage() {
    const today = new Date();
    const dateStr = this.formatDate(today);
    this.setData({ 'newRecord.date': dateStr });
    
    this.loadUserData();
    this.initChart();
    this.calculateProgress();
  },

  loadUserData() {
    try {
      const bodyData = wx.getStorageSync('bodyData');
      if (bodyData) {
        this.setData({
          recentHistory: bodyData.history || this.data.recentHistory,
          latestRecord: bodyData.latest || this.data.latestRecord
        });
      }
      
      const userGoals = wx.getStorageSync('userGoals');
      if (userGoals) {
        this.setData({ userGoals });
        this.calculateProgress();
      }
    } catch (error) {
      console.error('Failed to load user data:', error);
    }
  },

  // 图表相关
  initChart() {
    this.chartCtx = wx.createCanvasContext('trendChart', this);
    this.drawChart();
  },

  onChartFilterChange(e) {
    const { range } = e.currentTarget.dataset;
    this.setData({ chartTimeRange: range });
    this.updateChartSummary();
    this.drawChart();
  },

  onChartTypeChange(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({ selectedChartType: type });
    this.updateChartSummary();
    this.drawChart();
  },

  drawChart() {
    const ctx = this.chartCtx;
    ctx.clearRect(0, 0, 300, 200);
    ctx.setFillStyle('#9ca3af');
    ctx.setFontSize(14);
    ctx.setTextAlign('center');
    ctx.fillText('图表数据', 150, 100);
    ctx.draw();
  },

  updateChartSummary() {
    // 简化图表摘要更新
    this.setData({
      chartSummary: {
        max: '66.1kg', min: '64.8kg', avg: '65.4kg', change: -1.2
      }
    });
  },

  onChartTouch(e) {
    const touch = e.touches[0];
    this.setData({
      showTooltip: true,
      tooltipX: touch.x,
      tooltipY: touch.y - 60,
      tooltipData: {
        date: '2024-01-15',
        value: '65.2kg'
      }
    });
  },

  onChartTouchEnd() {
    this.setData({ showTooltip: false });
  },

  refreshCharts() {
    setTimeout(() => {
      this.updateChartSummary();
      this.drawChart();
    }, 100);
  },

  // 目标管理
  calculateProgress() {
    const { latestRecord, userGoals } = this.data;
    
    const weightProgress = this.calculateGoalProgress(
      latestRecord.weight, userGoals.targetWeight, 70
    );
    const bodyFatProgress = this.calculateGoalProgress(
      latestRecord.bodyFat, userGoals.targetBodyFat, 22
    );
    const muscleProgress = this.calculateGoalProgress(
      latestRecord.muscle, userGoals.targetMuscle, 35
    );
    
    this.setData({
      weightProgress: Math.max(0, Math.min(100, weightProgress)),
      bodyFatProgress: Math.max(0, Math.min(100, bodyFatProgress)),
      muscleProgress: Math.max(0, Math.min(100, muscleProgress))
    });
  },

  calculateGoalProgress(current, target, start) {
    const totalChange = target - start;
    const currentChange = current - start;
    return totalChange !== 0 ? Math.round((currentChange / totalChange) * 100) : 0;
  },

  onEditGoals() {
    this.setData({
      tempGoals: { ...this.data.userGoals },
      showGoalsModal: true
    });
  },

  // 添加数据
  onAddData() {
    const today = new Date();
    this.setData({
      newRecord: {
        date: this.formatDate(today),
        weight: '', height: '', bodyFat: '', muscle: '', visceralFat: '', note: ''
      },
      calculatedBMI: '',
      bmiStatusClass: '',
      bmiStatusText: '',
      canSave: false,
      showAddModal: true
    });
  },

  onCloseAddModal() {
    this.setData({ showAddModal: false });
  },

  onDateChange(e) {
    this.setData({ 'newRecord.date': e.detail.value });
  },

  onWeightInput(e) {
    this.setData({ 'newRecord.weight': e.detail.value });
    this.calculateBMI();
    this.validateRecord();
  },

  onHeightInput(e) {
    this.setData({ 'newRecord.height': e.detail.value });
    this.calculateBMI();
    this.validateRecord();
  },

  onBodyFatInput(e) {
    this.setData({ 'newRecord.bodyFat': e.detail.value });
    this.validateRecord();
  },

  onMuscleInput(e) {
    this.setData({ 'newRecord.muscle': e.detail.value });
    this.validateRecord();
  },

  onVisceralFatInput(e) {
    this.setData({ 'newRecord.visceralFat': e.detail.value });
    this.validateRecord();
  },

  onNoteInput(e) {
    this.setData({ 'newRecord.note': e.detail.value });
  },

  calculateBMI() {
    const { weight, height } = this.data.newRecord;
    
    if (weight && height) {
      const weightNum = parseFloat(weight);
      const heightNum = parseFloat(height) / 100;
      
      if (weightNum > 0 && heightNum > 0) {
        const bmi = weightNum / (heightNum * heightNum);
        const bmiValue = bmi.toFixed(1);
        
        let statusClass = 'normal';
        let statusText = '正常';
        
        if (bmi < 18.5) {
          statusClass = 'underweight';
          statusText = '偏瘦';
        } else if (bmi >= 25) {
          statusClass = 'overweight';
          statusText = '超重';
        }
        
        this.setData({
          calculatedBMI: bmiValue,
          bmiStatusClass: statusClass,
          bmiStatusText: statusText
        });
      }
    } else {
      this.setData({
        calculatedBMI: '',
        bmiStatusClass: '',
        bmiStatusText: ''
      });
    }
  },

  validateRecord() {
    const { weight } = this.data.newRecord;
    const canSave = weight && parseFloat(weight) > 0;
    this.setData({ canSave });
  },

  onSaveRecord() {
    if (!this.data.canSave) return;
    
    wx.showLoading({ title: '保存中...' });
    
    setTimeout(() => {
      wx.hideLoading();
      this.setData({ showAddModal: false });
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }, 1500);
  },

  // 目标设置
  onCloseGoalsModal() {
    this.setData({ showGoalsModal: false });
  },

  onTargetWeightInput(e) {
    this.setData({ 'tempGoals.targetWeight': e.detail.value });
  },

  onTargetBodyFatInput(e) {
    this.setData({ 'tempGoals.targetBodyFat': e.detail.value });
  },

  onTargetMuscleInput(e) {
    this.setData({ 'tempGoals.targetMuscle': e.detail.value });
  },

  onTargetDateChange(e) {
    this.setData({ 'tempGoals.targetDate': e.detail.value });
  },

  onSaveGoals() {
    const { tempGoals } = this.data;
    
    if (!tempGoals.targetWeight || !tempGoals.targetDate) {
      wx.showToast({
        title: '请填写必要信息',
        icon: 'none'
      });
      return;
    }
    
    const userGoals = {
      targetWeight: parseFloat(tempGoals.targetWeight),
      targetBodyFat: tempGoals.targetBodyFat ? parseFloat(tempGoals.targetBodyFat) : this.data.userGoals.targetBodyFat,
      targetMuscle: tempGoals.targetMuscle ? parseFloat(tempGoals.targetMuscle) : this.data.userGoals.targetMuscle,
      targetDate: tempGoals.targetDate
    };
    
    this.setData({
      userGoals: userGoals,
      showGoalsModal: false
    });
    
    try {
      wx.setStorageSync('userGoals', userGoals);
    } catch (error) {
      console.error('Failed to save goals:', error);
    }
    
    this.calculateProgress();
    
    wx.showToast({
      title: '目标已保存',
      icon: 'success'
    });
  },

  // 其他功能
  onShowHistory() {
    wx.navigateTo({
      url: '/pages/body-data-history/body-data-history'
    });
  },

  onViewAllHistory() {
    this.onShowHistory();
  },

  onHistoryItemTap(e) {
    const { record } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '身体数据详情',
      content: `日期：${record.date}\n体重：${record.weight}kg\n体脂率：${record.bodyFat}%\n肌肉量：${record.muscle}%`,
      showCancel: false
    });
  },

  onRefreshAnalysis() {
    wx.showToast({
      title: '分析已更新',
      icon: 'success'
    });
  },

  onBack() {
    wx.navigateBack();
  },

  // 工具函数
  formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  onPullDownRefresh() {
    this.loadUserData();
    this.refreshCharts();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  onShareAppMessage() {
    return {
      title: 'FitFocus身体数据追踪 - 科学管理健康',
      path: '/pages/body-data/body-data',
      imageUrl: '/assets/images/body-data-share.jpg'
    };
  }
}); 