server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: gym-backend
  
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 2000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 10000ms
    jedis:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.gym.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.gym.mapper: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# JWT配置
jwt:
  secret: gym-weixinxiaochengxu-secret-key-2024
  expiration: 604800 # 7天过期
  header: Authorization
  prefix: Bearer

# 微信小程序配置
wechat:
  miniapp:
    app-id: your-miniapp-appid
    app-secret: your-miniapp-appsecret

# 文件上传配置
file:
  upload:
    path: /opt/gym/uploads/
    max-size: 10MB

# 设备物联网配置
iot:
  mqtt:
    broker: tcp://localhost:1883
    client-id: gym-backend-${random.value}
    username: admin
    password: public 