package com.gym.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 微信工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Component
public class WechatUtil {

    @Value("${wechat.miniapp.app-id}")
    private String appId;

    @Value("${wechat.miniapp.app-secret}")
    private String appSecret;

    /**
     * 微信小程序登录凭证校验
     */
    public WechatLoginResult code2Session(String code) {
        String url = String.format(
                "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                appId, appSecret, code
        );

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.info("微信登录响应: {}", result);
                
                JSONObject jsonObject = JSON.parseObject(result);
                
                WechatLoginResult loginResult = new WechatLoginResult();
                loginResult.setOpenid(jsonObject.getString("openid"));
                loginResult.setSessionKey(jsonObject.getString("session_key"));
                loginResult.setUnionid(jsonObject.getString("unionid"));
                loginResult.setErrcode(jsonObject.getInteger("errcode"));
                loginResult.setErrmsg(jsonObject.getString("errmsg"));
                
                return loginResult;
            }
        } catch (IOException e) {
            log.error("微信登录请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("微信接口调用失败");
        }
    }

    /**
     * 获取小程序全局唯一后台接口调用凭据
     */
    public String getAccessToken() {
        String url = String.format(
                "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                appId, appSecret
        );

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                JSONObject jsonObject = JSON.parseObject(result);
                
                if (jsonObject.containsKey("access_token")) {
                    return jsonObject.getString("access_token");
                } else {
                    log.error("获取access_token失败: {}", result);
                    throw new RuntimeException("获取微信access_token失败");
                }
            }
        } catch (IOException e) {
            log.error("获取access_token请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("微信接口调用失败");
        }
    }

    /**
     * 微信登录结果
     */
    @Data
    public static class WechatLoginResult {
        private String openid;
        private String sessionKey;
        private String unionid;
        private Integer errcode;
        private String errmsg;

        public boolean isSuccess() {
            return errcode == null && openid != null;
        }
    }
} 